# H42N42 - Creet Simulation Game Makefile
# Professional build system for OCaml/Eliom project

# Colors for output
RED = \033[0;31m
GREEN = \033[0;32m
YELLOW = \033[0;33m
BLUE = \033[0;34m
PURPLE = \033[0;35m
CYAN = \033[0;36m
WHITE = \033[0;37m
BOLD = \033[1m
NC = \033[0m # No Color

# Project configuration
PROJECT_NAME = h42n42
BUILD_DIR = _build
STATIC_DIR = static
SRC_DIR = src
JS_OUTPUT = $(STATIC_DIR)/js/h42n42_client.js
SERVER_PORT = 8000

# Build tools
DUNE = dune
OPAM = opam
PYTHON = python3

#Rules
all: build standalone
	@echo "$(GREEN)$(BOLD)✅ H42N42 build completed successfully!$(NC)"
	@echo "$(CYAN)🌐 Run 'make run' to start the development server$(NC)"

build:
	@echo "$(BLUE)$(BOLD)🔨 Building H42N42 project...$(NC)"
	@if ! command -v $(DUNE) >/dev/null 2>&1; then \
		echo "$(RED)❌ Error: dune not found. Please install dune.$(NC)"; \
		exit 1; \
	fi
	@eval $$($(OPAM) env) && $(DUNE) build
	@echo "$(GREEN)✅ Build completed$(NC)"

run: all
	@echo "$(PURPLE)$(BOLD)🚀 Starting H42N42 development server...$(NC)"
	@echo "$(CYAN)📍 Server will be available at: http://localhost:$(SERVER_PORT)$(NC)"
	@echo "$(CYAN)🏠 Welcome page: http://localhost:$(SERVER_PORT)/welcome.html$(NC)"
	@echo "$(CYAN)🎮 Game page: http://localhost:$(SERVER_PORT)/game.html$(NC)"
	@echo "$(YELLOW)💡 Press Ctrl+C to stop the server$(NC)"
	@echo ""
	@cd $(STATIC_DIR) && $(PYTHON) -m http.server $(SERVER_PORT)

shutdown:
	@echo "$(RED)$(BOLD)🛑 Shutting down H42N42 development server...$(NC)"
	@if lsof -i :$(SERVER_PORT) >/dev/null 2>&1; then \
		PID=$$(lsof -t -i :$(SERVER_PORT)); \
		echo "$(YELLOW)🔍 Found server running on port $(SERVER_PORT) with PID $$PID$(NC)"; \
		kill -9 $$PID; \
		echo "$(GREEN)✅ Server on port $(SERVER_PORT) has been stopped$(NC)"; \
	else \
		echo "$(BLUE)ℹ️  No server running on port $(SERVER_PORT), nothing to stop$(NC)"; \
	fi

clean:
	@echo "$(YELLOW)$(BOLD)🧹 Cleaning H42N42 build artifacts...$(NC)"
	@if [ -d "$(BUILD_DIR)" ]; then \
		echo "$(CYAN)📂 Contents of $(BUILD_DIR):$(NC)"; \
		tree -l $(BUILD_DIR); \
		rm -rf $(BUILD_DIR); \
		echo "$(GREEN)✅ Removed build directory: $(BUILD_DIR)$(NC)"; \
	else \
		echo "$(BLUE)ℹ️  Build directory $(BUILD_DIR) not present, skipping clean$(NC)"; \
	fi
	@echo "$(CYAN)🧹 Cleaning standalone JavaScript files...$(NC)"
	@if [ -d "$(STATIC_DIR)/js" ]; then \
		echo "$(CYAN)📂 Contents of $(STATIC_DIR)/js:$(NC)"; \
		tree -l $(STATIC_DIR)/js; \
		rm -rf $(STATIC_DIR)/js; \
		echo "$(GREEN)✅ Removed JavaScript files in $(STATIC_DIR)/js$(NC)"; \
	else \
		echo "$(BLUE)ℹ️  JavaScript directory $(STATIC_DIR)/js not present, skipping clean$(NC)"; \
	fi
	@echo "$(GREEN)$(BOLD)✅ All JavaScript files and build artifacts cleaned$(NC)"

re: clean all
	@echo "$(GREEN)$(BOLD)🔄 H42N42 rebuild completed!$(NC)"

# Standalone component builds
standalone: standalone-all
	@echo "$(GREEN)$(BOLD)🎉 All standalone components built!$(NC)"

standalone-all:
	@mkdir -p $(STATIC_DIR)/js
	@echo "$(PURPLE)$(BOLD)🚀 Building all standalone components...$(NC)"
	@chmod +x scripts/build_all_standalone.sh
	@./scripts/build_all_standalone.sh

standalone-theme:
	@echo "$(PURPLE)$(BOLD)🎨 Building standalone theme system...$(NC)"
	@chmod +x scripts/build_theme.sh
	@./scripts/build_theme.sh

standalone-settings:
	@echo "$(PURPLE)$(BOLD)⚙️ Building standalone settings system...$(NC)"
	@chmod +x scripts/build_settings.sh
	@./scripts/build_settings.sh

standalone-game:
	@echo "$(PURPLE)$(BOLD)🎮 Building standalone game system...$(NC)"
	@chmod +x scripts/build_game.sh
	@./scripts/build_game.sh

help:
	@echo "$(PURPLE)$(BOLD)🎮 H42N42 - Creet Simulation Game$(NC)"
	@echo "$(CYAN)━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━$(NC)"
	@echo ""
	@echo "$(YELLOW)$(BOLD)📋 Available Commands:$(NC)"
	@echo "  $(GREEN)make all$(NC)                - Build the entire project"
	@echo "  $(GREEN)make build$(NC)              - Compile OCaml/Eliom code"
	@echo "  $(GREEN)make run$(NC)                - Start development server"
	@echo "  $(GREEN)make shutdown$(NC)           - Stop development server"
	@echo "  $(GREEN)make clean$(NC)              - Remove build artifacts"
	@echo "  $(GREEN)make re$(NC)                 - Clean and rebuild"
	@echo "  $(GREEN)make standalone$(NC)         - Build all standalone components"
	@echo "  $(GREEN)make standalone-theme$(NC)   - Build theme system only"
	@echo "  $(GREEN)make standalone-settings$(NC) - Build settings system only"
	@echo "  $(GREEN)make standalone-game$(NC)    - Build game system only"
	@echo "  $(GREEN)make help$(NC)               - Show this help message"
	@echo ""
	@echo "$(YELLOW)$(BOLD)🌐 URLs:$(NC)"
	@echo "  $(CYAN)Welcome:      http://localhost:$(SERVER_PORT)/welcome.html$(NC)"
	@echo "  $(CYAN)Game:         http://localhost:$(SERVER_PORT)/game.html$(NC)"
	@echo "  $(CYAN)Instructions: http://localhost:$(SERVER_PORT)/instructions.html$(NC)"
	@echo "  $(CYAN)Settings:     http://localhost:$(SERVER_PORT)/settings.html$(NC)"
	@echo "  $(CYAN)Theme:        http://localhost:$(SERVER_PORT)/theme.html$(NC)"
	@echo "  $(CYAN)Credits:      http://localhost:$(SERVER_PORT)/credits.html$(NC)"
	@echo ""
	@echo "$(YELLOW)$(BOLD)🛠️  Quick Start:$(NC)"
	@echo "  $(WHITE)1.$(NC) $(GREEN)make all$(NC)   - Build project"
	@echo "  $(WHITE)2.$(NC) $(GREEN)make run$(NC)   - Start server"

.DEFAULT_GOAL := help

.PHONY: help all build run shutdown clean re standalone standalone-all standalone-theme standalone-settings standalone-game