#!/bin/bash

echo "🎨 Building standalone OCaml theme system..."

cd src

# Clean up any previous builds
rm -f theme_standalone.bc theme_standalone.js theme_standalone.cmi theme_standalone.cmo

echo "📦 Compiling OCaml theme logic to bytecode..."
if ocamlfind ocamlc -package js_of_ocaml -package js_of_ocaml-ppx -linkpkg -o theme_standalone.bc theme_standalone.ml; then
    echo "✅ Theme bytecode compilation successful"
    
    echo "🔄 Converting theme logic to JavaScript..."
    if js_of_ocaml theme_standalone.bc; then
        echo "✅ Theme JavaScript conversion successful"
        
        # Move the generated JavaScript to the static directory
        echo "📁 Moving theme JavaScript file..."
        mv theme_standalone.js ../static/js/theme_standalone.js
        echo "✅ Theme JavaScript file moved to static/js/theme_standalone.js"
    else
        echo "❌ Theme JavaScript conversion failed"
        exit 1
    fi
else
    echo "❌ Theme bytecode compilation failed"
    exit 1
fi

# Clean up intermediate files
rm -f theme_standalone.bc theme_standalone.cmi theme_standalone.cmo

cd ..

echo "✅ Standalone theme system built successfully!"
echo "📄 JavaScript file: static/js/theme_standalone.js"
echo "🎨 Theme system ready for debugging"
