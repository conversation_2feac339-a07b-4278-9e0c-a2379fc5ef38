#!/bin/bash

echo "⚙️ Building standalone OCaml settings system..."

cd src

# Clean up any previous builds
rm -f settings_standalone.bc settings_standalone.js settings_standalone.cmi settings_standalone.cmo

echo "📦 Compiling OCaml settings logic to bytecode..."
if ocamlfind ocamlc -package js_of_ocaml -package js_of_ocaml-ppx -linkpkg -o settings_standalone.bc settings_standalone.ml; then
    echo "✅ Settings bytecode compilation successful"
    
    echo "🔄 Converting settings logic to JavaScript..."
    if js_of_ocaml settings_standalone.bc; then
        echo "✅ Settings JavaScript conversion successful"
        
        # Move the generated JavaScript to the static directory
        echo "📁 Moving settings JavaScript file..."
        mv settings_standalone.js ../static/js/settings_standalone.js
        echo "✅ Settings JavaScript file moved to static/js/settings_standalone.js"
    else
        echo "❌ Settings JavaScript conversion failed"
        exit 1
    fi
else
    echo "❌ Settings bytecode compilation failed"
    exit 1
fi

# Clean up intermediate files
rm -f settings_standalone.bc settings_standalone.cmi settings_standalone.cmo

cd ..

echo "✅ Standalone settings system built successfully!"
echo "📄 JavaScript file: static/js/settings_standalone.js"
echo "⚙️ Settings management ready for debugging"
