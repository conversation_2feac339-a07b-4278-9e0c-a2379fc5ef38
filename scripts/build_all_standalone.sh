#!/bin/bash

echo "🚀 Building all H42N42 standalone components..."
echo "================================================"

# Make sure scripts are executable
chmod +x scripts/build_theme.sh
chmod +x scripts/build_settings.sh
chmod +x scripts/build_game.sh

# Build theme system
echo "1️⃣ Building theme system..."
if ./scripts/build_theme.sh; then
    echo "✅ Theme system build completed"
else
    echo "❌ Theme system build failed"
    exit 1
fi

echo ""

# Build settings system
echo "2️⃣ Building settings system..."
if ./scripts/build_settings.sh; then
    echo "✅ Settings system build completed"
else
    echo "❌ Settings system build failed"
    exit 1
fi

echo ""

# Build game system
echo "3️⃣ Building game system..."
if ./scripts/build_game.sh; then
    echo "✅ Game system build completed"
else
    echo "❌ Game system build failed"
    exit 1
fi

echo ""
echo "🎉 All standalone components built successfully!"
echo "================================================"
echo "📁 Generated files:"
echo "   • static/js/theme_standalone.js"
echo "   • static/js/settings_standalone.js"
echo "   • static/js/game_standalone.js"
echo ""
echo "🎮 Ready for independent debugging and testing!"
