#!/bin/bash

echo "🎮 Building standalone OCaml game system..."

cd src

# Clean up any previous builds
rm -f game_standalone.bc game_standalone.js game_standalone.cmi game_standalone.cmo

echo "📦 Compiling OCaml game logic to bytecode..."
if ocamlfind ocamlc -package js_of_ocaml -package js_of_ocaml-lwt -package lwt -package js_of_ocaml-ppx -package lwt_ppx -linkpkg -o game_standalone.bc game_standalone.ml; then
    echo "✅ Game bytecode compilation successful"
    
    echo "🔄 Converting game logic to JavaScript..."
    if js_of_ocaml game_standalone.bc; then
        echo "✅ Game JavaScript conversion successful"
        
        # Move the generated JavaScript to the static directory
        echo "📁 Moving game JavaScript file..."
        mv game_standalone.js ../static/js/game_standalone.js
        echo "✅ Game JavaScript file moved to static/js/game_standalone.js"
    else
        echo "❌ Game JavaScript conversion failed"
        exit 1
    fi
else
    echo "❌ Game bytecode compilation failed"
    exit 1
fi

# Clean up intermediate files
rm -f game_standalone.bc game_standalone.cmi game_standalone.cmo

cd ..

echo "✅ Standalone game system built successfully!"
echo "📄 JavaScript file: static/js/game_standalone.js"
echo "🎮 Game logic ready for debugging"
