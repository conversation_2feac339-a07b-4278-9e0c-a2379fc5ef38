/* Game Page Specific Styles */

/* Game Container */
#game-container {
    width: 800px;
    height: 700px;
    margin: 2rem auto;
    border: 3px solid #333;
    background-color: #D4F4D4;
    background: linear-gradient(135deg, #E8F5E8, #D4F4D4);
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

#toxic-river {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background: linear-gradient(45deg, #8B0000, #FF0000);
    color: white;
    text-align: center;
    line-height: 60px;
    font-weight: bold;
    z-index: 1;
    box-shadow: 0 5px 15px rgba(139, 0, 0, 0.7), 0 0 20px rgba(255, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    animation: toxicGlow 2s ease-in-out infinite;
}

#toxic-river::before {
    content: "☣️";
    font-size: 1.5em;
    animation: toxicPulse 2s ease-in-out infinite;
    filter: drop-shadow(0 0 8px rgba(255, 255, 0, 0.8));
}

#toxic-river::after {
    content: "☣️";
    font-size: 1.5em;
    animation: toxicPulse 2s ease-in-out infinite reverse;
    filter: drop-shadow(0 0 8px rgba(255, 255, 0, 0.8));
}

@keyframes toxicPulse {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.8;
        filter: drop-shadow(0 0 8px rgba(255, 255, 0, 0.6));
    }
    50% {
        transform: scale(1.2) rotate(180deg);
        opacity: 1;
        filter: drop-shadow(0 0 15px rgba(255, 255, 0, 1));
    }
}

@keyframes toxicGlow {
    0%, 100% {
        box-shadow: 0 2px 10px rgba(139, 0, 0, 0.5);
    }
    50% {
        box-shadow: 0 5px 20px rgba(255, 0, 0, 0.8), 0 0 30px rgba(255, 255, 0, 0.3);
    }
}

#game-area {
    position: relative;
    top: 60px;
    left: 0;
    width: 100%;
    height: 580px;
    background-color: #D4F4D4;
    background: linear-gradient(135deg, #E8F5E8, #D4F4D4);
    z-index: 2;
}

#hospital {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 60px;
    background: linear-gradient(45deg, #0066CC, #00AAFF);
    color: white;
    text-align: center;
    line-height: 60px;
    font-weight: bold;
    z-index: 1;
    box-shadow: 0 -2px 10px rgba(0, 102, 204, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    animation: hospitalGlow 2s ease-in-out infinite;
}

#hospital::before {
    content: "🏥";
    font-size: 1.5em;
    animation: hospitalPulse 2s ease-in-out infinite;
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
}

#hospital::after {
    content: "🏥";
    font-size: 1.5em;
    animation: hospitalPulse 2s ease-in-out infinite reverse;
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
}

@keyframes hospitalPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
        filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.6));
    }
    50% {
        transform: scale(1.2);
        opacity: 1;
        filter: drop-shadow(0 0 15px rgba(255, 255, 255, 1));
    }
}

@keyframes hospitalGlow {
    0%, 100% {
        box-shadow: 0 -2px 10px rgba(0, 102, 204, 0.5);
    }
    50% {
        box-shadow: 0 -5px 20px rgba(0, 170, 255, 0.8), 0 0 30px rgba(255, 255, 255, 0.3);
    }
}

#game-info {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50px;
    background-color: #333;
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-sizing: border-box;
    z-index: 3;
}

#score {
    font-weight: bold;
    font-size: 16px;
}

#game-over {
    font-size: 24px;
    font-weight: bold;
    color: #FF0000;
}

/* Creet Styles */
.creet {
    position: absolute;
    border: 2px solid #333;
    transition: all 0.1s ease;
    user-select: none;
}

.creet.healthy {
    background-color: #4CAF50 !important;
    border-color: #2E7D32;
}

.creet.sick {
    background-color: #FF9800 !important;
    border-color: #F57C00;
}

.creet.berserk {
    background-color: #F44336 !important;
    border-color: #C62828;
    animation: pulse 0.5s infinite alternate;
}

.creet.mean {
    background-color: #9C27B0 !important;
    border-color: #6A1B9A;
    animation: shake 0.3s infinite;
}

.creet.grabbed {
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    z-index: 100 !important;
}

@keyframes pulse {
    from { transform: scale(1); }
    to { transform: scale(1.05); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-1px); }
    75% { transform: translateX(1px); }
}

/* Game Controls Info */
.game-controls {
    display: flex;
    justify-content: center; /* Center horizontally */
    align-items: center;   /* Center vertically*/
    padding: 1rem;
    margin: 1rem auto;
    max-width: 800px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    background: rgba(255, 255, 255, 0.9);
}

/* Responsive Design for Game */
@media (max-width: 1024px) {
    #game-container {
        width: 95vw;
        max-width: 800px;
        height: 75vh;
        margin: 1rem auto;
    }

    .game-controls {
        margin: 1rem;
        padding: 1rem;
        font-size: 0.9rem;
    }
}

@media (max-width: 768px) {
    #game-container {
        width: 98vw;
        height: 70vh;
        margin: 0.5rem auto;
        border-radius: 10px;
    }

    .game-controls {
        margin: 0.5rem;
        padding: 0.8rem;
    }

    .game-controls h3 {
        font-size: 1.1rem;
        margin-bottom: 0.3rem;
    }

    .game-controls p {
        font-size: 0.85rem;
    }

    #toxic-river, #hospital {
        font-size: 0.9rem;
    }

    #toxic-river::before, #toxic-river::after,
    #hospital::before, #hospital::after {
        font-size: 1em;
    }

    #game-info {
        padding: 0 15px;
    }

    #score {
        font-size: 14px;
    }

    #game-over {
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    #game-container {
        width: 100vw;
        height: 65vh;
        margin: 0;
        border-radius: 0;
        border-left: none;
        border-right: none;
    }

    .game-controls {
        margin: 0;
        padding: 0.5rem;
        border-radius: 0;
    }

    .game-controls h3 {
        font-size: 1rem;
    }

    .game-controls p {
        font-size: 0.8rem;
    }

    #toxic-river, #hospital {
        font-size: 0.8rem;
        height: 50px;
        line-height: 50px;
    }

    #toxic-river {
        height: 50px;
        line-height: 50px;
    }

    #hospital {
        bottom: 0;
        height: 50px;
        line-height: 50px;
    }

    #game-area {
        top: 50px;
        height: calc(100% - 100px);
        min-height: 500px;
    }

    #game-info {
        height: 40px;
        padding: 0 10px;
    }

    #score {
        font-size: 12px;
    }

    #game-over {
        font-size: 16px;
    }

    .creet {
        /* Make creets slightly larger on mobile for easier interaction */
        min-width: 25px !important;
        min-height: 25px !important;
    }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
    .creet {
        /* Larger touch targets on touch devices */
        min-width: 30px !important;
        min-height: 30px !important;
    }

    .creet.grabbed {
        transform: scale(1.3);
        z-index: 1000 !important;
    }
}

/* Game Controls */
.game-wrapper {
    max-width: 900px;
    margin: 2rem auto;
    padding: 1rem;
}

.game-controls {
    text-align: center;
    margin-bottom: 2rem;
}

.game-controls .btn {
    margin: 0 0.5rem;
    padding: 1rem 2rem;
    font-size: 1.2rem;
    border-radius: 25px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.btn-primary {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #45a049, #4CAF50);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.btn-secondary {
    background: linear-gradient(45deg, #2196F3, #1976D2);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(45deg, #1976D2, #2196F3);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
}

/* Game Over Screen */
#game-over {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    padding: 3rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 3px solid #FF5722;
    z-index: 1000;
}

#game-over h2 {
    color: #FF5722;
    margin-bottom: 1rem;
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

#game-over p {
    color: #666;
    margin-bottom: 2rem;
    font-size: 1.2rem;
}
