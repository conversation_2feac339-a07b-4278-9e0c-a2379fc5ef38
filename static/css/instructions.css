/* Instructions Page Specific Styles */

.instructions-content {
    line-height: 1.6;
}

.instructions-content section {
    background: rgba(255, 255, 255, 0.7);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #667eea;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.instructions-content h2 {
    color: #667eea;
    margin-top: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.instructions-content h2::before {
    content: "📖";
    font-size: 1.2em;
}

.instructions-content section:nth-child(1) h2::before {
    content: "🎯";
}

.instructions-content section:nth-child(2) h2::before {
    content: "🎮";
}

.instructions-content section:nth-child(3) h2::before {
    content: "🖱️";
}

.instructions-content section:nth-child(4) h2::before {
    content: "👾";
}

.instructions-content section:nth-child(5) h2::before {
    content: "⚙️";
}

.instructions-content ul {
    padding-left: 1.5rem;
}

.instructions-content li {
    margin-bottom: 0.8rem;
    position: relative;
}

.instructions-content li::marker {
    color: #667eea;
}

.instructions-content strong {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
}

/* Creet type color indicators */
.creet-type-healthy {
    color: #4CAF50 !important;
    font-weight: bold;
}

.creet-type-sick {
    color: #FF9800 !important;
    font-weight: bold;
}

.creet-type-berserk {
    color: #F44336 !important;
    font-weight: bold;
}

.creet-type-mean {
    color: #9C27B0 !important;
    font-weight: bold;
}

/* Interactive elements */
.instructions-content .highlight {
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    padding: 0.2rem 0.5rem;
    border-radius: 5px;
    border: 1px solid rgba(102, 126, 234, 0.3);
}

.instructions-content .tip {
    background: rgba(76, 175, 80, 0.1);
    border: 1px solid rgba(76, 175, 80, 0.3);
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    position: relative;
}

.instructions-content .tip::before {
    content: "💡";
    position: absolute;
    top: -10px;
    left: 10px;
    background: white;
    padding: 0 5px;
    font-size: 1.2em;
}

.instructions-content .warning {
    background: rgba(255, 152, 0, 0.1);
    border: 1px solid rgba(255, 152, 0, 0.3);
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    position: relative;
}

.instructions-content .warning::before {
    content: "⚠️";
    position: absolute;
    top: -10px;
    left: 10px;
    background: white;
    padding: 0 5px;
    font-size: 1.2em;
}
