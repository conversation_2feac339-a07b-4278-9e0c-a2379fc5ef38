/* Theme Page Specific Styles */

.theme-content {
    line-height: 1.6;
}

.theme-section {
    background: rgba(255, 255, 255, 0.8);
    padding: 2rem;
    margin-bottom: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #667eea;
}

.theme-section h2 {
    color: #333;
    margin-top: 0;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.theme-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.theme-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
}

.theme-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    border-color: #667eea;
}

.theme-card.active {
    border-color: #4CAF50;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(255, 255, 255, 0.9));
}

.theme-card h3 {
    margin: 1rem 0 0.5rem 0;
    color: #333;
    font-size: 1.2rem;
    text-align: center;
}

.theme-card p {
    color: #666;
    font-size: 0.9rem;
    text-align: center;
    margin-bottom: 1rem;
}

.theme-preview {
    width: 100%;
    height: 150px;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 1rem;
    position: relative;
    border: 2px solid #ddd;
}

.preview-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 0.5rem;
    text-align: center;
    font-weight: bold;
    font-size: 0.9rem;
}

.preview-nav {
    background: rgba(255, 255, 255, 0.9);
    padding: 0.3rem;
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.8rem;
}

.preview-content {
    height: 80px;
    position: relative;
    background: linear-gradient(135deg, #E8F5E8, #D4F4D4);
}

.preview-toxic {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(45deg, #8B0000, #FF0000);
    color: white;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-hospital {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(45deg, #0066CC, #00AAFF);
    color: white;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.preview-game {
    position: absolute;
    top: 20px;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 0.5rem;
}

.preview-creet {
    width: 12px;
    height: 12px;
    background: #4CAF50;
    border-radius: 50%;
    border: 1px solid #2E7D32;
    animation: previewFloat 2s ease-in-out infinite;
}

.preview-creet:nth-child(2) {
    animation-delay: 0.5s;
}

@keyframes previewFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-5px); }
}

/* Theme-specific previews */
.dark-theme .preview-header {
    background: linear-gradient(45deg, #2c3e50, #34495e);
}

.dark-theme .preview-nav {
    background: rgba(52, 73, 94, 0.9);
    color: white;
}

.dark-theme .preview-content {
    background: linear-gradient(135deg, #2c3e50, #34495e);
}

.nature-theme .preview-header {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
}

.nature-theme .preview-content {
    background: linear-gradient(135deg, #a8e6cf, #dcedc1);
}

.sunset-theme .preview-header {
    background: linear-gradient(45deg, #e74c3c, #f39c12);
}

.sunset-theme .preview-content {
    background: linear-gradient(135deg, #ffecd2, #fcb69f);
}

.ocean-theme .preview-header {
    background: linear-gradient(45deg, #3498db, #2980b9);
}

.ocean-theme .preview-content {
    background: linear-gradient(135deg, #a8edea, #fed6e3);
}

.neon-theme .preview-header {
    background: linear-gradient(45deg, #8e44ad, #e91e63);
}

.neon-theme .preview-content {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

/* Customization options */
.customization-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.option-group label {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.option-group input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: linear-gradient(to right, #667eea, #764ba2);
    outline: none;
    opacity: 0.7;
    transition: opacity 0.2s;
}

.option-group input[type="range"]:hover {
    opacity: 1;
}

.option-group input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #667eea;
}

.option-value {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 0.2rem 0.6rem;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.8rem;
    text-align: center;
    align-self: flex-start;
}

.option-description {
    font-size: 0.8rem;
    color: #666;
    font-style: italic;
}

.theme-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    padding: 2rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 15px;
    border: 2px dashed rgba(102, 126, 234, 0.3);
}

.theme-actions .btn {
    min-width: 150px;
    font-size: 0.9rem;
}

/* Global theme classes */
body.dark-theme {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: #ecf0f1;
}

body.dark-theme .nav-menu {
    background: rgba(44, 62, 80, 0.95);
}

body.dark-theme .page-container {
    background: rgba(44, 62, 80, 0.9);
    color: #ecf0f1;
}

body.nature-theme {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
}

body.sunset-theme {
    background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%);
}

body.ocean-theme {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
}

body.neon-theme {
    background: linear-gradient(135deg, #8e44ad 0%, #e91e63 100%);
}

/* Responsive design for theme page */
@media (max-width: 768px) {
    .theme-grid {
        grid-template-columns: 1fr;
    }
    
    .theme-card {
        padding: 1rem;
    }
    
    .customization-options {
        grid-template-columns: 1fr;
    }
    
    .theme-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .theme-actions .btn {
        width: 100%;
        max-width: 250px;
    }
}

@media (max-width: 480px) {
    .theme-section {
        padding: 1rem;
    }
    
    .theme-preview {
        height: 120px;
    }
    
    .preview-content {
        height: 60px;
    }
}
