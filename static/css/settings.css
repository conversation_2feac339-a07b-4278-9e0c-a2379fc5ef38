/* Settings Page Specific Styles */

.settings-content {
    line-height: 1.6;
}

.settings-section {
    background: rgba(255, 255, 255, 0.8);
    padding: 2rem;
    margin-bottom: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    border-left: 4px solid #667eea;
}

.settings-section h2 {
    color: #333;
    margin-top: 0;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

.setting-group {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: 1rem;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(102, 126, 234, 0.1);
    transition: all 0.3s ease;
}

.setting-group:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: rgba(102, 126, 234, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.setting-group label {
    font-weight: 600;
    color: #333;
    cursor: pointer;
    font-size: 1rem;
}

.setting-group input[type="range"] {
    width: 200px;
    height: 8px;
    border-radius: 5px;
    background: linear-gradient(to right, #667eea, #764ba2);
    outline: none;
    opacity: 0.7;
    transition: opacity 0.2s;
    cursor: pointer;
}

.setting-group input[type="range"]:hover {
    opacity: 1;
}

.setting-group input[type="range"]::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    transition: all 0.2s ease;
}

.setting-group input[type="range"]::-webkit-slider-thumb:hover {
    transform: scale(1.2);
    background: #764ba2;
}

.setting-group input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.setting-group input[type="checkbox"] {
    width: 20px;
    height: 20px;
    accent-color: #667eea;
    cursor: pointer;
    transform: scale(1.2);
}

.setting-group select {
    padding: 0.5rem 1rem;
    border: 2px solid #667eea;
    border-radius: 8px;
    background: white;
    color: #333;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.setting-group select:hover,
.setting-group select:focus {
    border-color: #764ba2;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
    outline: none;
}

.setting-value {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
    min-width: 60px;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.setting-description {
    grid-column: 2 / 4;
    font-size: 0.85rem;
    color: #666;
    font-style: italic;
    margin-top: 0.5rem;
}

.settings-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
    padding: 2rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 15px;
    border: 2px dashed rgba(102, 126, 234, 0.3);
}

.settings-actions .btn {
    min-width: 160px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

/* Responsive Design for Settings */
@media (max-width: 768px) {
    .settings-section {
        padding: 1.5rem;
    }
    
    .setting-group {
        grid-template-columns: 1fr;
        gap: 0.8rem;
        text-align: center;
    }
    
    .setting-group label {
        font-size: 0.9rem;
    }
    
    .setting-group input[type="range"] {
        width: 100%;
        max-width: 300px;
    }
    
    .setting-description {
        grid-column: 1;
        text-align: center;
    }
    
    .settings-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .settings-actions .btn {
        width: 100%;
        max-width: 250px;
    }
}

@media (max-width: 480px) {
    .settings-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    .settings-section h2 {
        font-size: 1.2rem;
        text-align: center;
    }
    
    .setting-group {
        padding: 0.8rem;
        margin-bottom: 1rem;
    }
    
    .setting-group label {
        font-size: 0.85rem;
    }
    
    .setting-value {
        font-size: 0.8rem;
        padding: 0.2rem 0.6rem;
        min-width: 50px;
    }
    
    .settings-actions {
        padding: 1rem;
    }
}

/* Animation for settings changes */
.setting-group.changed {
    animation: settingChanged 0.5s ease;
}

@keyframes settingChanged {
    0% { background: rgba(76, 175, 80, 0.2); }
    100% { background: rgba(102, 126, 234, 0.05); }
}

/* Special styling for different setting types */
.setting-group[data-type="danger"] {
    border-left: 4px solid #f44336;
}

.setting-group[data-type="danger"]:hover {
    background: rgba(244, 67, 54, 0.1);
    border-color: rgba(244, 67, 54, 0.2);
}

.setting-group[data-type="warning"] {
    border-left: 4px solid #ff9800;
}

.setting-group[data-type="warning"]:hover {
    background: rgba(255, 152, 0, 0.1);
    border-color: rgba(255, 152, 0, 0.2);
}

.setting-group[data-type="success"] {
    border-left: 4px solid #4caf50;
}

.setting-group[data-type="success"]:hover {
    background: rgba(76, 175, 80, 0.1);
    border-color: rgba(76, 175, 80, 0.2);
}
