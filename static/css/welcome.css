/* Welcome Page Specific Styles */

.welcome-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
}

.welcome-header {
    margin-bottom: 3rem;
}

.welcome-header h1 {
    font-size: 5rem;
    font-weight: 900;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    text-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
    animation: gradientShift 3s ease-in-out infinite;
    letter-spacing: 0.1em;
    position: relative;
}

.welcome-header h1::before {
    content: "🦠";
    position: absolute;
    left: -1.5em;
    top: 0;
    font-size: 0.8em;
    animation: virusFloat 2s ease-in-out infinite;
}

.welcome-header h1::after {
    content: "🧬";
    position: absolute;
    right: -1.5em;
    top: 0;
    font-size: 0.8em;
    animation: dnaFloat 2s ease-in-out infinite reverse;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes virusFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(180deg); }
}

@keyframes dnaFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(-180deg); }
}

.welcome-header h2 {
    font-size: 1.8rem;
    color: white;
    margin: 0.5rem 0;
    font-weight: 300;
}

.subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin: 1rem 0;
    font-style: italic;
}

.welcome-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    margin-top: 2rem;
}

.game-preview {
    background: white;
    border-radius: 15px;
    padding: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transform: perspective(1000px) rotateY(-5deg);
    transition: transform 0.3s ease;
}

.game-preview:hover {
    transform: perspective(1000px) rotateY(0deg) scale(1.05);
}

.preview-toxic {
    background: linear-gradient(45deg, #8B0000, #FF0000);
    color: white;
    padding: 0.5rem;
    text-align: center;
    font-weight: bold;
    border-radius: 8px 8px 0 0;
}

.preview-area {
    background: linear-gradient(135deg, #E8F5E8, #D4F4D4);
    height: 120px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 1rem;
}

.preview-creet {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid;
    animation: creetFloat 4s ease-in-out infinite;
    cursor: pointer;
    transition: transform 0.2s ease;
    flex-shrink: 0;
}

.preview-creet:nth-child(2) {
    animation-delay: 0.5s;
}

.preview-creet:nth-child(3) {
    animation-delay: 1s;
}

/* Enhanced Game Preview Styles */
.preview-field {
    border: 3px solid #333;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.preview-toxic-river {
    background: linear-gradient(45deg, #8B0000, #FF0000);
    color: white;
    text-align: center;
    padding: 0.8rem;
    font-weight: bold;
    font-size: 1rem;
    box-shadow: 0 2px 10px rgba(139, 0, 0, 0.5);
    animation: toxicGlow 2s ease-in-out infinite;
}

@keyframes toxicGlow {
    0%, 100% { box-shadow: 0 2px 10px rgba(139, 0, 0, 0.5); }
    50% { box-shadow: 0 2px 15px rgba(255, 0, 0, 0.8); }
}

.preview-game-area {
    background: linear-gradient(135deg, #E8F5E8, #D4F4D4);
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: space-evenly;
    padding: 1rem;
    overflow: hidden;
}

.preview-hospital {
    background: linear-gradient(45deg, #0066CC, #00AAFF);
    color: white;
    text-align: center;
    padding: 0.8rem;
    font-weight: bold;
    font-size: 1rem;
    box-shadow: 0 -2px 10px rgba(0, 102, 204, 0.5);
    animation: hospitalGlow 3s ease-in-out infinite;
}

@keyframes hospitalGlow {
    0%, 100% { box-shadow: 0 -2px 10px rgba(0, 102, 204, 0.5); }
    50% { box-shadow: 0 -2px 15px rgba(0, 170, 255, 0.8); }
}

.preview-creet.healthy {
    background: #4CAF50;
    border-color: #2E7D32;
    animation: creetFloat 4s ease-in-out infinite;
}

.preview-creet.infected {
    background: #FF5722;
    border-color: #D32F2F;
    animation: creetFloat 4s ease-in-out infinite, infectPulse 1s ease-in-out infinite;
}

.preview-creet.berserk {
    background: #9C27B0;
    border-color: #6A1B9A;
    width: 22px;
    height: 22px;
    animation: creetFloat 4s ease-in-out infinite, berserkGrow 2s ease-in-out infinite;
}

.preview-creet.mean {
    background: #FF9800;
    border-color: #F57C00;
    width: 14px;
    height: 14px;
    animation: creetFloat 4s ease-in-out infinite, meanChase 3s linear infinite;
}

@keyframes creetFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

@keyframes infectPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes berserkGrow {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes meanChase {
    0% { transform: translateX(0px); }
    25% { transform: translateX(10px); }
    50% { transform: translateX(0px); }
    75% { transform: translateX(-10px); }
    100% { transform: translateX(0px); }
}

.preview-description {
    text-align: center;
    color: #555;
    font-size: 1.1rem;
    font-style: italic;
    margin: 0;
    line-height: 1.4;
}

/* Welcome Actions */
.welcome-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

/* Welcome Navigation Cards */
.welcome-navigation {
    margin-top: 3rem;
    text-align: center;
}

.welcome-navigation h3 {
    color: #333;
    margin-bottom: 2rem;
    font-size: 1.8rem;
    font-weight: 600;
}

.nav-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    justify-content: center;
    margin-top: 2rem;
}

.nav-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 25px;
    padding: 0.8rem 1.5rem;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 0.8rem;
    font-size: 0.9rem;
    font-weight: 600;
    min-width: 140px;
    justify-content: center;
}

.nav-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    border-color: #667eea;
    background: rgba(255, 255, 255, 1);
}

.nav-icon {
    font-size: 1.2rem;
    display: block;
}

.nav-card h4 {
    color: #333;
    margin: 0;
    font-size: 0.9rem;
    font-weight: 600;
}

.nav-card p {
    display: none; /* Hide descriptions in pill mode */
}

.preview-hospital {
    background: linear-gradient(45deg, #0066CC, #00AAFF);
    color: white;
    padding: 0.5rem;
    text-align: center;
    font-weight: bold;
    border-radius: 0 0 8px 8px;
}

.welcome-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Responsive Design for Welcome Page */
@media (max-width: 1024px) {
    .welcome-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .game-preview {
        transform: none;
        max-width: 400px;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .welcome-container {
        padding: 1rem;
    }

    .welcome-header h1 {
        font-size: 3.5rem;
        letter-spacing: 0.05em;
    }

    .welcome-header h1::before,
    .welcome-header h1::after {
        display: none; /* Hide decorative emojis on mobile for cleaner look */
    }

    .welcome-header h2 {
        font-size: 1.4rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    .btn {
        min-width: 250px;
        padding: 1.2rem 2rem;
        font-size: 1.2rem;
    }

    .nav-grid {
        gap: 0.5rem;
        justify-content: center;
    }

    .nav-card {
        padding: 0.6rem 1rem;
        min-width: 100px;
        font-size: 0.8rem;
        flex-shrink: 1;
    }

    .nav-icon {
        font-size: 1rem;
    }

    .nav-card h4 {
        font-size: 0.75rem;
    }
}

@media (max-width: 480px) {
    .welcome-header h1 {
        font-size: 2.8rem;
    }

    .welcome-header h2 {
        font-size: 1.2rem;
    }

    .btn {
        min-width: 200px;
        padding: 1rem 1.5rem;
        font-size: 1rem;
    }

    .game-preview {
        margin: 0;
        border-radius: 10px;
    }

    .preview-area {
        height: 80px;
        padding: 0.5rem;
    }

    .preview-creet {
        width: 15px;
        height: 15px;
    }
}
