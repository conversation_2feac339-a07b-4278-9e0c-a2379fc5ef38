body {
    margin: 0;
    padding: 0;
    font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* Navigation Menu */
.nav-menu {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.nav-menu li {
    margin: 0;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Welcome Page */
.welcome-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    text-align: center;
}

.welcome-header {
    margin-bottom: 3rem;
}

.welcome-header h1 {
    font-size: 4rem;
    font-weight: bold;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.welcome-header h2 {
    font-size: 1.8rem;
    color: white;
    margin: 0.5rem 0;
    font-weight: 300;
}

.subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin: 1rem 0;
    font-style: italic;
}

.welcome-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
    margin-top: 2rem;
}

.game-preview {
    background: white;
    border-radius: 15px;
    padding: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    transform: perspective(1000px) rotateY(-5deg);
    transition: transform 0.3s ease;
}

.game-preview:hover {
    transform: perspective(1000px) rotateY(0deg) scale(1.05);
}

.preview-toxic {
    background: linear-gradient(45deg, #8B0000, #FF0000);
    color: white;
    padding: 0.5rem;
    text-align: center;
    font-weight: bold;
    border-radius: 8px 8px 0 0;
}

.preview-area {
    background: linear-gradient(135deg, #E8F5E8, #D4F4D4);
    height: 120px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 1rem;
}

.preview-creet {
    width: 20px;
    height: 20px;
    background: #4CAF50;
    border-radius: 50%;
    border: 2px solid #2E7D32;
    animation: float 2s ease-in-out infinite;
}

.preview-creet:nth-child(2) {
    animation-delay: 0.5s;
}

.preview-creet:nth-child(3) {
    animation-delay: 1s;
}

.preview-hospital {
    background: linear-gradient(45deg, #0066CC, #00AAFF);
    color: white;
    padding: 0.5rem;
    text-align: center;
    font-weight: bold;
    border-radius: 0 0 8px 8px;
}

.welcome-actions {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.btn {
    display: inline-block;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    min-width: 200px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.6);
}

.btn-secondary {
    background: linear-gradient(45deg, #2196F3, #1976D2);
    color: white;
    box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
}

.btn-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.6);
}

.btn-tertiary {
    background: linear-gradient(45deg, #FF9800, #F57C00);
    color: white;
    box-shadow: 0 5px 15px rgba(255, 152, 0, 0.4);
}

.btn-tertiary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 152, 0, 0.6);
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* Page Container for Instructions and Credits */
.page-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    margin-top: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.page-container h1 {
    color: #333;
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2.5rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-container h2 {
    color: #555;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
    margin-top: 2rem;
}

.page-container section {
    margin-bottom: 2rem;
}

.page-container ul {
    line-height: 1.8;
}

.page-container li {
    margin-bottom: 0.5rem;
}

.page-container strong {
    color: #667eea;
}

/* Responsive Design */
@media (max-width: 768px) {
    .welcome-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .welcome-header h1 {
        font-size: 2.5rem;
    }

    .nav-menu ul {
        flex-direction: column;
        gap: 0.5rem;
    }

    .page-container {
        margin: 1rem;
        padding: 1rem;
    }
}

/* Game Container */
#game-container {
    width: 800px;
    height: 700px;
    margin: 2rem auto;
    border: 3px solid #333;
    background-color: white;
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

#toxic-river {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 50px;
    background: linear-gradient(45deg, #8B0000, #FF0000);
    color: white;
    text-align: center;
    line-height: 50px;
    font-weight: bold;
    z-index: 1;
    box-shadow: 0 2px 10px rgba(139, 0, 0, 0.5);
}

#game-area {
    position: absolute;
    top: 50px;
    left: 0;
    width: 100%;
    height: 550px;
    background: linear-gradient(135deg, #E8F5E8, #D4F4D4);
    z-index: 2;
}

#hospital {
    position: absolute;
    bottom: 50px;
    left: 0;
    width: 100%;
    height: 50px;
    background: linear-gradient(45deg, #0066CC, #00AAFF);
    color: white;
    text-align: center;
    line-height: 50px;
    font-weight: bold;
    z-index: 1;
    box-shadow: 0 -2px 10px rgba(0, 102, 204, 0.5);
}

#game-info {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 50px;
    background-color: #333;
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-sizing: border-box;
    z-index: 3;
}

#score {
    font-weight: bold;
    font-size: 16px;
}

#game-over {
    font-size: 24px;
    font-weight: bold;
    color: #FF0000;
}
