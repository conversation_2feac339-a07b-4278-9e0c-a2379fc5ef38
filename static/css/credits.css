/* Credits Page Specific Styles */

.credits-content {
    line-height: 1.7;
}

.credits-content section {
    background: rgba(255, 255, 255, 0.8);
    padding: 2rem;
    margin-bottom: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.credits-content section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
}

.credits-content h2 {
    color: #333;
    margin-top: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.8rem;
}

.credits-content h2::before {
    font-size: 1.5em;
}

.credits-content section:nth-child(1) h2::before {
    content: "🎮";
}

.credits-content section:nth-child(2) h2::before {
    content: "🛠️";
}

.credits-content section:nth-child(3) h2::before {
    content: "📋";
}

.credits-content section:nth-child(4) h2::before {
    content: "🎨";
}

.credits-content section:nth-child(5) h2::before {
    content: "👨‍💻";
}

.credits-content ul {
    padding-left: 2rem;
}

.credits-content li {
    margin-bottom: 1rem;
    position: relative;
}

.credits-content li::marker {
    color: #667eea;
    font-weight: bold;
}

.credits-content strong {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 1.1em;
}

.credits-content p {
    margin-bottom: 1rem;
    text-align: justify;
}

/* Technology badges */
.tech-badge {
    display: inline-block;
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0.2rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}



/* Creator section gets normal styling like other sections */
.credits-content section:nth-child(5) {
    text-align: center;
}

/* Heart animation */
.heart {
    color: #e74c3c;
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Acknowledgment section */
.acknowledgment {
    background: rgba(76, 175, 80, 0.1);
    border: 2px solid rgba(76, 175, 80, 0.3);
    padding: 1.5rem;
    border-radius: 10px;
    margin: 2rem 0;
    text-align: center;
}

.acknowledgment h3 {
    color: #4CAF50;
    margin-top: 0;
}

.acknowledgment p {
    margin-bottom: 0;
    font-style: italic;
}
