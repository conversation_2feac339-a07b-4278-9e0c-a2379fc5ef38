/* CSS Variables for Customization */
:root {
    --base-font-size: 16px;
    --animation-speed: 1;
}

/* Common styles shared across all pages */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-size: var(--base-font-size);
    line-height: 1.6;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    transition: all calc(0.3s / var(--animation-speed)) ease;
}

/* Navigation Menu */
.nav-menu {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem 0;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    gap: 2rem;
}

.nav-menu li {
    margin: 0;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Common Button Styles */
.btn {
    display: inline-block;
    padding: 1rem 2rem;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    min-width: 200px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(76, 175, 80, 0.6);
}

.btn-secondary {
    background: linear-gradient(45deg, #2196F3, #1976D2);
    color: white;
    box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
}

.btn-secondary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(33, 150, 243, 0.6);
}

.btn-tertiary {
    background: linear-gradient(45deg, #FF9800, #F57C00);
    color: white;
    box-shadow: 0 5px 15px rgba(255, 152, 0, 0.4);
}

.btn-tertiary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 152, 0, 0.6);
}

/* Page Container for Instructions and Credits */
.page-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    margin-top: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.page-container h1 {
    color: #333;
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2.5rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.page-container h2 {
    color: #555;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
    margin-top: 2rem;
}

.page-container section {
    margin-bottom: 2rem;
}

.page-container ul {
    line-height: 1.8;
}

.page-container li {
    margin-bottom: 0.5rem;
}

.page-container strong {
    color: #667eea;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .nav-menu {
        padding: 0.8rem 0;
    }

    .nav-menu ul {
        gap: 1.5rem;
    }

    .page-container {
        margin: 1.5rem;
        padding: 1.5rem;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        padding: 0.5rem 0;
    }

    .nav-menu ul {
        flex-wrap: wrap;
        gap: 0.8rem;
        padding: 0 1rem;
    }

    .nav-menu a {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }

    .page-container {
        margin: 1rem;
        padding: 1rem;
    }

    .page-container h1 {
        font-size: 2rem;
    }

    .btn {
        min-width: 180px;
        padding: 0.9rem 1.8rem;
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    body {
        font-size: 14px;
    }

    .nav-menu ul {
        flex-direction: column;
        gap: 0.5rem;
        padding: 0 0.5rem;
    }

    .nav-menu a {
        padding: 0.8rem 1rem;
        font-size: 0.9rem;
        border-radius: 15px;
    }

    .page-container {
        margin: 0.5rem;
        padding: 0.8rem;
        border-radius: 10px;
    }

    .page-container h1 {
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
    }

    .page-container h2 {
        font-size: 1.3rem;
        margin-top: 1.5rem;
    }

    .btn {
        min-width: 160px;
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
        border-radius: 25px;
    }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
    .nav-menu a,
    .btn {
        /* Larger touch targets */
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .nav-menu a:active,
    .btn:active {
        transform: scale(0.95);
    }
}

/* Global Theme Styles */
/* Default theme - no additional styles needed */

/* Dark theme */
body.dark-theme {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%) !important;
    color: #ecf0f1 !important;
}

body.dark-theme .nav-menu {
    background: rgba(44, 62, 80, 0.95) !important;
}

body.dark-theme .nav-menu a {
    color: #ecf0f1 !important;
}

body.dark-theme .page-container {
    background: rgba(44, 62, 80, 0.9) !important;
    color: #ecf0f1 !important;
}

body.dark-theme .page-container h1,
body.dark-theme .page-container h2,
body.dark-theme .page-container h3 {
    color: #ecf0f1 !important;
}

/* Nature theme */
body.nature-theme {
    background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%) !important;
}

body.nature-theme .nav-menu {
    background: rgba(39, 174, 96, 0.95) !important;
}

body.nature-theme .page-container {
    background: rgba(46, 204, 113, 0.1) !important;
}

/* Sunset theme */
body.sunset-theme {
    background: linear-gradient(135deg, #e74c3c 0%, #f39c12 100%) !important;
}

body.sunset-theme .nav-menu {
    background: rgba(231, 76, 60, 0.95) !important;
}

body.sunset-theme .page-container {
    background: rgba(243, 156, 18, 0.1) !important;
}

/* Ocean theme */
body.ocean-theme {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%) !important;
}

body.ocean-theme .nav-menu {
    background: rgba(52, 152, 219, 0.95) !important;
}

body.ocean-theme .page-container {
    background: rgba(41, 128, 185, 0.1) !important;
}

/* Neon theme */
body.neon-theme {
    background: linear-gradient(135deg, #8e44ad 0%, #e91e63 100%) !important;
    color: #fff !important;
}

body.neon-theme .nav-menu {
    background: rgba(142, 68, 173, 0.95) !important;
}

body.neon-theme .nav-menu a {
    color: #fff !important;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

body.neon-theme .page-container {
    background: rgba(233, 30, 99, 0.1) !important;
    color: #fff !important;
    box-shadow: 0 0 20px rgba(142, 68, 173, 0.3);
}

body.neon-theme .page-container h1,
body.neon-theme .page-container h2,
body.neon-theme .page-container h3 {
    color: #fff !important;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

/* Accessibility Features */
/* Reduce Motion */
body.reduce-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
}

/* High Contrast */
body.high-contrast {
    filter: contrast(150%) !important;
}

body.high-contrast .nav-menu {
    background: #000 !important;
    border: 2px solid #fff !important;
}

body.high-contrast .nav-menu a {
    color: #fff !important;
    border: 1px solid #fff !important;
}

body.high-contrast .page-container {
    background: #fff !important;
    color: #000 !important;
    border: 2px solid #000 !important;
}

body.high-contrast .btn {
    background: #000 !important;
    color: #fff !important;
    border: 2px solid #fff !important;
}
