// Generated by js_of_ocaml
//# buildInfo:effects=false, kind=exe, use-js-string=true, version=5.9.1
(function(a){typeof
globalThis!=="object"&&(this?b():(a.defineProperty(a.prototype,"_T_",{configurable:true,get:b}),_T_));function
b(){var
b=this||self;b.globalThis=b;delete
a.prototype._T_}}(Object));(function(c){"use strict";var
mO=typeof
module==="object"&&module.exports||c,b9=10.,av=" ",W="0",dr="compare: functional value",br=1000,dq="mkdir",ge="@[",bu="int_of_string",b5="game-area",f2=180.,f3="%u",gd="/static/",f1=1.2,dz="node:fs",fv="%i",fw="dir",gC="%ni",ao=0xff,gB="initial-creets",gc="Assert_failure",fu="0x",fZ="#9C27B0",f0=0x7ff0,ft=0xdfff,fY="End_of_file",aF="%.1fpx",fs=": closedir failed",gA="Out_of_memory",gb=240.,fX=345.,bt="auto-heal",fr="Not_found",dh=220.,dy="Failure",fq=405.,fW=": (",cd="Unix.Unix_error",fU=480.,fV="^",dg="ENOTDIR",ga="mean-chance",fp="%Li",fo="Invalid_argument",b4=254,a1="+",gz="false",dp=0.016,gy=", characters ",an=0xffffff,fS=460.,fT=350.,b8=120,f$=1027,bq=1024,fR=125.,fQ="Pervasives.do_at_exit",fn=140.,fm="Lwt.%s",fP=65536,gx=1000.,dx=280.,df="game-over",de=0xf,ad=", ",fl=512,gv="Match_failure",gw="closedir",fj="%li",fk=1026,gu="#6A1B9A",dn="=",c8=340.,dd=2147483000.,fi="#FF9800",c7="nan",e="",c6="rmdir",b7="Fatal error: exception ",fO="infinity",a="camlinternalFormat.ml",fN="fd ",fM=410.,cc=": ",fL="#game-container",y=248,a0=0.85,gt=0xe0,a2=24029,fh="block",fJ=".game-controls",fK=0xf0,fg="infection-rate",fI="Sys_error",ab=0x80,ff=" Creets",f_="ENOTEMPTY",gs="EEXIST",dm=1255,dc=1073741823,c4=100.,c5=" : flags Open_text and Open_binary are not compatible",ax="px",fe=160.,fG="#2E7D32",fH="time",db="e",c3=": Not a directory",da="ENOENT",f9="Stack_overflow",gr=" not found, using default: ",a3=50.,fd=225.,gq="Undefined_recursive_module",dw="none",gp="#4CAF50",cb=120.,ae=0x8000,fF=0x800,f8=-48,go="1.0",fb="error",fc=" : is a directory",fD="@{",fE="Division_by_zero",aU=".",f7="#D32F2F",aG=0x3f,gn="berserk-chance",dl="Creet ",c$=" : file already exists",bp=128,dv=750.,bs=": No such file or directory",c_=", using default: ",fC=255,gm=256,fB=100,c2="index out of bounds",f6="Lwt.",aw="/",b3=252,fA=290.,gl=285.,c1="%d",b6=200.,gk=6.283185307179586,fa=": file descriptor already closed",aT="-",dk=580.,c0="EBADF",fz="true",e$="loading",e_=15.,fy=-97,dj="10",gi="#F57C00",gj="Printexc.handle_uncaught_exception",ca="h42n42_",bo=400.,e9=12520,gh=420.,du=" : flags Open_rdonly and Open_wronly are not compatible",b$=300.,c9="difficulty-progression",f5="Fatal error: exception %s\n",e8=250,e7=800.,fx="([^/]+)",di="jsError",e6=103,b2='"',dt=127,af=0xffff,e5=0xdc00,gg="Sys_blocked_io",f4="heal-time",ds="game-speed",gf="_",b_="_bigarr02";function
lM(a,b,c,d,e){if(d<=b)for(var
f=1;f<=e;f++)c[d+f]=a[b+f];else
for(var
f=e;f>=1;f--)c[d+f]=a[b+f];return 0}function
gE(a,b,c){if(a[1]===b){a[1]=c;return 1}return 0}function
lO(a,b){var
c=a[1];a[1]+=b;return c}function
cf(a){return a[1]}var
hf={};function
mQ(a){if(hf[a])return hf[a];var
b=c.process;if(b&&b.env&&b.env[a]!==undefined)return b.env[a];if(c.jsoo_env&&typeof
c.jsoo_env[a]==="string")return c.jsoo_env[a]}var
co=0;(function(){var
c=mQ("OCAMLRUNPARAM");if(c!==undefined){var
b=c.split(",");for(var
a=0;a<b.length;a++)if(b[a]==="b"){co=1;break}else if(b[a].startsWith("b="))co=+b[a].slice(2);else
continue}}());var
mE=co,I=[0];function
l0(a,b){if(!a.js_error||b||a[0]===y)a.js_error=new
c.Error("Js exception containing backtrace");return a}function
i(a,b){return co&&mE?l0(a,b):a}function
mD(a,b){throw i([0,a,b])}function
_(a){return a}function
dR(a,b){mD(a,_(b))}function
w(a){dR(I.Invalid_argument,a)}function
gI(a){switch(a){case
7:case
10:case
11:return 2;default:return 1}}function
gG(a,b){var
c;switch(a){case
0:c=Float32Array;break;case
1:c=Float64Array;break;case
2:c=Int8Array;break;case
3:c=Uint8Array;break;case
4:c=Int16Array;break;case
5:c=Uint16Array;break;case
6:c=Int32Array;break;case
7:c=Int32Array;break;case
8:c=Int32Array;break;case
9:c=Int32Array;break;case
10:c=Float32Array;break;case
11:c=Float64Array;break;case
12:c=Uint8Array;break}if(!c)w("Bigarray.create: unsupported kind");var
d=new
c(b*gI(a));return d}function
cg(a){var
d=a.length,c=1;for(var
b=0;b<d;b++){if(a[b]<0)w("Bigarray.create: negative dimension");c=c*a[b]}return c}var
gZ=Math.pow(2,-24);function
mC(a){throw a}function
dS(){mC(I.Division_by_zero)}function
d(a,b,c){this.lo=a&an;this.mi=b&an;this.hi=c&af}d.prototype.caml_custom="_j";d.prototype.copy=function(){return new
d(this.lo,this.mi,this.hi)};d.prototype.ucompare=function(a){if(this.hi>a.hi)return 1;if(this.hi<a.hi)return-1;if(this.mi>a.mi)return 1;if(this.mi<a.mi)return-1;if(this.lo>a.lo)return 1;if(this.lo<a.lo)return-1;return 0};d.prototype.compare=function(a){var
b=this.hi<<16,c=a.hi<<16;if(b>c)return 1;if(b<c)return-1;if(this.mi>a.mi)return 1;if(this.mi<a.mi)return-1;if(this.lo>a.lo)return 1;if(this.lo<a.lo)return-1;return 0};d.prototype.neg=function(){var
a=-this.lo,b=-this.mi+(a>>24),c=-this.hi+(b>>24);return new
d(a,b,c)};d.prototype.add=function(a){var
b=this.lo+a.lo,c=this.mi+a.mi+(b>>24),e=this.hi+a.hi+(c>>24);return new
d(b,c,e)};d.prototype.sub=function(a){var
b=this.lo-a.lo,c=this.mi-a.mi+(b>>24),e=this.hi-a.hi+(c>>24);return new
d(b,c,e)};d.prototype.mul=function(a){var
b=this.lo*a.lo,c=(b*gZ|0)+this.mi*a.lo+this.lo*a.mi,e=(c*gZ|0)+this.hi*a.lo+this.mi*a.mi+this.lo*a.hi;return new
d(b,c,e)};d.prototype.isZero=function(){return(this.lo|this.mi|this.hi)===0};d.prototype.isNeg=function(){return this.hi<<16<0};d.prototype.and=function(a){return new
d(this.lo&a.lo,this.mi&a.mi,this.hi&a.hi)};d.prototype.or=function(a){return new
d(this.lo|a.lo,this.mi|a.mi,this.hi|a.hi)};d.prototype.xor=function(a){return new
d(this.lo^a.lo,this.mi^a.mi,this.hi^a.hi)};d.prototype.shift_left=function(a){a=a&63;if(a===0)return this;if(a<24)return new
d(this.lo<<a,this.mi<<a|this.lo>>24-a,this.hi<<a|this.mi>>24-a);if(a<48)return new
d(0,this.lo<<a-24,this.mi<<a-24|this.lo>>48-a);return new
d(0,0,this.lo<<a-48)};d.prototype.shift_right_unsigned=function(a){a=a&63;if(a===0)return this;if(a<24)return new
d(this.lo>>a|this.mi<<24-a,this.mi>>a|this.hi<<24-a,this.hi>>a);if(a<48)return new
d(this.mi>>a-24|this.hi<<48-a,this.hi>>a-24,0);return new
d(this.hi>>a-48,0,0)};d.prototype.shift_right=function(a){a=a&63;if(a===0)return this;var
c=this.hi<<16>>16;if(a<24)return new
d(this.lo>>a|this.mi<<24-a,this.mi>>a|c<<24-a,this.hi<<16>>a>>>16);var
b=this.hi<<16>>31;if(a<48)return new
d(this.mi>>a-24|this.hi<<48-a,this.hi<<16>>a-24>>16,b&af);return new
d(this.hi<<16>>a-32,b,b)};d.prototype.lsl1=function(){this.hi=this.hi<<1|this.mi>>23;this.mi=(this.mi<<1|this.lo>>23)&an;this.lo=this.lo<<1&an};d.prototype.lsr1=function(){this.lo=(this.lo>>>1|this.mi<<23)&an;this.mi=(this.mi>>>1|this.hi<<23)&an;this.hi=this.hi>>>1};d.prototype.udivmod=function(a){var
e=0,c=this.copy(),b=a.copy(),f=new
d(0,0,0);while(c.ucompare(b)>0){e++;b.lsl1()}while(e>=0){e--;f.lsl1();if(c.ucompare(b)>=0){f.lo++;c=c.sub(b)}b.lsr1()}return{quotient:f,modulus:c}};d.prototype.div=function(a){var
b=this;if(a.isZero())dS();var
d=b.hi^a.hi;if(b.hi&ae)b=b.neg();if(a.hi&ae)a=a.neg();var
c=b.udivmod(a).quotient;if(d&ae)c=c.neg();return c};d.prototype.mod=function(a){var
b=this;if(a.isZero())dS();var
d=b.hi;if(b.hi&ae)b=b.neg();if(a.hi&ae)a=a.neg();var
c=b.udivmod(a).modulus;if(d&ae)c=c.neg();return c};d.prototype.toInt=function(){return this.lo|this.mi<<24};d.prototype.toFloat=function(){return(this.hi<<16)*Math.pow(2,32)+this.mi*Math.pow(2,24)+this.lo};d.prototype.toArray=function(){return[this.hi>>8,this.hi&ao,this.mi>>16,this.mi>>8&ao,this.mi&ao,this.lo>>16,this.lo>>8&ao,this.lo&ao]};d.prototype.lo32=function(){return this.lo|(this.mi&ao)<<24};d.prototype.hi32=function(){return this.mi>>>8&af|this.hi<<16};function
l9(a,b){return new
d(a&an,a>>>24&ao|(b&af)<<8,b>>>16&af)}function
dK(a){return a.hi32()}function
dL(a){return a.lo32()}function
bw(){w(c2)}var
lQ=b_;function
aH(a,b,c,d){this.kind=a;this.layout=b;this.dims=c;this.data=d}aH.prototype.caml_custom=lQ;aH.prototype.offset=function(a){var
c=0;if(typeof
a==="number")a=[a];if(!Array.isArray(a))w("bigarray.js: invalid offset");if(this.dims.length!==a.length)w("Bigarray.get/set: bad number of dimensions");if(this.layout===0)for(var
b=0;b<this.dims.length;b++){if(a[b]<0||a[b]>=this.dims[b])bw();c=c*this.dims[b]+a[b]}else
for(var
b=this.dims.length-1;b>=0;b--){if(a[b]<1||a[b]>this.dims[b])bw();c=c*this.dims[b]+(a[b]-1)}return c};aH.prototype.get=function(a){switch(this.kind){case
7:var
d=this.data[a*2+0],b=this.data[a*2+1];return l9(d,b);case
10:case
11:var
e=this.data[a*2+0],c=this.data[a*2+1];return[b4,e,c];default:return this.data[a]}};aH.prototype.set=function(a,b){switch(this.kind){case
7:this.data[a*2+0]=dL(b);this.data[a*2+1]=dK(b);break;case
10:case
11:this.data[a*2+0]=b[1];this.data[a*2+1]=b[2];break;default:this.data[a]=b;break}return 0};aH.prototype.fill=function(a){switch(this.kind){case
7:var
c=dL(a),e=dK(a);if(c===e)this.data.fill(c);else
for(var
b=0;b<this.data.length;b++)this.data[b]=b%2===0?c:e;break;case
10:case
11:var
d=a[1],f=a[2];if(d===f)this.data.fill(d);else
for(var
b=0;b<this.data.length;b++)this.data[b]=b%2===0?d:f;break;default:this.data.fill(a);break}};aH.prototype.compare=function(a,b){if(this.layout!==a.layout||this.kind!==a.kind){var
f=this.kind|this.layout<<8,g=a.kind|a.layout<<8;return g-f}if(this.dims.length!==a.dims.length)return a.dims.length-this.dims.length;for(var
c=0;c<this.dims.length;c++)if(this.dims[c]!==a.dims[c])return this.dims[c]<a.dims[c]?-1:1;switch(this.kind){case
0:case
1:case
10:case
11:var
d,e;for(var
c=0;c<this.data.length;c++){d=this.data[c];e=a.data[c];if(d<e)return-1;if(d>e)return 1;if(d!==e){if(!b)return Number.NaN;if(!Number.isNaN(d))return 1;if(!Number.isNaN(e))return-1}}break;case
7:for(var
c=0;c<this.data.length;c+=2){if(this.data[c+1]<a.data[c+1])return-1;if(this.data[c+1]>a.data[c+1])return 1;if(this.data[c]>>>0<a.data[c]>>>0)return-1;if(this.data[c]>>>0>a.data[c]>>>0)return 1}break;case
2:case
3:case
4:case
5:case
6:case
8:case
9:case
12:for(var
c=0;c<this.data.length;c++){if(this.data[c]<a.data[c])return-1;if(this.data[c]>a.data[c])return 1}break}return 0};function
a4(a,b,c,d){this.kind=a;this.layout=b;this.dims=c;this.data=d}a4.prototype=new
aH();a4.prototype.offset=function(a){if(typeof
a!=="number")if(Array.isArray(a)&&a.length===1)a=a[0];else
w("Ml_Bigarray_c_1_1.offset");if(a<0||a>=this.dims[0])bw();return a};a4.prototype.get=function(a){return this.data[a]};a4.prototype.set=function(a,b){this.data[a]=b;return 0};a4.prototype.fill=function(a){this.data.fill(a);return 0};function
dA(a,b,c,d){var
e=gI(a);if(cg(c)*e!==d.length)w("length doesn't match dims");if(b===0&&c.length===1&&e===1)return new
a4(a,b,c,d);return new
aH(a,b,c,d)}function
g3(a){return a.slice(1)}function
lP(a,b,c){var
d=g3(c),e=gG(a,cg(d));return dA(a,b,d,e)}function
bx(a,b,c){a.set(a.offset(b),c);return 0}function
bB(a,b,c){var
d=String.fromCharCode;if(b===0&&c<=4096&&c===a.length)return d.apply(null,a);var
f=e;for(;0<c;b+=bq,c-=bq)f+=d.apply(null,a.slice(b,b+Math.min(c,bq)));return f}function
ci(a){var
c=new
Uint8Array(a.l),e=a.c,d=e.length,b=0;for(;b<d;b++)c[b]=e.charCodeAt(b);for(d=a.l;b<d;b++)c[b]=0;a.c=c;a.t=4;return c}function
aI(a,b,c,d,e){if(e===0)return 0;if(d===0&&(e>=c.l||c.t===2&&e>=c.c.length)){c.c=a.t===4?bB(a.c,b,e):b===0&&a.c.length===e?a.c:a.c.slice(b,b+e);c.t=c.c.length===c.l?0:2}else if(c.t===2&&d===c.c.length){c.c+=a.t===4?bB(a.c,b,e):b===0&&a.c.length===e?a.c:a.c.slice(b,b+e);c.t=c.c.length===c.l?0:2}else{if(c.t!==4)ci(c);var
g=a.c,h=c.c;if(a.t===4)if(d<=b)for(var
f=0;f<e;f++)h[d+f]=g[b+f];else
for(var
f=e-1;f>=0;f--)h[d+f]=g[b+f];else{var
i=Math.min(e,g.length-b);for(var
f=0;f<i;f++)h[d+f]=g.charCodeAt(b+f);for(;f<e;f++)h[d+f]=0}}return 0}function
a$(a,b){if(a===0)return e;if(b.repeat)return b.repeat(a);var
d=e,c=0;for(;;){if(a&1)d+=b;a>>=1;if(a===0)return d;b+=b;c++;if(c===9)b.slice(0,1)}}function
cj(a){if(a.t===2)a.c+=a$(a.l-a.c.length,"\0");else
a.c=bB(a.c,0,a.c.length);a.t=0}function
dV(a){if(a.length<24){for(var
b=0;b<a.length;b++)if(a.charCodeAt(b)>dt)return false;return true}else
return!/[^\x00-\x7f]/.test(a)}function
hd(a){for(var
k=e,d=e,h,g,i,b,c=0,j=a.length;c<j;c++){g=a.charCodeAt(c);if(g<ab){for(var
f=c+1;f<j&&(g=a.charCodeAt(f))<ab;f++);if(f-c>fl){d.slice(0,1);k+=d;d=e;k+=a.slice(c,f)}else
d+=a.slice(c,f);if(f===j)break;c=f}b=1;if(++c<j&&((i=a.charCodeAt(c))&-64)===bp){h=i+(g<<6);if(g<gt){b=h-0x3080;if(b<ab)b=1}else{b=2;if(++c<j&&((i=a.charCodeAt(c))&-64)===bp){h=i+(h<<6);if(g<fK){b=h-0xe2080;if(b<fF||b>=0xd7ff&&b<0xe000)b=2}else{b=3;if(++c<j&&((i=a.charCodeAt(c))&-64)===bp&&g<0xf5){b=i-0x3c82080+(h<<6);if(b<0x10000||b>0x10ffff)b=3}}}}}if(b<4){c-=b;d+="\ufffd"}else if(b>af)d+=String.fromCharCode(0xd7c0+(b>>10),e5+(b&0x3ff));else
d+=String.fromCharCode(b);if(d.length>bq){d.slice(0,1);k+=d;d=e}}return k+d}function
ay(a,b,c){this.t=a;this.c=b;this.l=c}ay.prototype.toString=function(){switch(this.t){case
9:case
8:return this.c;case
4:case
2:cj(this);case
0:if(dV(this.c))this.t=9;else
this.t=8;return this.c}};ay.prototype.toUtf16=function(){var
a=this.toString();if(this.t===9)return a;return hd(a)};ay.prototype.slice=function(){var
a=this.t===4?this.c.slice():this.c;return new
ay(this.t,a,this.l)};function
gL(a){return new
ay(0,a,a.length)}function
aB(a){return a}function
ah(a){return gL(aB(a))}function
ch(a,b,c,d,e){aI(ah(a),b,c,d,e);return 0}function
a7(a){return new
d(a[7]<<0|a[6]<<8|a[5]<<16,a[4]<<0|a[3]<<8|a[2]<<16,a[1]<<0|a[0]<<8)}function
a5(a,b){switch(a.t&6){case
0:return a.c.charCodeAt(b);case
2:if(b>=a.c.length)return 0;return a.c.charCodeAt(b);case
4:return a.c[b]}}function
dB(){w(c2)}function
lU(a,b){if(b>>>0>=a.l-7)dB();var
d=new
Array(8);for(var
c=0;c<8;c++)d[7-c]=a5(a,b+c);return a7(d)}function
u(a,b,c){c&=ao;if(a.t!==4){if(b===a.c.length){a.c+=String.fromCharCode(c);if(b+1===a.l)a.t=0;return 0}ci(a)}a.c[b]=c;return 0}function
az(a,b,c){if(b>>>0>=a.l)dB();return u(a,b,c)}function
a8(a){return a.toArray()}function
lV(a,b,c){if(b>>>0>=a.l-7)dB();var
e=a8(c);for(var
d=0;d<8;d++)u(a,b+7-d,e[d]);return 0}function
ap(d,c){var
f=d.l>=0?d.l:d.l=d.length,e=c.length,b=f-e;if(b===0)return d.apply(null,c);else if(b<0){var
a=d.apply(null,c.slice(0,f));if(typeof
a!=="function")return a;return ap(a,c.slice(f))}else{switch(b){case
1:{var
a=function(a){var
f=new
Array(e+1);for(var
b=0;b<e;b++)f[b]=c[b];f[e]=a;return d.apply(null,f)};break}case
2:{var
a=function(a,b){var
g=new
Array(e+2);for(var
f=0;f<e;f++)g[f]=c[f];g[e]=a;g[e+1]=b;return d.apply(null,g)};break}default:var
a=function(){var
e=arguments.length===0?1:arguments.length,b=new
Array(c.length+e);for(var
a=0;a<c.length;a++)b[a]=c[a];for(var
a=0;a<arguments.length;a++)b[c.length+a]=arguments[a];return ap(d,b)}}a.l=b;return a}}function
aW(a,b){if(b>>>0>=a.length-1)bw();return a}function
lW(a){if(Number.isFinite(a)){if(Math.abs(a)>=2.2250738585072014e-308)return 0;if(a!==0)return 1;return 2}return Number.isNaN(a)?4:3}function
lX(){return[0]}function
A(a){if(a<0)w("Bytes.create");return new
ay(a?2:9,e,a)}var
gQ=[0];function
lZ(a){return gQ}function
gR(a){gQ=a}function
l1(a,b,c,d){if(c>0)if(b===0&&(c>=a.l||a.t===2&&c>=a.c.length))if(d===0){a.c=e;a.t=2}else{a.c=a$(c,String.fromCharCode(d));a.t=c===a.l?0:2}else{if(a.t!==4)ci(a);for(c+=b;b<c;b++)a.c[b]=d}return 0}function
Z(a){if(!I.Failure)I.Failure=[y,_(dy),-3];dR(I.Failure,a)}function
l2(a){var
b,f=/^ *[-+]?(?:\d*\.?\d+|\d+\.?\d*)(?:[eE][-+]?\d+)?$/;a=aB(a);b=+a;if(!Number.isNaN(b)&&f.test(a))return b;a=a.replace(/_/g,e);b=+a;if(!Number.isNaN(b)&&f.test(a)||/^[+-]?nan$/i.test(a))return b;var
c=/^ *([+-]?)0x([0-9a-f]+)\.?([0-9a-f]*)(p([+-]?[0-9]+))?$/i.exec(a);if(c){var
d=c[3].replace(/0+$/,e),h=Number.parseInt(c[1]+c[2]+d,16),g=(c[5]|0)-4*d.length;b=h*Math.pow(2,g);return b}if(/^\+?inf(inity)?$/i.test(a))return Number.POSITIVE_INFINITY;if(/^-inf(inity)?$/i.test(a))return Number.NEGATIVE_INFINITY;Z("float_of_string")}function
dQ(a){a=aB(a);var
e=a.length;if(e>31)w("format_int: format too long");var
b={justify:a1,signstyle:aT,filler:av,alternate:false,base:0,signedconv:false,width:0,uppercase:false,sign:1,prec:-1,conv:"f"};for(var
d=0;d<e;d++){var
c=a.charAt(d);switch(c){case"-":b.justify=aT;break;case"+":case" ":b.signstyle=c;break;case"0":b.filler=W;break;case"#":b.alternate=true;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":b.width=0;while(c=a.charCodeAt(d)-48,c>=0&&c<=9){b.width=b.width*10+c;d++}d--;break;case".":b.prec=0;d++;while(c=a.charCodeAt(d)-48,c>=0&&c<=9){b.prec=b.prec*10+c;d++}d--;break;case"d":case"i":b.signedconv=true;b.base=10;break;case"u":b.base=10;break;case"x":b.base=16;break;case"X":b.base=16;b.uppercase=true;break;case"o":b.base=8;break;case"e":case"f":case"g":b.signedconv=true;b.conv=c;break;case"E":case"F":case"G":b.signedconv=true;b.uppercase=true;b.conv=c.toLowerCase();break}}return b}function
dE(a,b){if(a.uppercase)b=b.toUpperCase();var
f=b.length;if(a.signedconv&&(a.sign<0||a.signstyle!==aT))f++;if(a.alternate){if(a.base===8)f+=1;if(a.base===16)f+=2}var
c=e;if(a.justify===a1&&a.filler===av)for(var
d=f;d<a.width;d++)c+=av;if(a.signedconv)if(a.sign<0)c+=aT;else if(a.signstyle!==aT)c+=a.signstyle;if(a.alternate&&a.base===8)c+=W;if(a.alternate&&a.base===16)c+=a.uppercase?"0X":fu;if(a.justify===a1&&a.filler===W)for(var
d=f;d<a.width;d++)c+=W;c+=b;if(a.justify===aT)for(var
d=f;d<a.width;d++)c+=av;return _(c)}function
dF(a,b){function
j(a,b){if(Math.abs(a)<1.0)return a.toFixed(b);else{var
c=Number.parseInt(a.toString().split(a1)[1]);if(c>20){c-=20;a/=Math.pow(10,c);a+=new
Array(c+1).join(W);if(b>0)a=a+aU+new
Array(b+1).join(W);return a}else
return a.toFixed(b)}}var
c,f=dQ(a),e=f.prec<0?6:f.prec;if(b<0||b===0&&1/b===Number.NEGATIVE_INFINITY){f.sign=-1;b=-b}if(Number.isNaN(b)){c=c7;f.filler=av}else if(!Number.isFinite(b)){c="inf";f.filler=av}else
switch(f.conv){case"e":var
c=b.toExponential(e),d=c.length;if(c.charAt(d-3)===db)c=c.slice(0,d-1)+W+c.slice(d-1);break;case"f":c=j(b,e);break;case"g":e=e?e:1;c=b.toExponential(e-1);var
i=c.indexOf(db),h=+c.slice(i+1);if(h<-4||b>=1e21||b.toFixed(0).length>e){var
d=i-1;while(c.charAt(d)===W)d--;if(c.charAt(d)===aU)d--;c=c.slice(0,d+1)+c.slice(i);d=c.length;if(c.charAt(d-3)===db)c=c.slice(0,d-1)+W+c.slice(d-1);break}else{var
g=e;if(h<0){g-=h+1;c=b.toFixed(g)}else
while(c=b.toFixed(g),c.length>e+1)g--;if(g){var
d=c.length-1;while(c.charAt(d)===W)d--;if(c.charAt(d)===aU)d--;c=c.slice(0,d+1)}}break}return dE(f,c)}function
ck(a,b){if(aB(a)===c1)return _(e+b);var
c=dQ(a);if(b<0)if(c.signedconv){c.sign=-1;b=-b}else
b>>>=0;var
d=b.toString(c.base);if(c.prec>=0){c.filler=av;var
f=c.prec-d.length;if(f>0)d=a$(f,W)+d}return dE(c,d)}var
mA=0;function
cl(){return mA++}function
j(a){if(dV(a))return a;return hd(a)}function
bD(){return typeof
c.process!=="undefined"&&typeof
c.process.versions!=="undefined"&&typeof
c.process.versions.node!=="undefined"}function
mS(){function
a(a){if(a.charAt(0)===aw)return[e,a.slice(1)];return}function
b(a){var
h=/^([a-zA-Z]:|[\\/]{2}[^\\/]+[\\/]+[^\\/]+)?([\\/])?([\s\S]*?)$/,b=h.exec(a),c=b[1]||e,f=c.length>0&&c.charAt(1)!==":";if(b[2]||f){var
d=b[1]||e,g=b[2]||e;return[d,a.slice(d.length+g.length)]}return}return bD()&&c.process&&c.process.platform?c.process.platform==="win32"?b:a:a}var
dW=mS();function
hc(a){return a.slice(-1)!==aw?a+aw:a}if(bD()&&c.process&&c.process.cwd)var
by=c.process.cwd().replace(/\\/g,aw);else
var
by="/static";by=hc(by);function
mr(a){a=j(a);if(!dW(a))a=by+a;var
e=dW(a),d=e[1].split(/[/\\]/),b=[];for(var
c=0;c<d.length;c++)switch(d[c]){case"..":if(b.length>1)b.pop();break;case".":break;case"":break;default:b.push(d[c]);break}b.unshift(e[0]);b.orig=a;return b}function
mN(a){for(var
g=e,c=g,b,i,d=0,h=a.length;d<h;d++){b=a.charCodeAt(d);if(b<ab){for(var
f=d+1;f<h&&(b=a.charCodeAt(f))<ab;f++);if(f-d>fl){c.slice(0,1);g+=c;c=e;g+=a.slice(d,f)}else
c+=a.slice(d,f);if(f===h)break;d=f}if(b<fF){c+=String.fromCharCode(0xc0|b>>6);c+=String.fromCharCode(ab|b&aG)}else if(b<0xd800||b>=ft)c+=String.fromCharCode(gt|b>>12,ab|b>>6&aG,ab|b&aG);else if(b>=0xdbff||d+1===h||(i=a.charCodeAt(d+1))<e5||i>ft)c+="\xef\xbf\xbd";else{d++;b=(b<<10)+i-0x35fdc00;c+=String.fromCharCode(fK|b>>18,ab|b>>12&aG,ab|b>>6&aG,ab|b&aG)}if(c.length>bq){c.slice(0,1);g+=c;c=e}}return g+c}function
E(a){return dV(a)?_(a):_(mN(a))}var
mT=["E2BIG","EACCES","EAGAIN",c0,"EBUSY","ECHILD","EDEADLK","EDOM",gs,"EFAULT","EFBIG","EINTR","EINVAL","EIO","EISDIR","EMFILE","EMLINK","ENAMETOOLONG","ENFILE","ENODEV",da,"ENOEXEC","ENOLCK","ENOMEM","ENOSPC","ENOSYS",dg,f_,"ENOTTY","ENXIO","EPERM","EPIPE","ERANGE","EROFS","ESPIPE","ESRCH","EXDEV","EWOULDBLOCK","EINPROGRESS","EALREADY","ENOTSOCK","EDESTADDRREQ","EMSGSIZE","EPROTOTYPE","ENOPROTOOPT","EPROTONOSUPPORT","ESOCKTNOSUPPORT","EOPNOTSUPP","EPFNOSUPPORT","EAFNOSUPPORT","EADDRINUSE","EADDRNOTAVAIL","ENETDOWN","ENETUNREACH","ENETRESET","ECONNABORTED","ECONNRESET","ENOBUFS","EISCONN","ENOTCONN","ESHUTDOWN","ETOOMANYREFS","ETIMEDOUT","ECONNREFUSED","EHOSTDOWN","EHOSTUNREACH","ELOOP","EOVERFLOW"];function
aD(a,b,c,d){var
f=mT.indexOf(a);if(f<0){if(d==null)d=-9999;f=[0,d]}var
g=[f,E(b||e),E(c||e)];return g}var
g$={};function
aK(a){return g$[a]}function
aC(a,b){throw i([0,a].concat(b))}function
dN(a){return a
instanceof
ay}function
dO(a){return typeof
a==="string"&&!/[^\x00-\xff]/.test(a)}function
dC(a){if(!(a
instanceof
Uint8Array))a=new
Uint8Array(a);return new
ay(4,a,a.length)}function
k(a){dR(I.Sys_error,a)}function
hb(a){k(a+bs)}function
dU(a){if(a.t!==4)ci(a);return a.c}function
ai(a){return a.l}function
gD(){}function
M(a){this.data=a}M.prototype=new
gD();M.prototype.constructor=M;M.prototype.truncate=function(a){var
b=this.data;this.data=A(a|0);aI(b,0,this.data,0,a)};M.prototype.length=function(){return ai(this.data)};M.prototype.write=function(a,b,c,d){var
e=this.length();if(a+d>=e){var
f=A(a+d),g=this.data;this.data=f;aI(g,0,this.data,0,e)}aI(dC(b),c,this.data,a,d);return 0};M.prototype.read=function(a,b,c,d){var
e=this.length();if(a+d>=e)d=e-a;if(d){var
f=A(d|0);aI(this.data,a,f,0,d);b.set(dU(f),c)}return d};function
aV(a,b,c){this.file=b;this.name=a;this.flags=c}aV.prototype.err_closed=function(){k(this.name+fa)};aV.prototype.length=function(){if(this.file)return this.file.length();this.err_closed()};aV.prototype.write=function(a,b,c,d){if(this.file)return this.file.write(a,b,c,d);this.err_closed()};aV.prototype.read=function(a,b,c,d){if(this.file)return this.file.read(a,b,c,d);this.err_closed()};aV.prototype.close=function(){this.file=undefined};function
D(a,b){this.content={};this.root=a;this.lookupFun=b}D.prototype.nm=function(a){return this.root+a};D.prototype.create_dir_if_needed=function(a){var
d=a.split(aw),c=e;for(var
b=0;b<d.length-1;b++){c+=d[b]+aw;if(this.content[c])continue;this.content[c]=Symbol("directory")}};D.prototype.slash=function(a){return/\/$/.test(a)?a:a+aw};D.prototype.lookup=function(a){if(!this.content[a]&&this.lookupFun){var
b=this.lookupFun(_(this.root),_(a));if(b!==0){this.create_dir_if_needed(a);this.content[a]=new
M(ah(b[1]))}}};D.prototype.exists=function(a,b){if(a===e)return 1;var
c=this.slash(a);if(this.content[c])return 1;if(!b)this.lookup(a);return this.content[a]?1:0};D.prototype.isFile=function(a){return this.exists(a)&&!this.is_dir(a)?1:0};D.prototype.mkdir=function(a,b,c){var
f=c&&aK(cd);if(this.exists(a))if(f)aC(f,aD(gs,dq,this.nm(a)));else
k(a+": File exists");var
d=/^(.*)\/[^/]+/.exec(a);d=d&&d[1]||e;if(!this.exists(d))if(f)aC(f,aD(da,dq,this.nm(d)));else
k(d+bs);if(!this.is_dir(d))if(f)aC(f,aD(dg,dq,this.nm(d)));else
k(d+c3);this.create_dir_if_needed(this.slash(a))};D.prototype.rmdir=function(a,b){var
c=b&&aK(cd),d=a===e?e:this.slash(a),g=new
RegExp(fV+d+fx);if(!this.exists(a))if(c)aC(c,aD(da,c6,this.nm(a)));else
k(a+bs);if(!this.is_dir(a))if(c)aC(c,aD(dg,c6,this.nm(a)));else
k(a+c3);for(var
f
in
this.content)if(f.match(g))if(c)aC(c,aD(f_,c6,this.nm(a)));else
k(this.nm(a)+": Directory not empty");delete
this.content[d]};D.prototype.readdir=function(a){var
g=a===e?e:this.slash(a);if(!this.exists(a))k(a+bs);if(!this.is_dir(a))k(a+c3);var
h=new
RegExp(fV+g+fx),d={},c=[];for(var
f
in
this.content){var
b=f.match(h);if(b&&!d[b[1]]){d[b[1]]=true;c.push(b[1])}}return c};D.prototype.opendir=function(a,b){var
c=b&&aK(cd),d=this.readdir(a),e=false,f=0;return{readSync:function(){if(e)if(c)aC(c,aD(c0,gw,this.nm(a)));else
k(a+fs);if(f===d.length)return null;var
b=d[f];f++;return{name:b}},closeSync:function(){if(e)if(c)aC(c,aD(c0,gw,this.nm(a)));else
k(a+fs);e=true;d=[]}}};D.prototype.is_dir=function(a){if(a===e)return true;var
b=this.slash(a);return this.content[b]?1:0};D.prototype.unlink=function(a){if(!this.exists(a,true))k(a+bs);delete
this.content[a];return 0};D.prototype.open=function(a,b){var
c;if(b.rdonly&&b.wronly)k(this.nm(a)+du);if(b.text&&b.binary)k(this.nm(a)+c5);this.lookup(a);if(this.content[a]){if(this.is_dir(a))k(this.nm(a)+fc);if(b.create&&b.excl)k(this.nm(a)+c$);c=this.content[a];if(b.truncate)c.truncate()}else if(b.create){this.create_dir_if_needed(a);this.content[a]=new
M(A(0));c=this.content[a]}else
hb(this.nm(a));return new
aV(this.nm(a),c,b)};D.prototype.open=function(a,b){var
c;if(b.rdonly&&b.wronly)k(this.nm(a)+du);if(b.text&&b.binary)k(this.nm(a)+c5);this.lookup(a);if(this.content[a]){if(this.is_dir(a))k(this.nm(a)+fc);if(b.create&&b.excl)k(this.nm(a)+c$);c=this.content[a];if(b.truncate)c.truncate()}else if(b.create){this.create_dir_if_needed(a);this.content[a]=new
M(A(0));c=this.content[a]}else
hb(this.nm(a));return new
aV(this.nm(a),c,b)};D.prototype.register=function(a,b){var
c;if(this.content[a])k(this.nm(a)+c$);if(dN(b))c=new
M(b);if(dO(b))c=new
M(ah(b));else if(Array.isArray(b))c=new
M(dC(b));else if(typeof
b==="string")c=new
M(gL(b));else if(b.toString){var
d=ah(E(b.toString()));c=new
M(d)}if(c){this.create_dir_if_needed(a);this.content[a]=c}else
k(this.nm(a)+" : registering file with invalid content type")};D.prototype.constructor=D;function
l(a){return a.length}function
$(a,b){return a.charCodeAt(b)}function
mX(a){var
d=l(a),c=new
Uint8Array(d),b=0;for(;b<d;b++)c[b]=$(a,b);return c}function
ag(a,b){this.fs=require(dz);this.fd=a;this.flags=b}ag.prototype=new
gD();ag.prototype.constructor=ag;ag.prototype.truncate=function(a){try{this.fs.ftruncateSync(this.fd,a|0)}catch(f){k(f.toString())}};ag.prototype.length=function(){try{return this.fs.fstatSync(this.fd).size}catch(f){k(f.toString())}};ag.prototype.write=function(a,b,c,d){try{if(this.flags.isCharacterDevice)this.fs.writeSync(this.fd,b,c,d);else
this.fs.writeSync(this.fd,b,c,d,a)}catch(f){k(f.toString())}return 0};ag.prototype.read=function(a,b,c,d){try{if(this.flags.isCharacterDevice)var
e=this.fs.readSync(this.fd,b,c,d);else
var
e=this.fs.readSync(this.fd,b,c,d,a);return e}catch(f){k(f.toString())}};ag.prototype.close=function(){try{this.fs.closeSync(this.fd);return 0}catch(f){k(f.toString())}};function
b(a){this.fs=require(dz);this.root=a}b.prototype.nm=function(a){return this.root+a};b.prototype.exists=function(a){try{return this.fs.existsSync(this.nm(a))?1:0}catch(f){return 0}};b.prototype.isFile=function(a){try{return this.fs.statSync(this.nm(a)).isFile()?1:0}catch(f){k(f.toString())}};b.prototype.mkdir=function(a,b,c){try{this.fs.mkdirSync(this.nm(a),{mode:b});return 0}catch(f){this.raise_nodejs_error(f,c)}};b.prototype.rmdir=function(a,b){try{this.fs.rmdirSync(this.nm(a));return 0}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.readdir=function(a,b){try{return this.fs.readdirSync(this.nm(a))}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.is_dir=function(a){try{return this.fs.statSync(this.nm(a)).isDirectory()?1:0}catch(f){k(f.toString())}};b.prototype.unlink=function(a,b){try{this.fs.unlinkSync(this.nm(a));return 0}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.open=function(a,b,c){var
d=require("node:constants"),e=0;for(var
h
in
b)switch(h){case"rdonly":e|=d.O_RDONLY;break;case"wronly":e|=d.O_WRONLY;break;case"append":e|=d.O_WRONLY|d.O_APPEND;break;case"create":e|=d.O_CREAT;break;case"truncate":e|=d.O_TRUNC;break;case"excl":e|=d.O_EXCL;break;case"binary":e|=d.O_BINARY;break;case"text":e|=d.O_TEXT;break;case"nonblock":e|=d.O_NONBLOCK;break}try{var
f=this.fs.openSync(this.nm(a),e),g=this.fs.lstatSync(this.nm(a)).isCharacterDevice();b.isCharacterDevice=g;return new
ag(f,b)}catch(f){this.raise_nodejs_error(f,c)}};b.prototype.rename=function(a,b,c){try{this.fs.renameSync(this.nm(a),this.nm(b))}catch(f){this.raise_nodejs_error(f,c)}};b.prototype.stat=function(a,b){try{var
c=this.fs.statSync(this.nm(a));return this.stats_from_js(c)}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.lstat=function(a,b){try{var
c=this.fs.lstatSync(this.nm(a));return this.stats_from_js(c)}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.symlink=function(a,b,c,d){try{this.fs.symlinkSync(this.nm(b),this.nm(c),a?fw:"file");return 0}catch(f){this.raise_nodejs_error(f,d)}};b.prototype.readlink=function(a,b){try{var
c=this.fs.readlinkSync(this.nm(a),"utf8");return E(c)}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.opendir=function(a,b){try{return this.fs.opendirSync(this.nm(a))}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.raise_nodejs_error=function(a,b){var
c=aK(cd);if(b&&c){var
d=aD(a.code,a.syscall,a.path,a.errno);aC(c,d)}else
k(a.toString())};b.prototype.stats_from_js=function(a){var
b;if(a.isFile())b=0;else if(a.isDirectory())b=1;else if(a.isCharacterDevice())b=2;else if(a.isBlockDevice())b=3;else if(a.isSymbolicLink())b=4;else if(a.isFIFO())b=5;else if(a.isSocket())b=6;return[0,a.dev,a.ino,b,a.mode,a.nlink,a.uid,a.gid,a.rdev,a.size,a.atimeMs,a.mtimeMs,a.ctimeMs]};b.prototype.constructor=b;function
gT(a){var
b=dW(a);if(!b)return;return b[0]+aw}var
cp=gT(by)||Z("unable to compute caml_root"),ba=[];if(bD())ba.push({path:cp,device:new
b(cp)});else
ba.push({path:cp,device:new
D(cp)});ba.push({path:gd,device:new
D(gd)});function
hh(a){var
h=mr(a),a=h.join(aw),g=hc(a),d;for(var
f=0;f<ba.length;f++){var
c=ba[f];if(g.search(c.path)===0&&(!d||d.path.length<c.path.length))d={path:c.path,device:c.device,rest:a.slice(c.path.length,a.length)}}if(!d&&bD()){var
e=gT(a);if(e&&e.match(/^[a-zA-Z]:\/$/)){var
c={path:e,device:new
b(e)};ba.push(c);d={path:c.path,device:c.device,rest:a.slice(c.path.length,a.length)}}}if(d)return d;k("no device found for "+g)}function
lY(a,b){var
c=hh(a);if(!c.device.register)Z("cannot register file");c.device.register(c.rest,b);return 0}function
he(a,b){var
a=_(a),b=_(b);return lY(a,b)}function
l4(){var
b=c.jsoo_fs_tmp;if(b)for(var
a=0;a<b.length;a++)he(b[a].name,b[a].content);c.jsoo_create_file=he;c.jsoo_fs_tmp=[];return 0}function
dG(){return[0]}function
l6(a,b,c){if(!Number.isFinite(a)){if(Number.isNaN(a))return E(c7);return E(a>0?fO:"-infinity")}var
k=a===0&&1/a===Number.NEGATIVE_INFINITY?1:a>=0?0:1;if(k)a=-a;var
f=0;if(a===0);else if(a<1)while(a<1&&f>-1022){a*=2;f--}else
while(a>=2){a/=2;f++}var
l=f<0?e:a1,g=e;if(k)g=aT;else
switch(c){case
43:g=a1;break;case
32:g=av;break;default:break}if(b>=0&&b<13){var
i=Math.pow(2,b*4);a=Math.round(a*i)/i}var
d=a.toString(16);if(b>=0){var
j=d.indexOf(aU);if(j<0)d+=aU+a$(b,W);else{var
h=j+1+b;if(d.length<h)d+=a$(h-d.length,W);else
d=d.slice(0,h)}}return E(g+fu+d+"p"+l+f.toString(10))}function
X(a,b,c){return new
d(a,b,c)}function
mb(a){return+a.isZero()}function
bz(a){return new
d(a&an,a>>24&an,a>>31&af)}function
g2(a){return a.toInt()}function
ma(a){return+a.isNeg()}function
gX(a){return a.neg()}function
l_(a,b){var
c=dQ(a);if(c.signedconv&&ma(b)){c.sign=-1;b=gX(b)}var
d=e,i=bz(c.base),h="0123456789abcdef";do{var
g=b.udivmod(i);b=g.quotient;d=h.charAt(g2(g.modulus))+d}while(!mb(b));if(c.prec>=0){c.filler=av;var
f=c.prec-d.length;if(f>0)d=a$(f,W)+d}return dE(c,d)}function
g0(a,b){return a.or(b)}function
g1(a,b){return a.shift_right_unsigned(b)}function
me(a){return a.toFloat()}function
mk(a,b,c){return a.apply(b,g3(c))}function
g4(a){return!!a}function
mm(){var
b=console,c=["log","debug","info","warn",fb,"assert",fw,"dirxml","trace","group","groupCollapsed","groupEnd",fH,"timeEnd"];function
d(){}for(var
a=0;a<c.length;a++)if(!b[c[a]])b[c[a]]=d;return b}var
a6=ap;function
bA(a){return function(){var
d=arguments.length;if(d>0){var
c=new
Array(d);for(var
b=0;b<d;b++)c[b]=arguments[b]}else
c=[undefined];var
e=a6(a,c);return e
instanceof
Function?bA(e):e}}function
ml(a){return a.l>=0?a.l:a.l=a.length}function
mn(a){return function(){var
d=ml(a),c=new
Array(d);for(var
b=0;b<d;b++)c[b]=arguments[b];return a6(a,c)}}function
a9(a){return function(){var
e=arguments.length,c=new
Array(e+1);c[0]=this;for(var
b=0;b<e;b++)c[b+1]=arguments[b];var
d=a6(a,c);return d
instanceof
Function?bA(d):d}}function
mi(a){return a===245?1:0}var
mR=Math.log2&&Math.log2(1.1235582092889474e307)===1020;function
mP(a){if(mR)return Math.floor(Math.log2(a));var
b=0;if(a===0)return Number.NEGATIVE_INFINITY;if(a>=1)while(a>=2){a/=2;b++}else
while(a<1){a*=2;b--}return b}function
dH(a){var
b=new
Float32Array(1);b[0]=a;var
c=new
Int32Array(b.buffer);return c[0]|0}function
cm(a){if(!Number.isFinite(a)){if(Number.isNaN(a))return X(1,0,f0);return a>0?X(0,0,f0):X(0,0,0xfff0)}var
f=a===0&&1/a===Number.NEGATIVE_INFINITY?ae:a>=0?0:ae;if(f)a=-a;var
b=mP(a)+1023;if(b<=0){b=0;a/=Math.pow(2,-fk)}else{a/=Math.pow(2,b-f$);if(a<16){a*=2;b-=1}if(b===0)a/=2}var
d=Math.pow(2,24),c=a|0;a=(a-c)*d;var
e=a|0;a=(a-e)*d;var
g=a|0;c=c&de|f|b<<4;return X(g,e,c)}function
gK(a,b,c){a.write(32,b.dims.length);a.write(32,b.kind|b.layout<<8);if(b.caml_custom===b_)for(var
d=0;d<b.dims.length;d++)if(b.dims[d]<af)a.write(16,b.dims[d]);else{a.write(16,af);a.write(32,0);a.write(32,b.dims[d])}else
for(var
d=0;d<b.dims.length;d++)a.write(32,b.dims[d]);switch(b.kind){case
2:case
3:case
12:for(var
d=0;d<b.data.length;d++)a.write(8,b.data[d]);break;case
4:case
5:for(var
d=0;d<b.data.length;d++)a.write(16,b.data[d]);break;case
6:for(var
d=0;d<b.data.length;d++)a.write(32,b.data[d]);break;case
8:case
9:a.write(8,0);for(var
d=0;d<b.data.length;d++)a.write(32,b.data[d]);break;case
7:for(var
d=0;d<b.data.length/2;d++){var
f=a8(b.get(d));for(var
e=0;e<8;e++)a.write(8,f[e])}break;case
1:for(var
d=0;d<b.data.length;d++){var
f=a8(cm(b.get(d)));for(var
e=0;e<8;e++)a.write(8,f[e])}break;case
0:for(var
d=0;d<b.data.length;d++){var
f=dH(b.get(d));a.write(32,f)}break;case
10:for(var
d=0;d<b.data.length/2;d++){var
e=b.get(d);a.write(32,dH(e[1]));a.write(32,dH(e[2]))}break;case
11:for(var
d=0;d<b.data.length/2;d++){var
g=b.get(d),f=a8(cm(g[1]));for(var
e=0;e<8;e++)a.write(8,f[e]);var
f=a8(cm(g[2]));for(var
e=0;e<8;e++)a.write(8,f[e])}break}c[0]=(4+b.dims.length)*4;c[1]=(4+b.dims.length)*8}function
dI(a){var
b=new
Int32Array(1);b[0]=a;var
c=new
Float32Array(b.buffer);return c[0]}function
dJ(a){var
f=a.lo,g=a.mi,c=a.hi,d=(c&0x7fff)>>4;if(d===2047)return(f|g|c&de)===0?c&ae?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY:Number.NaN;var
e=Math.pow(2,-24),b=(f*e+g)*e+(c&de);if(d>0){b+=16;b*=Math.pow(2,d-f$)}else
b*=Math.pow(2,-fk);if(c&ae)b=-b;return b}function
gH(a,b,c){var
k=a.read32s();if(k<0||k>16)Z("input_value: wrong number of bigarray dimensions");var
r=a.read32s(),l=r&ao,q=r>>8&1,j=[];if(c===b_)for(var
d=0;d<k;d++){var
p=a.read16u();if(p===af){var
u=a.read32u(),v=a.read32u();if(u!==0)Z("input_value: bigarray dimension overflow in 32bit");p=v}j.push(p)}else
for(var
d=0;d<k;d++)j.push(a.read32u());var
f=cg(j),h=gG(l,f),i=dA(l,q,j,h);switch(l){case
2:for(var
d=0;d<f;d++)h[d]=a.read8s();break;case
3:case
12:for(var
d=0;d<f;d++)h[d]=a.read8u();break;case
4:for(var
d=0;d<f;d++)h[d]=a.read16s();break;case
5:for(var
d=0;d<f;d++)h[d]=a.read16u();break;case
6:for(var
d=0;d<f;d++)h[d]=a.read32s();break;case
8:case
9:var
t=a.read8u();if(t)Z("input_value: cannot read bigarray with 64-bit OCaml ints");for(var
d=0;d<f;d++)h[d]=a.read32s();break;case
7:var
g=new
Array(8);for(var
d=0;d<f;d++){for(var
e=0;e<8;e++)g[e]=a.read8u();var
s=a7(g);i.set(d,s)}break;case
1:var
g=new
Array(8);for(var
d=0;d<f;d++){for(var
e=0;e<8;e++)g[e]=a.read8u();var
m=dJ(a7(g));i.set(d,m)}break;case
0:for(var
d=0;d<f;d++){var
m=dI(a.read32s());i.set(d,m)}break;case
10:for(var
d=0;d<f;d++){var
o=dI(a.read32s()),n=dI(a.read32s());i.set(d,[b4,o,n])}break;case
11:var
g=new
Array(8);for(var
d=0;d<f;d++){for(var
e=0;e<8;e++)g[e]=a.read8u();var
o=dJ(a7(g));for(var
e=0;e<8;e++)g[e]=a.read8u();var
n=dJ(a7(g));i.set(d,[b4,o,n])}break}b[0]=(4+k)*4;return dA(l,q,j,h)}function
gF(a,b,c){return a.compare(b,c)}function
g_(a,b){return Math.imul(a,b)}function
aA(a,b){b=g_(b,0xcc9e2d51|0);b=b<<15|b>>>32-15;b=g_(b,0x1b873593);a^=b;a=a<<13|a>>>32-13;return(a+(a<<2)|0)+(0xe6546b64|0)|0}function
l5(a,b){a=aA(a,dL(b));a=aA(a,dK(b));return a}function
gU(a,b){return l5(a,cm(b))}function
gJ(a){var
c=cg(a.dims),d=0;switch(a.kind){case
2:case
3:case
12:if(c>gm)c=gm;var
e=0,b=0;for(b=0;b+4<=a.data.length;b+=4){e=a.data[b+0]|a.data[b+1]<<8|a.data[b+2]<<16|a.data[b+3]<<24;d=aA(d,e)}e=0;switch(c&3){case
3:e=a.data[b+2]<<16;case
2:e|=a.data[b+1]<<8;case
1:e|=a.data[b+0];d=aA(d,e)}break;case
4:case
5:if(c>bp)c=bp;var
e=0,b=0;for(b=0;b+2<=a.data.length;b+=2){e=a.data[b+0]|a.data[b+1]<<16;d=aA(d,e)}if((c&1)!==0)d=aA(d,a.data[b]);break;case
6:if(c>64)c=64;for(var
b=0;b<c;b++)d=aA(d,a.data[b]);break;case
8:case
9:if(c>64)c=64;for(var
b=0;b<c;b++)d=aA(d,a.data[b]);break;case
7:if(c>32)c=32;c*=2;for(var
b=0;b<c;b++)d=aA(d,a.data[b]);break;case
10:c*=2;case
0:if(c>64)c=64;for(var
b=0;b<c;b++)d=gU(d,a.data[b]);break;case
11:c*=2;case
1:if(c>32)c=32;for(var
b=0;b<c;b++)d=gU(d,a.data[b]);break}return d}function
l7(a,b){b[0]=4;return a.read32s()}function
mz(a,b){switch(a.read8u()){case
1:b[0]=4;return a.read32s();case
2:Z("input_value: native integer value too large");break;default:Z("input_value: ill-formed native integer")}}function
mf(a,b){var
d=new
Array(8);for(var
c=0;c<8;c++)d[c]=a.read8u();b[0]=8;return a7(d)}function
mc(a,b,c){var
e=a8(b);for(var
d=0;d<8;d++)a.write(8,e[d]);c[0]=8;c[1]=8}function
l8(a,b,c){return a.compare(b)}function
l$(a){return a.lo32()^a.hi32()}var
gP={_j:{deserialize:mf,serialize:mc,fixed_length:8,compare:l8,hash:l$},_i:{deserialize:l7,fixed_length:4},_n:{deserialize:mz,fixed_length:4},_bigarray:{deserialize:function(a,b){return gH(a,b,"_bigarray")},serialize:gK,compare:gF,hash:gJ},_bigarr02:{deserialize:function(a,b){return gH(a,b,b_)},serialize:gK,compare:gF,hash:gJ}};function
dD(a){return gP[a.caml_custom]&&gP[a.caml_custom].compare}function
gN(a,b,c,d){var
f=dD(b);if(f){var
e=c>0?f(b,a,d):f(a,b,d);if(d&&Number.isNaN(e))return c;if(Number.isNaN(+e))return+e;if((e|0)!==0)return e|0}return c}function
gO(a){if(typeof
a==="number")return br;else if(dN(a))return b3;else if(dO(a))return 1252;else if(Array.isArray(a)&&a[0]===a[0]>>>0&&a[0]<=fC){var
b=a[0]|0;return b===b4?0:b}else if(a
instanceof
String)return e9;else if(typeof
a==="string")return e9;else if(a
instanceof
Number)return br;else if(a&&a.caml_custom)return dm;else if(a&&a.compare)return 1256;else if(typeof
a==="function")return 1247;else if(typeof
a==="symbol")return 1251;return 1001}function
mh(a,b){if(a<b)return-1;if(a===b)return 0;return 1}function
mV(a,b){return a<b?-1:a>b?1:0}function
lT(a,b){a.t&6&&cj(a);b.t&6&&cj(b);return a.c<b.c?-1:a.c>b.c?1:0}function
gM(a,b,c){var
f=[];for(;;){if(!(c&&a===b)){var
e=gO(a);if(e===e8){a=a[1];continue}var
g=gO(b);if(g===e8){b=b[1];continue}if(e!==g){if(e===br){if(g===dm)return gN(a,b,-1,c);return-1}if(g===br){if(e===dm)return gN(b,a,1,c);return 1}return e<g?-1:1}switch(e){case
247:w(dr);break;case
248:var
d=mh(a[2],b[2])|0;if(d!==0)return d;break;case
249:w(dr);break;case
250:w("equal: got Forward_tag, should not happen");break;case
251:w("equal: abstract value");break;case
252:if(a!==b){var
d=lT(a,b)|0;if(d!==0)return d}break;case
253:w("equal: got Double_tag, should not happen");break;case
254:w("equal: got Double_array_tag, should not happen");break;case
255:w("equal: got Custom_tag, should not happen");break;case
1247:w(dr);break;case
1255:var
i=dD(a);if(i!==dD(b))return a.caml_custom<b.caml_custom?-1:1;if(!i)w("compare: abstract value");var
d=i(a,b,c);if(Number.isNaN(d))return c?-1:d;if(d!==(d|0))return-1;if(d!==0)return d|0;break;case
1256:var
d=a.compare(b,c);if(Number.isNaN(d))return c?-1:d;if(d!==(d|0))return-1;if(d!==0)return d|0;break;case
1000:a=+a;b=+b;if(a<b)return-1;if(a>b)return 1;if(a!==b){if(!c)return Number.NaN;if(!Number.isNaN(a))return 1;if(!Number.isNaN(b))return-1}break;case
1001:if(a<b)return-1;if(a>b)return 1;if(a!==b)return c?1:Number.NaN;break;case
1251:if(a!==b)return c?1:Number.NaN;break;case
1252:var
a=aB(a),b=aB(b);if(a!==b){if(a<b)return-1;if(a>b)return 1}break;case
12520:var
a=a.toString(),b=b.toString();if(a!==b){if(a<b)return-1;if(a>b)return 1}break;default:if(mi(e)){w("compare: continuation value");break}if(a.length!==b.length)return a.length<b.length?-1:1;if(a.length>1)f.push(a,b,1);break}}if(f.length===0)return 0;var
h=f.pop();b=f.pop();a=f.pop();if(h+1<a.length)f.push(a,b,h+1);a=a[h];b=b[h]}}function
mo(a,b){return+(gM(a,b,false)<=0)}function
gV(a,b){return a.add(b)}function
gW(a,b){return a.mul(b)}function
dM(a,b){return a.ucompare(b)<0}function
mB(a){var
b=0,f=l(a),d=10,e=1,c=1;if(f>0)switch($(a,b)){case
45:b++;e=-1;break;case
43:b++;e=1;break}if(b+1<f&&$(a,b)===48)switch($(a,b+1)){case
120:case
88:c=0;d=16;b+=2;break;case
111:case
79:c=0;d=8;b+=2;break;case
98:case
66:c=0;d=2;b+=2;break;case
117:case
85:c=0;b+=2;break}return[b,e,d,c]}function
ha(a){if(a>=48&&a<=57)return a-48;if(a>=65&&a<=90)return a-55;if(a>=97&&a<=122)return a-87;return-1}function
gY(a){var
g=mB(a),f=g[0],j=g[1],h=g[2],k=g[3],i=bz(h),m=new
d(an,0xfffffff,af).udivmod(i).quotient,e=$(a,f),b=ha(e);if(b<0||b>=h)Z(bu);var
c=bz(b);for(;;){f++;e=$(a,f);if(e===95)continue;b=ha(e);if(b<0||b>=h)break;if(dM(m,c))Z(bu);b=bz(b);c=gV(gW(i,c),b);if(dM(c,b))Z(bu)}if(f!==l(a))Z(bu);if(k&&dM(new
d(0,0,ae),c))Z(bu);if(j<0)c=gX(c);return c}var
mq=gY(E("0xdaba0b6eb09322e3")),mp=gY(E("0xd1342543de82ef95"));function
lR(a,b){return a.get(a.offset(b))}function
mg(a,b){return a.xor(b)}function
md(a,b){return a.shift_left(b)}function
a_(a){function
o(a,b){return md(a,b)}function
h(a,b){return g1(a,b)}function
s(a,b){return g0(a,b)}function
f(a,b){return mg(a,b)}function
k(a,b){return gV(a,b)}function
i(a,b){return gW(a,b)}function
m(a,b){return s(o(a,b),h(a,64-b))}function
g(a,b){return lR(a,b)}function
j(a,b,c){return bx(a,b,c)}var
q=mp,l=mq,b,d,c,e=a,r=g(e,0),n=g(e,1),p=g(e,2),t=g(e,3);b=k(n,p);b=i(f(b,h(b,32)),l);b=i(f(b,h(b,32)),l);b=f(b,h(b,32));j(e,1,k(i(n,q),r));var
d=p,c=t;c=f(c,d);d=m(d,24);d=f(f(d,c),o(c,16));c=m(c,37);j(e,2,d);j(e,3,c);return b}function
lN(a,b){if(a<0)bw();var
a=a+1|0,c=new
Array(a);c[0]=0;for(var
d=1;d<a;d++)c[d]=b;return c}function
g5(a,b){return lN(a,b)}function
lK(){var
a=new
ArrayBuffer(64),b=new
Uint32Array(a),c=new
Uint8Array(a);return{len:0,w:new
Uint32Array([0x67452301,0xefcdab89,0x98badcfe,0x10325476]),b32:b,b8:c}}var
ce=function(){function
k(a,b){return a+b|0}function
l(a,b,c,d,e,f){b=k(k(b,a),k(d,f));return k(b<<e|b>>>32-e,c)}function
g(a,b,c,d,e,f,g){return l(b&c|~b&d,a,b,e,f,g)}function
h(a,b,c,d,e,f,g){return l(b&d|c&~d,a,b,e,f,g)}function
i(a,b,c,d,e,f,g){return l(b^c^d,a,b,e,f,g)}function
j(a,b,c,d,e,f,g){return l(c^(b|~d),a,b,e,f,g)}return function(a,b){var
c=a[0],d=a[1],e=a[2],f=a[3];c=g(c,d,e,f,b[0],7,0xd76aa478);f=g(f,c,d,e,b[1],12,0xe8c7b756);e=g(e,f,c,d,b[2],17,0x242070db);d=g(d,e,f,c,b[3],22,0xc1bdceee);c=g(c,d,e,f,b[4],7,0xf57c0faf);f=g(f,c,d,e,b[5],12,0x4787c62a);e=g(e,f,c,d,b[6],17,0xa8304613);d=g(d,e,f,c,b[7],22,0xfd469501);c=g(c,d,e,f,b[8],7,0x698098d8);f=g(f,c,d,e,b[9],12,0x8b44f7af);e=g(e,f,c,d,b[10],17,0xffff5bb1);d=g(d,e,f,c,b[11],22,0x895cd7be);c=g(c,d,e,f,b[12],7,0x6b901122);f=g(f,c,d,e,b[13],12,0xfd987193);e=g(e,f,c,d,b[14],17,0xa679438e);d=g(d,e,f,c,b[15],22,0x49b40821);c=h(c,d,e,f,b[1],5,0xf61e2562);f=h(f,c,d,e,b[6],9,0xc040b340);e=h(e,f,c,d,b[11],14,0x265e5a51);d=h(d,e,f,c,b[0],20,0xe9b6c7aa);c=h(c,d,e,f,b[5],5,0xd62f105d);f=h(f,c,d,e,b[10],9,0x02441453);e=h(e,f,c,d,b[15],14,0xd8a1e681);d=h(d,e,f,c,b[4],20,0xe7d3fbc8);c=h(c,d,e,f,b[9],5,0x21e1cde6);f=h(f,c,d,e,b[14],9,0xc33707d6);e=h(e,f,c,d,b[3],14,0xf4d50d87);d=h(d,e,f,c,b[8],20,0x455a14ed);c=h(c,d,e,f,b[13],5,0xa9e3e905);f=h(f,c,d,e,b[2],9,0xfcefa3f8);e=h(e,f,c,d,b[7],14,0x676f02d9);d=h(d,e,f,c,b[12],20,0x8d2a4c8a);c=i(c,d,e,f,b[5],4,0xfffa3942);f=i(f,c,d,e,b[8],11,0x8771f681);e=i(e,f,c,d,b[11],16,0x6d9d6122);d=i(d,e,f,c,b[14],23,0xfde5380c);c=i(c,d,e,f,b[1],4,0xa4beea44);f=i(f,c,d,e,b[4],11,0x4bdecfa9);e=i(e,f,c,d,b[7],16,0xf6bb4b60);d=i(d,e,f,c,b[10],23,0xbebfbc70);c=i(c,d,e,f,b[13],4,0x289b7ec6);f=i(f,c,d,e,b[0],11,0xeaa127fa);e=i(e,f,c,d,b[3],16,0xd4ef3085);d=i(d,e,f,c,b[6],23,0x04881d05);c=i(c,d,e,f,b[9],4,0xd9d4d039);f=i(f,c,d,e,b[12],11,0xe6db99e5);e=i(e,f,c,d,b[15],16,0x1fa27cf8);d=i(d,e,f,c,b[2],23,0xc4ac5665);c=j(c,d,e,f,b[0],6,0xf4292244);f=j(f,c,d,e,b[7],10,0x432aff97);e=j(e,f,c,d,b[14],15,0xab9423a7);d=j(d,e,f,c,b[5],21,0xfc93a039);c=j(c,d,e,f,b[12],6,0x655b59c3);f=j(f,c,d,e,b[3],10,0x8f0ccc92);e=j(e,f,c,d,b[10],15,0xffeff47d);d=j(d,e,f,c,b[1],21,0x85845dd1);c=j(c,d,e,f,b[8],6,0x6fa87e4f);f=j(f,c,d,e,b[15],10,0xfe2ce6e0);e=j(e,f,c,d,b[6],15,0xa3014314);d=j(d,e,f,c,b[13],21,0x4e0811a1);c=j(c,d,e,f,b[4],6,0xf7537e82);f=j(f,c,d,e,b[11],10,0xbd3af235);e=j(e,f,c,d,b[2],15,0x2ad7d2bb);d=j(d,e,f,c,b[9],21,0xeb86d391);a[0]=k(c,a[0]);a[1]=k(d,a[1]);a[2]=k(e,a[2]);a[3]=k(f,a[3])}}();function
lL(a,b,c){var
e=a.len&aG,d=0;a.len+=c;if(e){var
f=64-e;if(c<f){a.b8.set(b.subarray(0,c),e);return}a.b8.set(b.subarray(0,f),e);ce(a.w,a.b32);c-=f;d+=f}while(c>=64){a.b8.set(b.subarray(d,d+64),0);ce(a.w,a.b32);c-=64;d+=64}if(c)a.b8.set(b.subarray(d,d+c),0)}function
lJ(a){var
c=a.len&aG;a.b8[c]=ab;c++;if(c>56){for(var
b=c;b<64;b++)a.b8[b]=0;ce(a.w,a.b32);for(var
b=0;b<56;b++)a.b8[b]=0}else
for(var
b=c;b<56;b++)a.b8[b]=0;a.b32[14]=a.len<<3;a.b32[15]=a.len>>29&0x1fffffff;ce(a.w,a.b32);var
e=new
Uint8Array(16);for(var
d=0;d<4;d++)for(var
b=0;b<4;b++)e[d*4+b]=a.w[d]>>8*b&ao;return e}function
mH(a){return _(bB(a,0,a.length))}function
ms(a,b,c){var
d=lK(),e=dU(a);lL(d,e.subarray(b,b+c),c);return mH(lJ(d))}function
mt(a,b,c){return ms(ah(a),b,c)}function
mu(){return 0}var
aJ=new
Array();function
g6(a){return aJ[a]}function
aq(a){var
b=g6(a);if(!b.opened)k("Cannot flush a closed channel");if(!b.buffer||b.buffer_curr===0)return 0;if(b.output)b.output(bB(b.buffer,0,b.buffer_curr));else
b.file.write(b.offset,b.buffer,0,b.buffer_curr);b.offset+=b.buffer_curr;b.buffer_curr=0;return 0}function
mK(a,b){if(b.name)try{var
d=require(dz),c=d.openSync(b.name,"rs");return new
ag(c,b)}catch(f){}return new
ag(a,b)}var
cq=new
Array(3);function
bv(a,b){M.call(this,A(0));this.log=function(a){return 0};if(a===1&&typeof
console.log==="function")this.log=console.log;else if(a===2&&typeof
console.error==="function")this.log=console.error;else if(typeof
console.log==="function")this.log=console.log;this.flags=b}bv.prototype.length=function(){return 0};bv.prototype.write=function(a,b,c,d){if(this.log){if(d>0&&c>=0&&c+d<=b.length&&b[c+d-1]===10)d--;var
e=A(d);aI(dC(b),c,e,0,d);this.log(e.toUtf16());return 0}k(this.fd+fa)};bv.prototype.read=function(a,b,c,d){k(this.fd+": file descriptor is write only")};bv.prototype.close=function(){this.log=undefined};function
cr(a,b){if(b===undefined)b=cq.length;cq[b]=a;return b|0}function
mW(a,b,c){var
d={};while(b){switch(b[1]){case
0:d.rdonly=1;break;case
1:d.wronly=1;break;case
2:d.append=1;break;case
3:d.create=1;break;case
4:d.truncate=1;break;case
5:d.excl=1;break;case
6:d.binary=1;break;case
7:d.text=1;break;case
8:d.nonblock=1;break}b=b[2]}if(d.rdonly&&d.wronly)k(aB(a)+du);if(d.text&&d.binary)k(aB(a)+c5);var
e=hh(a),f=e.device.open(e.rest,d);return cr(f,undefined)}(function(){function
a(a,b){return bD()?mK(a,b):new
bv(a,b)}cr(a(0,{rdonly:1,altname:"/dev/stdin",isCharacterDevice:true}),0);cr(a(1,{buffered:2,wronly:1,isCharacterDevice:true}),1);cr(a(2,{buffered:2,wronly:1,isCharacterDevice:true}),2)}());function
mv(a){var
b=cq[a];if(b.flags.wronly)k(fN+a+" is writeonly");var
d=null,c={file:b,offset:b.flags.append?b.length():0,fd:a,opened:true,out:false,buffer_curr:0,buffer_max:0,buffer:new
Uint8Array(fP),refill:d};aJ[c.fd]=c;return c.fd}function
g7(a){var
b=cq[a];if(b.flags.rdonly)k(fN+a+" is readonly");var
d=b.flags.buffered!==undefined?b.flags.buffered:1,c={file:b,offset:b.flags.append?b.length():0,fd:a,opened:true,out:true,buffer_curr:0,buffer:new
Uint8Array(fP),buffered:d};aJ[c.fd]=c;return c.fd}function
mw(){var
b=0;for(var
a=0;a<aJ.length;a++)if(aJ[a]&&aJ[a].opened&&aJ[a].out)b=[0,aJ[a].fd,b];return b}function
my(a,b,c,d){var
e=g6(a);if(!e.opened)k("Cannot output to a closed channel");b=b.subarray(c,c+d);if(e.buffer_curr+b.length>e.buffer.length){var
g=new
Uint8Array(e.buffer_curr+b.length);g.set(e.buffer);e.buffer=g}switch(e.buffered){case
0:e.buffer.set(b,e.buffer_curr);e.buffer_curr+=b.length;aq(a);break;case
1:e.buffer.set(b,e.buffer_curr);e.buffer_curr+=b.length;if(e.buffer_curr>=e.buffer.length)aq(a);break;case
2:var
f=b.lastIndexOf(10);if(f<0){e.buffer.set(b,e.buffer_curr);e.buffer_curr+=b.length;if(e.buffer_curr>=e.buffer.length)aq(a)}else{e.buffer.set(b.subarray(0,f+1),e.buffer_curr);e.buffer_curr+=f+1;aq(a);e.buffer.set(b.subarray(f+1),e.buffer_curr);e.buffer_curr+=b.length-f-1}break}return 0}function
mx(a,b,c,d){var
b=dU(b);return my(a,b,c,d)}function
g8(a,b,c,d){return mx(a,ah(b),c,d)}function
dP(a,b){var
c=_(String.fromCharCode(b));g8(a,c,0,1);return 0}function
g9(a,b){if(b===0)dS();return a%b}function
aL(a,b){return+(gM(a,b,false)!==0)}function
cn(a){if(Array.isArray(a)&&a[0]===a[0]>>>0)return a[0];else if(dN(a))return b3;else if(dO(a))return b3;else if(a
instanceof
Function||typeof
a==="function")return 247;else if(a&&a.caml_custom)return fC;else
return br}var
hg=undefined;function
lS(a){var
d={},c=-1;if(a)for(var
b=1;b<a.length;b++){var
e=a[b][2];c=Math.max(c,e);d[j(a[b][1])]=e}d.next_idx=c+1;return d}function
ac(a,b,c){if(c){var
d=c;if(hg)a=a6(hg,[d]);else if(I.symbols){if(!I.symidx)I.symidx=lS(I.symbols);var
e=I.symidx[d];if(e>=0)a=e;else{var
a=I.symidx.next_idx++;I.symidx[d]=a}}}I[a+1]=b;if(c)I[c]=b}function
dT(a,b){g$[aB(a)]=b;return 0}function
mG(){w(c2)}function
R(a,b){if(b>>>0>=l(a))mG();return $(a,b)}function
N(a){a.t&6&&cj(a);return _(a.c)}function
mI(){return 0x7fffffff/4|0}function
mJ(a){if(c.quit)c.quit(a);if(c.process&&c.process.exit)c.process.exit(a);w("Function 'exit' not implemented")}function
mL(){if(c.crypto)if(c.crypto.getRandomValues){var
a=c.crypto.getRandomValues(new
Int32Array(4));return[0,a[0],a[1],a[2],a[3]]}else if(c.crypto.randomBytes){var
a=new
Int32Array(c.crypto.randomBytes(16).buffer);return[0,a[0],a[1],a[2],a[3]]}var
b=new
Date().getTime(),d=b^0xffffffff*Math.random();return[0,d]}function
bC(a){var
b=1;while(a&&a.joo_tramp){a=a.joo_tramp.apply(null,a.joo_args);b++}return a}function
p(a,b){return{joo_tramp:a,joo_args:b}}function
mM(a,b){if(b.fun){a.fun=b.fun;return 0}if(typeof
b==="function"){a.fun=b;return 0}var
c=b.length;while(c--)a[c]=b[c];return 0}function
s(a){{if(Array.isArray(a))return a;var
b;if(c.RangeError&&a
instanceof
c.RangeError&&a.message&&a.message.match(/maximum call stack/i))b=I.Stack_overflow;else if(c.InternalError&&a
instanceof
c.InternalError&&a.message&&a.message.match(/too much recursion/i))b=I.Stack_overflow;else if(a
instanceof
c.Error&&aK(di))b=[0,aK(di),a];else
b=[0,I.Failure,E(String(a))];if(a
instanceof
c.Error)b.js_error=a;return b}}function
mj(a){switch(a[2]){case-8:case-11:case-12:return 1;default:return 0}}function
l3(a){var
b=e;if(a[0]===0){b+=a[1][1];if(a.length===3&&a[2][0]===0&&mj(a[1]))var
f=a[2],g=1;else
var
g=2,f=a;b+="(";for(var
d=g;d<f.length;d++){if(d>g)b+=ad;var
c=f[d];if(typeof
c==="number")b+=c.toString();else if(c
instanceof
ay)b+=b2+c.toString()+b2;else if(typeof
c==="string")b+=b2+c.toString()+b2;else
b+=gf}b+=")"}else if(a[0]===y)b+=a[1];return b}function
gS(a){if(Array.isArray(a)&&(a[0]===0||a[0]===y)){var
c=aK(gj);if(c)a6(c,[a,false]);else{var
d=l3(a),b=aK(fQ);if(b)a6(b,[0]);console.error(b7+d);if(a.js_error)throw a.js_error}}else
throw a}function
mF(){var
d=c.process;if(d&&d.on)d.on("uncaughtException",function(a,b){gS(a);d.exit(2)});else if(c.addEventListener)c.addEventListener(fb,function(a){if(a.error)gS(a.error)})}mF();function
g(a,b){return(a.l>=0?a.l:a.l=a.length)===1?a(b):ap(a,[b])}function
V(a,b,c){return(a.l>=0?a.l:a.l=a.length)===2?a(b,c):ap(a,[b,c])}function
b1(a,b,c,d){return(a.l>=0?a.l:a.l=a.length)===3?a(b,c,d):ap(a,[b,c,d])}function
e4(a,b,c,d,e){return(a.l>=0?a.l:a.l=a.length)===4?a(b,c,d,e):ap(a,[b,c,d,e])}function
b0(a,b,c,d,e,f){return(a.l>=0?a.l:a.l=a.length)===5?a(b,c,d,e,f):ap(a,[b,c,d,e,f])}function
lI(a,b,c,d,e,f,g){return(a.l>=0?a.l:a.l=a.length)===6?a(b,c,d,e,f,g):ap(a,[b,c,d,e,f,g])}function
lH(a,b,c,d,e,f,g,h){return(a.l>=0?a.l:a.l=a.length)===7?a(b,c,d,e,f,g,h):ap(a,[b,c,d,e,f,g,h])}var
mU=0;l4();var
cu=[y,gA,-1],d0=[y,fI,-2],cs=[y,dy,-3],dX=[y,fo,-4],ct=[y,fr,-7],dY=[y,gv,-8],dZ=[y,f9,-9],v=[y,gc,-11],d1=[y,gq,-12],lA=[4,0,0,0,[12,45,[4,0,0,0,0]]],cD=[0,[11,'File "',[2,0,[11,'", line ',[4,0,0,0,[11,gy,[4,0,0,0,[12,45,[4,0,0,0,[11,cc,[2,0,0]]]]]]]]]],'File "%s", line %d, characters %d-%d: %s'],eC="wakeup",lB=[11,ad,[8,[0,0,0],0,[0,1],[12,41,0]]],lC=[0,1],lD=[0,0,0],lE=[12,41,0],lF=[0,1],lG=[0,0,0];ac(11,d1,gq);ac(10,v,gc);ac(9,[y,gg,-10],gg);ac(8,dZ,f9);ac(7,dY,gv);ac(6,ct,fr);ac(5,[y,fE,-6],fE);ac(4,[y,fY,-5],fY);ac(3,dX,fo);ac(2,cs,dy);ac(1,d0,fI);ac(0,cu,gA);function
t(a){if(typeof
a==="number")return 0;switch(a[0]){case
0:return[0,t(a[1])];case
1:return[1,t(a[1])];case
2:return[2,t(a[1])];case
3:return[3,t(a[1])];case
4:return[4,t(a[1])];case
5:return[5,t(a[1])];case
6:return[6,t(a[1])];case
7:return[7,t(a[1])];case
8:var
c=a[1];return[8,c,t(a[2])];case
9:var
b=a[1];return[9,b,b,t(a[3])];case
10:return[10,t(a[1])];case
11:return[11,t(a[1])];case
12:return[12,t(a[1])];case
13:return[13,t(a[1])];default:return[14,t(a[1])]}}function
K(a,b){if(typeof
a==="number")return b;switch(a[0]){case
0:return[0,K(a[1],b)];case
1:return[1,K(a[1],b)];case
2:return[2,K(a[1],b)];case
3:return[3,K(a[1],b)];case
4:return[4,K(a[1],b)];case
5:return[5,K(a[1],b)];case
6:return[6,K(a[1],b)];case
7:return[7,K(a[1],b)];case
8:var
c=a[1];return[8,c,K(a[2],b)];case
9:var
d=a[2],e=a[1];return[9,e,d,K(a[3],b)];case
10:return[10,K(a[1],b)];case
11:return[11,K(a[1],b)];case
12:return[12,K(a[1],b)];case
13:return[13,K(a[1],b)];default:return[14,K(a[1],b)]}}function
r(a,b){if(typeof
a==="number")return b;switch(a[0]){case
0:return[0,r(a[1],b)];case
1:return[1,r(a[1],b)];case
2:var
c=a[1];return[2,c,r(a[2],b)];case
3:var
d=a[1];return[3,d,r(a[2],b)];case
4:var
e=a[3],f=a[2],g=a[1];return[4,g,f,e,r(a[4],b)];case
5:var
h=a[3],i=a[2],j=a[1];return[5,j,i,h,r(a[4],b)];case
6:var
k=a[3],l=a[2],m=a[1];return[6,m,l,k,r(a[4],b)];case
7:var
n=a[3],o=a[2],p=a[1];return[7,p,o,n,r(a[4],b)];case
8:var
q=a[3],s=a[2],t=a[1];return[8,t,s,q,r(a[4],b)];case
9:var
u=a[1];return[9,u,r(a[2],b)];case
10:return[10,r(a[1],b)];case
11:var
v=a[1];return[11,v,r(a[2],b)];case
12:var
w=a[1];return[12,w,r(a[2],b)];case
13:var
x=a[2],y=a[1];return[13,y,x,r(a[3],b)];case
14:var
z=a[2],A=a[1];return[14,A,z,r(a[3],b)];case
15:return[15,r(a[1],b)];case
16:return[16,r(a[1],b)];case
17:var
B=a[1];return[17,B,r(a[2],b)];case
18:var
C=a[1];return[18,C,r(a[2],b)];case
19:return[19,r(a[1],b)];case
20:var
D=a[2],E=a[1];return[20,E,D,r(a[3],b)];case
21:var
F=a[1];return[21,F,r(a[2],b)];case
22:return[22,r(a[1],b)];case
23:var
G=a[1];return[23,G,r(a[2],b)];default:var
H=a[2],I=a[1];return[24,I,H,r(a[3],b)]}}var
hi=fz,hj=gz;function
aj(a){throw i([0,dX,a],1)}function
d2(a,b){return mo(a,b)?a:b}function
bE(a){return 0<=a?a:-a|0}function
cv(a){return a?hi:hj}function
bF(a){var
c=dF("%.12g",a),b=0,e=l(c);for(;;){if(e<=b)return c+aU;var
d=R(c,b);a:{if(48<=d){if(58>d)break a}else if(45===d)break a;return c}b=b+1|0}}mv(0);g7(1);var
J=g7(2);function
aM(a,b){g8(a,b,0,l(b))}function
d3(a){aM(J,a);dP(J,10);return aq(J)}var
d4=[0,function(a){}],hk=[0,function(a){var
b=mw(0);for(;;){if(!b)return 0;var
d=b[2],e=b[1];try{aq(e)}catch(f){var
c=s(f);if(c[1]!==d0)throw i(c,0)}b=d}}];function
bG(a){g(d4[1],0);return g(cf(hk),0)}dT(fQ,bG);var
bb=(4*mI(0)|0)-1|0;function
ar(a){var
c=0,b=a;for(;;){if(!b)return c;c=c+1|0;b=b[2]}}function
bH(a,b){if(!b)return 0;var
f=b[2],i=b[1];if(!f)return[0,g(a,i),0];var
m=f[2],n=f[1],o=g(a,i),j=[0,g(a,n),a2],e=j,d=1,c=m;for(;;){if(c){var
h=c[2],k=c[1];if(h){var
p=h[2],q=h[1],r=g(a,k),l=[0,g(a,q),a2];e[1+d]=[0,r,l];e=l;d=1;c=p;continue}e[1+d]=[0,g(a,k),0]}else
e[1+d]=0;return[0,o,j]}}function
bI(a,b){var
c=b;for(;;){if(!c)return;var
d=c[2];g(a,c[1]);c=d}}function
d5(a,b){var
c=b;for(;;){if(!c)return 0;var
e=c[2],d=g(a,c[1]);if(d)return d;c=e}}function
bc(a,b){var
c=b;for(;;){if(!c)return 0;var
d=c[1],e=c[2];if(g(a,d))return[0,d];c=e}}function
cw(a,b){var
d=b;for(;;){if(!d)return 0;var
h=d[2],i=d[1];if(g(a,i)){var
j=[0,i,a2],f=j,e=1,c=h;for(;;){if(!c){f[1+e]=0;return j}var
k=c[2],l=c[1];if(g(a,l)){var
m=[0,l,a2];f[1+e]=m;f=m;e=1;c=k}else
c=k}}else
d=h}}function
aN(a,b){var
c=A(a);l1(c,0,a,b);return c}var
ht="String.sub / Bytes.sub",hu="Bytes.blit",hv="String.blit / Bytes.blit_string";function
cx(a,b,c){if(0<=b&&0<=c&&(ai(a)-c|0)>=b){var
d=A(c);aI(a,b,d,0,c);return d}return aj(ht)}function
d6(a,b,c){return N(cx(a,b,c))}function
d7(a,b,c,d,e){if(0<=e&&0<=b&&(ai(a)-e|0)>=b&&0<=d&&(ai(c)-e|0)>=d){aI(a,b,c,d,e);return}return aj(hu)}function
ak(a,b,c,d,e){if(0<=e&&0<=b&&(l(a)-e|0)>=b&&0<=d&&(ai(c)-e|0)>=d){ch(a,b,c,d,e);return}return aj(hv)}function
d8(a){var
b=a-9|0;a:{if(4<b>>>0){if(23!==b)break a}else if(2===b)break a;return 1}return 0}var
hs=A(0),hw="String.contains_from / Bytes.contains_from";function
aO(a,b,c){return N(cx(ah(a),b,c))}function
d9(a){var
b=a-9|0;a:{if(4<b>>>0){if(23!==b)break a}else if(2===b)break a;return 1}return 0}function
d_(a){if(a===e)return a;if(!d9($(a,0))&&!d9($(a,l(a)-1|0)))return a;var
d=ah(a),f=ai(d),b=[0,0];for(;;){if(b[1]>=f)break;if(!d8(a5(d,b[1])))break;b[1]++}var
c=[0,f-1|0];for(;;){if(b[1]<=c[1]&&d8(a5(d,c[1]))){c[1]--;continue}var
g=b[1]<=c[1]?cx(d,b[1],(c[1]-b[1]|0)+1|0):hs;return N(g)}}function
d$(a,b){var
d=l(a),g=0;if(d<0)return aj(hw);try{var
c=g;for(;;){if(d<=c)throw i(ct,1);if($(a,c)===b){var
f=1;return f}c=c+1|0}}catch(f){var
e=s(f);if(e===ct)return 0;throw i(e,0)}}function
ea(a,b){var
d=[0,0],e=[0,l(b)],f=l(b)-1|0;if(f>=0){var
c=f;for(;;){if($(b,c)===a){var
h=d[1];d[1]=[0,aO(b,c+1|0,(e[1]-c|0)-1|0),h];e[1]=c}var
i=c-1|0;if(0===c)break;c=i}}var
g=d[1];return[0,aO(b,0,e[1]),g]}function
bJ(a,b){return lU(ah(a),b)}function
eb(a,b){var
c=[0,a,0],d=b[3];return d?(b[1]=b[1]+1|0,d[2]=c,b[3]=c,0):(b[1]=1,b[2]=c,b[3]=c,0)}var
hz="Buffer.add: cannot grow buffer";function
ec(a,b){var
d=a[2],c=[0,a[1][2]];for(;;){if(c[1]>=(d+b|0))break;c[1]=2*c[1]|0}if(bb<c[1]){if((d+b|0)>bb)throw i([0,cs,hz],1);c[1]=bb}var
e=A(c[1]);d7(a[1][1],0,e,0,a[2]);a[1]=[0,e,c[1]]}function
bd(a,b){var
c=l(b),d=a[2],e=a[1],f=d+c|0,g=e[1];if(e[2]<f){ec(a,c);ak(b,0,a[1][1],a[2],c)}else
ch(b,0,g,d,c);a[2]=f}var
cy=[0,0];gR(g5(8,cy));var
e3=[0,0],hl=[0,0],hx="Array.blit";function
ed(a,b){var
d=[0,lO(hl,1),b];if(a){var
e=[0,d,a[1]];for(;;){var
c=cf(e3);if(!(1-gE(e3,c,[0,e,c])))break}}return d}function
bK(a){var
c=a[1],k=a[2],d=lZ(0),b=d.length-1;if(c<b)var
h=d;else{var
e=b;for(;;){if(c<e)break;e=2*e|0}var
f=g5(e,cy);a:{if(0<=b&&(d.length-1-b|0)>=0&&(f.length-1-b|0)>=0){lM(d,0,f,0,b);break a}aj(hx)}gR(f);var
h=f}var
i=aW(h,c)[1+c];if(i!==cy)return i;var
j=g(k,0);aW(h,c)[1+c]=j;return j}var
hA=ed(0,function(a,b){return 0});d4[1]=function(a){return g(bK(hA),0)};var
hB="@]",hC="@}",hD="@?",hE="@\n",hF="@.",hG="@@",hH="@%",hI="%c",hJ="%s",hK=fv,hL=fj,hM=gC,hN=fp,hO="%f",hP="%B",hQ="%{",hR="%}",hS="%(",hT="%)",hU="%a",hV="%t",hW="%?",hX="%r",hY="%_r",hZ=[0,a,850,23],h0=[0,a,837,26],h1=[0,a,847,28],h2=[0,a,815,21],h3=[0,a,819,21],h4=[0,a,823,19],h5=[0,a,827,22],h6=[0,a,832,30],h7=[0,a,851,23],h8=[0,a,836,26],h9=[0,a,846,28],h_=[0,a,814,21],h$=[0,a,818,21],ia=[0,a,822,19],ib=[0,a,826,22],ic=[0,a,831,30];function
cz(a){return 5===a[2]?12:-6}function
ee(a){return[0,0,A(a)]}function
ef(a,b){var
c=ai(a[2]),d=a[1]+b|0;if(c<d){var
e=c*2|0,g=d<=e?e:d,f=A(g);d7(a[2],0,f,0,c);a[2]=f}}function
aX(a,b){ef(a,1);az(a[2],a[1],b);a[1]=a[1]+1|0}function
F(a,b){var
c=l(b);ef(a,c);ak(b,0,a[2],a[1],c);a[1]=a[1]+c|0}function
eg(a){return d6(a[2],0,a[1])}function
eh(a){if(typeof
a==="number")switch(a){case
0:return hB;case
1:return hC;case
2:return hD;case
3:return hE;case
4:return hF;case
5:return hG;default:return hH}switch(a[0]){case
0:return a[1];case
1:return a[1];default:return"@"+N(aN(1,a[1]))}}function
cA(a,b){var
c=b;for(;;){if(typeof
c==="number")return;switch(c[0]){case
0:var
d=c[1];F(a,hI);c=d;break;case
1:var
e=c[1];F(a,hJ);c=e;break;case
2:var
f=c[1];F(a,hK);c=f;break;case
3:var
g=c[1];F(a,hL);c=g;break;case
4:var
h=c[1];F(a,hM);c=h;break;case
5:var
i=c[1];F(a,hN);c=i;break;case
6:var
j=c[1];F(a,hO);c=j;break;case
7:var
k=c[1];F(a,hP);c=k;break;case
8:var
l=c[2],m=c[1];F(a,hQ);cA(a,m);F(a,hR);c=l;break;case
9:var
n=c[3],o=c[1];F(a,hS);cA(a,o);F(a,hT);c=n;break;case
10:var
p=c[1];F(a,hU);c=p;break;case
11:var
q=c[1];F(a,hV);c=q;break;case
12:var
r=c[1];F(a,hW);c=r;break;case
13:var
s=c[1];F(a,hX);c=s;break;default:var
t=c[1];F(a,hY);c=t}}}function
x(a){if(typeof
a==="number")return 0;switch(a[0]){case
0:return[0,x(a[1])];case
1:return[1,x(a[1])];case
2:return[2,x(a[1])];case
3:return[3,x(a[1])];case
4:return[4,x(a[1])];case
5:return[5,x(a[1])];case
6:return[6,x(a[1])];case
7:return[7,x(a[1])];case
8:var
b=a[1];return[8,b,x(a[2])];case
9:var
c=a[2],d=a[1];return[9,c,d,x(a[3])];case
10:return[10,x(a[1])];case
11:return[11,x(a[1])];case
12:return[12,x(a[1])];case
13:return[13,x(a[1])];default:return[14,x(a[1])]}}function
G(a){if(typeof
a==="number")return[0,,function(a){},,function(a){}];switch(a[0]){case
0:var
b=G(a[1]),r=b[2];return[0,,function(a){r(0)},,b[4]];case
1:var
c=G(a[1]),s=c[2];return[0,,function(a){s(0)},,c[4]];case
2:var
d=G(a[1]),t=d[2];return[0,,function(a){t(0)},,d[4]];case
3:var
e=G(a[1]),u=e[2];return[0,,function(a){u(0)},,e[4]];case
4:var
f=G(a[1]),v=f[2];return[0,,function(a){v(0)},,f[4]];case
5:var
g=G(a[1]),w=g[2];return[0,,function(a){w(0)},,g[4]];case
6:var
h=G(a[1]),y=h[2];return[0,,function(a){y(0)},,h[4]];case
7:var
i=G(a[1]),A=i[2];return[0,,function(a){A(0)},,i[4]];case
8:var
j=G(a[2]),B=j[2];return[0,,function(a){B(0)},,j[4]];case
9:var
C=a[2],D=a[1],k=G(a[3]),E=k[4],F=k[2],l=G(z(x(D),C)),H=l[4],I=l[2];return[0,,function(a){I(0);F(0)},,function(a){H(0);E(0)}];case
10:var
m=G(a[1]),J=m[2];return[0,,function(a){J(0)},,m[4]];case
11:var
n=G(a[1]),K=n[2];return[0,,function(a){K(0)},,n[4]];case
12:var
o=G(a[1]),L=o[2];return[0,,function(a){L(0)},,o[4]];case
13:var
p=G(a[1]),M=p[4],N=p[2];return[0,,function(a){N(0)},,function(a){M(0)}];default:var
q=G(a[1]),O=q[4],P=q[2];return[0,,function(a){P(0)},,function(a){O(0)}]}}function
z(a,b){a:{b:{c:{d:{e:{f:{g:{if(typeof
a!=="number"){switch(a[0]){case
0:var
d=a[1];if(typeof
b!=="number")switch(b[0]){case
0:return[0,z(d,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
1:var
e=a[1];if(typeof
b!=="number")switch(b[0]){case
1:return[1,z(e,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
2:var
f=a[1];if(typeof
b!=="number")switch(b[0]){case
2:return[2,z(f,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
3:var
g=a[1];if(typeof
b!=="number")switch(b[0]){case
3:return[3,z(g,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
4:var
h=a[1];if(typeof
b!=="number")switch(b[0]){case
4:return[4,z(h,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
5:var
j=a[1];if(typeof
b!=="number")switch(b[0]){case
5:return[5,z(j,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
6:var
k=a[1];if(typeof
b!=="number")switch(b[0]){case
6:return[6,z(k,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
7:var
l=a[1];if(typeof
b!=="number")switch(b[0]){case
7:return[7,z(l,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
8:var
m=a[2],n=a[1];if(typeof
b!=="number")switch(b[0]){case
8:var
o=b[1],p=z(m,b[2]);return[8,z(n,o),p];case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}throw i([0,v,h8],1);case
9:var
q=a[3],r=a[2],s=a[1];if(typeof
b!=="number")switch(b[0]){case
8:break f;case
9:var
t=b[3],u=b[2],w=b[1],c=G(z(x(r),w)),y=c[4];c[2].call(null,0);y(0);return[9,s,u,z(q,t)];case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}throw i([0,v,h9],1);case
10:var
A=a[1];if(typeof
b!=="number"&&10===b[0])return[10,z(A,b[1])];throw i([0,v,h_],1);case
11:var
B=a[1];if(typeof
b!=="number")switch(b[0]){case
10:break a;case
11:return[11,z(B,b[1])]}throw i([0,v,h$],1);case
12:var
C=a[1];if(typeof
b!=="number")switch(b[0]){case
10:break a;case
11:break b;case
12:return[12,z(C,b[1])]}throw i([0,v,ia],1);case
13:var
D=a[1];if(typeof
b!=="number")switch(b[0]){case
10:break a;case
11:break b;case
12:break c;case
13:return[13,z(D,b[1])]}throw i([0,v,ib],1);default:var
E=a[1];if(typeof
b!=="number")switch(b[0]){case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:return[14,z(E,b[1])]}throw i([0,v,ic],1)}throw i([0,v,h7],1)}if(typeof
b==="number")return 0;switch(b[0]){case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e;case
8:break f;case
9:break;default:throw i([0,v,hZ],1)}}throw i([0,v,h1],1)}throw i([0,v,h0],1)}throw i([0,v,h6],1)}throw i([0,v,h5],1)}throw i([0,v,h4],1)}throw i([0,v,h3],1)}throw i([0,v,h2],1)}var
B=[y,"CamlinternalFormat.Type_mismatch",cl(0)],hm="\\\\",hn="\\'",ho="\\b",hp="\\t",hq="\\n",hr="\\r",ie=c1,ig="%+d",ih="% d",ii=fv,ij="%+i",ik="% i",il="%x",im="%#x",io="%X",ip="%#X",iq="%o",ir="%#o",is=f3,it="%Ld",iu="%+Ld",iv="% Ld",iw=fp,ix="%+Li",iy="% Li",iz="%Lx",iA="%#Lx",iB="%LX",iC="%#LX",iD="%Lo",iE="%#Lo",iF="%Lu",iG="%ld",iH="%+ld",iI="% ld",iJ=fj,iK="%+li",iL="% li",iM="%lx",iN="%#lx",iO="%lX",iP="%#lX",iQ="%lo",iR="%#lo",iS="%lu",iT="%nd",iU="%+nd",iV="% nd",iW=gC,iX="%+ni",iY="% ni",iZ="%nx",i0="%#nx",i1="%nX",i2="%#nX",i3="%no",i4="%#no",i5="%nu",i6=[0,e6],i$="neg_infinity",ja=fO,jb=c7,jd=[0,a,1558,4],je="Printf: bad conversion %[",jf=[0,a,1626,39],jg=[0,a,1649,31],jh=[0,a,1650,31],ji="Printf: bad conversion %_",jj=fD,jk=ge,jl=fD,jm=ge;function
bL(a,b){if(typeof
a==="number")return[0,0,b];if(0===a[0])return[0,[0,a[1],a[2]],b];if(typeof
b!=="number"&&2===b[0])return[0,[1,a[1]],b[1]];throw i(B,1)}function
be(a,b,c){var
d=bL(a,c);if(typeof
b!=="number")return[0,d[1],[0,b[1]],d[2]];if(!b)return[0,d[1],0,d[2]];var
e=d[2];if(typeof
e!=="number"&&2===e[0])return[0,d[1],1,e[1]];throw i(B,1)}function
o(a,b){if(typeof
a==="number")return[0,0,b];switch(a[0]){case
0:if(typeof
b!=="number"&&0===b[0]){var
v=o(a[1],b[1]);return[0,[0,v[1]],v[2]]}break;case
1:if(typeof
b!=="number"&&0===b[0]){var
w=o(a[1],b[1]);return[0,[1,w[1]],w[2]]}break;case
2:var
ag=a[2],x=bL(a[1],b),e=x[2],ah=x[1];if(typeof
e!=="number"&&1===e[0]){var
y=o(ag,e[1]);return[0,[2,ah,y[1]],y[2]]}throw i(B,1);case
3:var
ai=a[2],z=bL(a[1],b),f=z[2],aj=z[1];if(typeof
f!=="number"&&1===f[0]){var
A=o(ai,f[1]);return[0,[3,aj,A[1]],A[2]]}throw i(B,1);case
4:var
ak=a[4],al=a[1],g=be(a[2],a[3],b),h=g[3],am=g[1];if(typeof
h!=="number"&&2===h[0]){var
an=g[2],C=o(ak,h[1]);return[0,[4,al,am,an,C[1]],C[2]]}throw i(B,1);case
5:var
ao=a[4],ap=a[1],j=be(a[2],a[3],b),k=j[3],aq=j[1];if(typeof
k!=="number"&&3===k[0]){var
ar=j[2],D=o(ao,k[1]);return[0,[5,ap,aq,ar,D[1]],D[2]]}throw i(B,1);case
6:var
as=a[4],at=a[1],l=be(a[2],a[3],b),m=l[3],au=l[1];if(typeof
m!=="number"&&4===m[0]){var
av=l[2],E=o(as,m[1]);return[0,[6,at,au,av,E[1]],E[2]]}throw i(B,1);case
7:var
aw=a[4],ax=a[1],n=be(a[2],a[3],b),p=n[3],ay=n[1];if(typeof
p!=="number"&&5===p[0]){var
az=n[2],F=o(aw,p[1]);return[0,[7,ax,ay,az,F[1]],F[2]]}throw i(B,1);case
8:var
aA=a[4],aB=a[1],q=be(a[2],a[3],b),r=q[3],aC=q[1];if(typeof
r!=="number"&&6===r[0]){var
aD=q[2],G=o(aA,r[1]);return[0,[8,aB,aC,aD,G[1]],G[2]]}throw i(B,1);case
9:var
aE=a[2],H=bL(a[1],b),s=H[2],aF=H[1];if(typeof
s!=="number"&&7===s[0]){var
I=o(aE,s[1]);return[0,[9,aF,I[1]],I[2]]}throw i(B,1);case
10:var
J=o(a[1],b);return[0,[10,J[1]],J[2]];case
11:var
aG=a[1],K=o(a[2],b);return[0,[11,aG,K[1]],K[2]];case
12:var
aH=a[1],L=o(a[2],b);return[0,[12,aH,L[1]],L[2]];case
13:if(typeof
b!=="number"&&8===b[0]){var
M=b[1],aI=b[2],aJ=a[3],aK=a[1];if(aL([0,a[2]],[0,M]))throw i(B,1);var
N=o(aJ,aI);return[0,[13,aK,M,N[1]],N[2]]}break;case
14:if(typeof
b!=="number"&&9===b[0]){var
P=b[1],aM=b[3],aN=a[3],aO=a[2],aP=a[1],aQ=[0,t(P)];if(aL([0,t(aO)],aQ))throw i(B,1);var
Q=o(aN,t(aM));return[0,[14,aP,P,Q[1]],Q[2]]}break;case
15:if(typeof
b!=="number"&&10===b[0]){var
R=o(a[1],b[1]);return[0,[15,R[1]],R[2]]}break;case
16:if(typeof
b!=="number"&&11===b[0]){var
T=o(a[1],b[1]);return[0,[16,T[1]],T[2]]}break;case
17:var
aR=a[1],U=o(a[2],b);return[0,[17,aR,U[1]],U[2]];case
18:var
V=a[2],u=a[1];if(0===u[0]){var
Z=u[1],aV=Z[2],_=o(Z[1],b),aW=_[1],$=o(V,_[2]);return[0,[18,[0,[0,aW,aV]],$[1]],$[2]]}var
aa=u[1],aX=aa[2],ab=o(aa[1],b),aY=ab[1],ac=o(V,ab[2]);return[0,[18,[1,[0,aY,aX]],ac[1]],ac[2]];case
19:if(typeof
b!=="number"&&13===b[0]){var
W=o(a[1],b[1]);return[0,[19,W[1]],W[2]]}break;case
20:if(typeof
b!=="number"&&1===b[0]){var
aS=a[2],aT=a[1],X=o(a[3],b[1]);return[0,[20,aT,aS,X[1]],X[2]]}break;case
21:if(typeof
b!=="number"&&2===b[0]){var
aU=a[1],Y=o(a[2],b[1]);return[0,[21,aU,Y[1]],Y[2]]}break;case
23:var
d=a[2],c=a[1];if(typeof
c!=="number")switch(c[0]){case
0:return S(c,d,b);case
1:return S(c,d,b);case
2:return S(c,d,b);case
3:return S(c,d,b);case
4:return S(c,d,b);case
5:return S(c,d,b);case
6:return S(c,d,b);case
7:return S(c,d,b);case
8:return S([8,c[1],c[2]],d,b);case
9:var
aZ=c[1],ae=O(c[2],d,b),af=ae[2];return[0,[23,[9,aZ,ae[1]],af[1]],af[2]];case
10:return S(c,d,b);default:return S(c,d,b)}switch(c){case
0:return S(c,d,b);case
1:return S(c,d,b);case
2:if(typeof
b!=="number"&&14===b[0]){var
ad=o(d,b[1]);return[0,[23,2,ad[1]],ad[2]]}throw i(B,1);default:return S(c,d,b)}}throw i(B,1)}function
S(a,b,c){var
d=o(b,c);return[0,[23,a,d[1]],d[2]]}function
O(a,b,c){if(typeof
a==="number")return[0,0,o(b,c)];switch(a[0]){case
0:if(typeof
c!=="number"&&0===c[0]){var
f=O(a[1],b,c[1]);return[0,[0,f[1]],f[2]]}break;case
1:if(typeof
c!=="number"&&1===c[0]){var
g=O(a[1],b,c[1]);return[0,[1,g[1]],g[2]]}break;case
2:if(typeof
c!=="number"&&2===c[0]){var
h=O(a[1],b,c[1]);return[0,[2,h[1]],h[2]]}break;case
3:if(typeof
c!=="number"&&3===c[0]){var
j=O(a[1],b,c[1]);return[0,[3,j[1]],j[2]]}break;case
4:if(typeof
c!=="number"&&4===c[0]){var
k=O(a[1],b,c[1]);return[0,[4,k[1]],k[2]]}break;case
5:if(typeof
c!=="number"&&5===c[0]){var
l=O(a[1],b,c[1]);return[0,[5,l[1]],l[2]]}break;case
6:if(typeof
c!=="number"&&6===c[0]){var
m=O(a[1],b,c[1]);return[0,[6,m[1]],m[2]]}break;case
7:if(typeof
c!=="number"&&7===c[0]){var
n=O(a[1],b,c[1]);return[0,[7,n[1]],n[2]]}break;case
8:if(typeof
c!=="number"&&8===c[0]){var
p=c[1],A=c[2],C=a[2];if(aL([0,a[1]],[0,p]))throw i(B,1);var
q=O(C,b,A);return[0,[8,p,q[1]],q[2]]}break;case
9:if(typeof
c!=="number"&&9===c[0]){var
d=c[2],e=c[1],D=c[3],E=a[3],F=a[2],H=a[1],I=[0,t(e)];if(aL([0,t(H)],I))throw i(B,1);var
J=[0,t(d)];if(aL([0,t(F)],J))throw i(B,1);var
r=G(z(x(e),d)),K=r[4];r[2].call(null,0);K(0);var
s=O(t(E),b,D),L=s[2];return[0,[9,e,d,x(s[1])],L]}break;case
10:if(typeof
c!=="number"&&10===c[0]){var
u=O(a[1],b,c[1]);return[0,[10,u[1]],u[2]]}break;case
11:if(typeof
c!=="number"&&11===c[0]){var
v=O(a[1],b,c[1]);return[0,[11,v[1]],v[2]]}break;case
13:if(typeof
c!=="number"&&13===c[0]){var
w=O(a[1],b,c[1]);return[0,[13,w[1]],w[2]]}break;case
14:if(typeof
c!=="number"&&14===c[0]){var
y=O(a[1],b,c[1]);return[0,[14,y[1]],y[2]]}break}throw i(B,1)}function
T(a,b,c){var
d=l(c),g=0<=b?a:0,f=bE(b);if(f<=d)return c;var
h=2===g?48:32,e=aN(f,h);switch(g){case
0:ak(c,0,e,0,d);break;case
1:ak(c,0,e,f-d|0,d);break;default:a:if(0<d){if(43!==R(c,0)&&45!==R(c,0)&&32!==R(c,0))break a;az(e,0,R(c,0));ak(c,1,e,(f-d|0)+1|0,d-1|0);break}a:if(1<d&&48===R(c,0)){if(b8!==R(c,1)&&88!==R(c,1))break a;az(e,1,R(c,1));ak(c,2,e,(f-d|0)+2|0,d-2|0);break}ak(c,0,e,f-d|0,d)}return N(e)}function
aY(a,b){var
d=bE(a),c=l(b),e=R(b,0);a:{b:{if(58>e){if(32!==e){if(43>e)break a;switch(e-43|0){case
5:c:if(c<(d+2|0)&&1<c){if(b8!==R(b,1)&&88!==R(b,1))break c;var
g=aN(d+2|0,48);az(g,1,R(b,1));ak(b,2,g,(d-c|0)+4|0,c-2|0);return N(g)}break b;case
0:case
2:break;case
1:case
3:case
4:break a;default:break b}}if(c>=(d+1|0))break a;var
f=aN(d+1|0,48);az(f,0,e);ak(b,1,f,(d-c|0)+2|0,c-1|0);return N(f)}if(71<=e){if(5<e+fy>>>0)break a}else if(65>e)break a}if(c<d){var
h=aN(d,48);ak(b,0,h,d-c|0,c);return N(h)}}return b}function
id(a){var
e=ah(a),b=[0,0],k=ai(e)-1|0,r=0;if(k>=0){var
h=r;for(;;){var
f=a5(e,h);a:{b:{c:{if(32<=f){var
i=f-34|0;if(58<i>>>0){if(93<=i)break c}else if(56<i-1>>>0)break b;var
j=1;break a}if(11<=f){if(13===f)break b}else if(8<=f)break b}var
j=4;break a}var
j=2}b[1]=b[1]+j|0;var
v=h+1|0;if(k===h)break;h=v}}if(b[1]===ai(e))var
n=e;else{var
c=A(b[1]);b[1]=0;var
m=ai(e)-1|0,s=0;if(m>=0){var
g=s;for(;;){var
d=a5(e,g);a:{b:{c:{if(35<=d){if(92!==d){if(dt<=d)break c;break b}}else{if(32>d){if(14<=d)break c;switch(d){case
8:u(c,b[1],92);b[1]++;u(c,b[1],98);break a;case
9:u(c,b[1],92);b[1]++;u(c,b[1],116);break a;case
10:u(c,b[1],92);b[1]++;u(c,b[1],110);break a;case
13:u(c,b[1],92);b[1]++;u(c,b[1],114);break a;default:break c}}if(34>d)break b}u(c,b[1],92);b[1]++;u(c,b[1],d);break a}u(c,b[1],92);b[1]++;u(c,b[1],48+(d/fB|0)|0);b[1]++;u(c,b[1],48+((d/10|0)%10|0)|0);b[1]++;u(c,b[1],48+(d%10|0)|0);break a}u(c,b[1],d)}b[1]++;var
t=g+1|0;if(m===g)break;g=t}}var
n=c}var
q=N(n),o=l(q),p=aN(o+2|0,34);ch(q,0,p,1,o);return N(p)}function
ei(a,b){var
g=bE(b),f=i6[1];switch(a[2]){case
0:var
c=102;break;case
1:var
c=101;break;case
2:var
c=69;break;case
3:var
c=e6;break;case
4:var
c=71;break;case
5:var
c=f;break;case
6:var
c=104;break;case
7:var
c=72;break;default:var
c=70}var
d=ee(16);aX(d,37);switch(a[1]){case
0:break;case
1:aX(d,43);break;default:aX(d,32)}if(8<=a[2])aX(d,35);aX(d,46);F(d,e+g);aX(d,c);return eg(d)}function
bM(a,b){if(13>a)return b;var
h=[0,0],i=l(b)-1|0,o=0;if(i>=0){var
d=o;for(;;){if(9>=$(b,d)+f8>>>0)h[1]++;var
r=d+1|0;if(i===d)break;d=r}}var
j=h[1],k=A(l(b)+((j-1|0)/3|0)|0),m=[0,0];function
e(a){az(k,m[1],a);m[1]++}var
f=[0,((j-1|0)%3|0)+1|0],n=l(b)-1|0,p=0;if(n>=0){var
c=p;for(;;){var
g=$(b,c);if(9<g+f8>>>0)e(g);else{if(0===f[1]){e(95);f[1]=3}f[1]--;e(g)}var
q=c+1|0;if(n===c)break;c=q}}return N(k)}function
i7(a,b){switch(a){case
1:var
c=ig;break;case
2:var
c=ih;break;case
4:var
c=ij;break;case
5:var
c=ik;break;case
6:var
c=il;break;case
7:var
c=im;break;case
8:var
c=io;break;case
9:var
c=ip;break;case
10:var
c=iq;break;case
11:var
c=ir;break;case
0:case
13:var
c=ie;break;case
3:case
14:var
c=ii;break;default:var
c=is}return bM(a,ck(c,b))}function
i8(a,b){switch(a){case
1:var
c=iH;break;case
2:var
c=iI;break;case
4:var
c=iK;break;case
5:var
c=iL;break;case
6:var
c=iM;break;case
7:var
c=iN;break;case
8:var
c=iO;break;case
9:var
c=iP;break;case
10:var
c=iQ;break;case
11:var
c=iR;break;case
0:case
13:var
c=iG;break;case
3:case
14:var
c=iJ;break;default:var
c=iS}return bM(a,ck(c,b))}function
i9(a,b){switch(a){case
1:var
c=iU;break;case
2:var
c=iV;break;case
4:var
c=iX;break;case
5:var
c=iY;break;case
6:var
c=iZ;break;case
7:var
c=i0;break;case
8:var
c=i1;break;case
9:var
c=i2;break;case
10:var
c=i3;break;case
11:var
c=i4;break;case
0:case
13:var
c=iT;break;case
3:case
14:var
c=iW;break;default:var
c=i5}return bM(a,ck(c,b))}function
i_(a,b){switch(a){case
1:var
c=iu;break;case
2:var
c=iv;break;case
4:var
c=ix;break;case
5:var
c=iy;break;case
6:var
c=iz;break;case
7:var
c=iA;break;case
8:var
c=iB;break;case
9:var
c=iC;break;case
10:var
c=iD;break;case
11:var
c=iE;break;case
0:case
13:var
c=it;break;case
3:case
14:var
c=iw;break;default:var
c=iF}return bM(a,l_(c,b))}function
as(d,b,c){function
i(a){switch(d[1]){case
0:var
e=45;break;case
1:var
e=43;break;default:var
e=32}return l6(c,b,e)}function
q(a){var
b=lW(c);return 3===b?c<0.?i$:ja:4<=b?jb:a}switch(d[2]){case
5:var
f=dF(ei(d,b),c),e=0,v=l(f);for(;;){if(e===v)var
p=0;else{var
j=R(f,e)-46|0;a:{if(23<j>>>0){if(55===j)break a}else if(21<j-1>>>0)break a;e=e+1|0;continue}var
p=1}var
w=p?f:f+aU;return q(w)}case
6:return i(0);case
7:var
k=ah(i(0)),g=ai(k);if(0===g)var
o=k;else{var
m=A(g),n=g-1|0,r=0;if(n>=0){var
a=r;for(;;){var
h=a5(k,a),s=25<h+fy>>>0?h:h-32|0;u(m,a,s);var
t=a+1|0;if(n===a)break;a=t}}var
o=m}return N(o);case
8:return q(i(0));default:return dF(ei(d,b),c)}}function
bn(a,b,c,d){var
f=b,e=c,h=d;for(;;){if(typeof
h==="number")return g(f,e);switch(h[0]){case
0:var
L=h[1];return function(a){return m(f,[5,e,a],L)};case
1:var
M=h[1];return function(a){a:{b:{if(40<=a){if(92===a){var
b=hm;break a}if(dt>a)break b}else{if(32<=a){if(39>a)break b;var
b=hn;break a}if(14>a)switch(a){case
8:var
b=ho;break a;case
9:var
b=hp;break a;case
10:var
b=hq;break a;case
13:var
b=hr;break a}}var
c=A(4);u(c,0,92);u(c,1,48+(a/fB|0)|0);u(c,2,48+((a/10|0)%10|0)|0);u(c,3,48+(a%10|0)|0);var
b=N(c);break a}var
d=A(1);u(d,0,a);var
b=N(d)}var
g=l(b),h=aN(g+2|0,39);ch(b,0,h,1,g);return m(f,[4,e,N(h)],M)};case
2:return cB(f,e,h[2],h[1],function(a){return a});case
3:return cB(f,e,h[2],h[1],id);case
4:return bN(f,e,h[4],h[2],h[3],i7,h[1]);case
5:return bN(f,e,h[4],h[2],h[3],i8,h[1]);case
6:return bN(f,e,h[4],h[2],h[3],i9,h[1]);case
7:return bN(f,e,h[4],h[2],h[3],i_,h[1]);case
8:var
q=h[4],s=h[3],w=h[2],k=h[1];if(typeof
w==="number"){if(typeof
s==="number")return s?function(a,b){return m(f,[4,e,as(k,a,b)],q)}:function(a){return m(f,[4,e,as(k,cz(k),a)],q)};var
_=s[1];return function(a){return m(f,[4,e,as(k,_,a)],q)}}if(0===w[0]){var
C=w[2],D=w[1];if(typeof
s==="number")return s?function(a,b){return m(f,[4,e,T(D,C,as(k,a,b))],q)}:function(a){return m(f,[4,e,T(D,C,as(k,cz(k),a))],q)};var
$=s[1];return function(a){return m(f,[4,e,T(D,C,as(k,$,a))],q)}}var
E=w[1];if(typeof
s==="number")return s?function(a,b,c){return m(f,[4,e,T(E,a,as(k,b,c))],q)}:function(a,b){return m(f,[4,e,T(E,a,as(k,cz(k),b))],q)};var
aa=s[1];return function(a,b){return m(f,[4,e,T(E,a,as(k,aa,b))],q)};case
9:return cB(f,e,h[2],h[1],cv);case
10:e=[7,e];h=h[1];break;case
11:e=[2,e,h[1]];h=h[2];break;case
12:e=[3,e,h[1]];h=h[2];break;case
13:var
O=h[3],P=h[2],F=ee(16);cA(F,P);var
K=eg(F);return function(a){return m(f,[4,e,K],O)};case
14:var
Q=h[3],R=h[2];return function(a){var
c=a[1],b=o(c,t(x(R)));if(typeof
b[2]==="number")return m(f,e,r(b[1],Q));throw i(B,1)};case
15:var
S=h[1];return function(c,b){return m(f,[6,e,function(a){return V(c,a,b)}],S)};case
16:var
U=h[1];return function(a){return m(f,[6,e,a],U)};case
17:e=[0,e,h[1]];h=h[2];break;case
18:var
z=h[1];if(0===z[0]){let
b=e,c=f,d=h[2];f=function(a){return m(c,[1,b,[0,a]],d)};e=0;h=z[1][1]}else{let
b=e,c=f,d=h[2];f=function(a){return m(c,[1,b,[1,a]],d)};e=0;h=z[1][1]}break;case
19:throw i([0,v,jd],1);case
20:var
W=h[3],X=[8,e,je];return function(a){return m(f,X,W)};case
21:var
Y=h[2];return function(a){return m(f,[4,e,ck(f3,a)],Y)};case
22:var
Z=h[1];return function(a){return m(f,[5,e,a],Z)};case
23:var
j=h[2],y=h[1];if(typeof
y==="number")switch(y){case
0:return a<50?n(a+1|0,f,e,j):p(n,[0,f,e,j]);case
1:return a<50?n(a+1|0,f,e,j):p(n,[0,f,e,j]);case
2:throw i([0,v,jf],1);default:return a<50?n(a+1|0,f,e,j):p(n,[0,f,e,j])}switch(y[0]){case
0:return a<50?n(a+1|0,f,e,j):p(n,[0,f,e,j]);case
1:return a<50?n(a+1|0,f,e,j):p(n,[0,f,e,j]);case
2:return a<50?n(a+1|0,f,e,j):p(n,[0,f,e,j]);case
3:return a<50?n(a+1|0,f,e,j):p(n,[0,f,e,j]);case
4:return a<50?n(a+1|0,f,e,j):p(n,[0,f,e,j]);case
5:return a<50?n(a+1|0,f,e,j):p(n,[0,f,e,j]);case
6:return a<50?n(a+1|0,f,e,j):p(n,[0,f,e,j]);case
7:return a<50?n(a+1|0,f,e,j):p(n,[0,f,e,j]);case
8:return a<50?n(a+1|0,f,e,j):p(n,[0,f,e,j]);case
9:var
J=y[2];return a<50?cZ(a+1|0,f,e,J,j):p(cZ,[0,f,e,J,j]);case
10:return a<50?n(a+1|0,f,e,j):p(n,[0,f,e,j]);default:return a<50?n(a+1|0,f,e,j):p(n,[0,f,e,j])}default:var
G=h[3],H=h[1],I=g(h[2],0);return a<50?cY(a+1|0,f,e,G,H,I):p(cY,[0,f,e,G,H,I])}}}function
m(a,b,c){return bC(bn(0,a,b,c))}function
cZ(a,f,c,d,e){if(typeof
d==="number")return a<50?n(a+1|0,f,c,e):p(n,[0,f,c,e]);switch(d[0]){case
0:var
b=d[1];return function(a){return Y(f,c,b,e)};case
1:var
g=d[1];return function(a){return Y(f,c,g,e)};case
2:var
h=d[1];return function(a){return Y(f,c,h,e)};case
3:var
j=d[1];return function(a){return Y(f,c,j,e)};case
4:var
k=d[1];return function(a){return Y(f,c,k,e)};case
5:var
l=d[1];return function(a){return Y(f,c,l,e)};case
6:var
m=d[1];return function(a){return Y(f,c,m,e)};case
7:var
o=d[1];return function(a){return Y(f,c,o,e)};case
8:var
q=d[2];return function(a){return Y(f,c,q,e)};case
9:var
r=d[3],s=d[2],t=z(x(d[1]),s);return function(a){return Y(f,c,K(t,r),e)};case
10:var
u=d[1];return function(a,b){return Y(f,c,u,e)};case
11:var
w=d[1];return function(a){return Y(f,c,w,e)};case
12:var
y=d[1];return function(a){return Y(f,c,y,e)};case
13:throw i([0,v,jg],1);default:throw i([0,v,jh],1)}}function
Y(a,b,c,d){return bC(cZ(0,a,b,c,d))}function
n(a,b,c,d){var
e=[8,c,ji];return a<50?bn(a+1|0,b,e,d):p(bn,[0,b,e,d])}function
cB(h,f,c,d,e){if(typeof
d==="number")return function(a){return m(h,[4,f,g(e,a)],c)};if(0===d[0]){var
b=d[2],i=d[1];return function(a){return m(h,[4,f,T(i,b,g(e,a))],c)}}var
j=d[1];return function(a,b){return m(h,[4,f,T(j,a,g(e,b))],c)}}function
bN(j,i,h,d,e,f,g){if(typeof
d==="number"){if(typeof
e==="number")return e?function(a,b){return m(j,[4,i,aY(a,V(f,g,b))],h)}:function(a){return m(j,[4,i,V(f,g,a)],h)};var
b=e[1];return function(a){return m(j,[4,i,aY(b,V(f,g,a))],h)}}if(0===d[0]){var
c=d[2],k=d[1];if(typeof
e==="number")return e?function(a,b){return m(j,[4,i,T(k,c,aY(a,V(f,g,b)))],h)}:function(a){return m(j,[4,i,T(k,c,V(f,g,a))],h)};var
n=e[1];return function(a){return m(j,[4,i,T(k,c,aY(n,V(f,g,a)))],h)}}var
l=d[1];if(typeof
e==="number")return e?function(a,b,c){return m(j,[4,i,T(l,a,aY(b,V(f,g,c)))],h)}:function(a,b){return m(j,[4,i,T(l,a,V(f,g,b))],h)};var
o=e[1];return function(a,b){return m(j,[4,i,T(l,a,aY(o,V(f,g,b)))],h)}}function
cY(a,b,c,d,e,f){if(e){var
i=e[1];return function(a){return jc(b,c,d,i,g(f,a))}}var
h=[4,c,f];return a<50?bn(a+1|0,b,h,d):p(bn,[0,b,h,d])}function
jc(a,b,c,d,e){return bC(cY(0,a,b,c,d,e))}function
at(a,b){var
c=b;for(;;){if(typeof
c==="number")return;switch(c[0]){case
0:var
f=c[1],h=eh(c[2]);at(a,f);return aM(a,h);case
1:var
d=c[2],e=c[1];if(0===d[0]){var
i=d[1];at(a,e);aM(a,jj);c=i}else{var
j=d[1];at(a,e);aM(a,jk);c=j}break;case
6:var
m=c[2];at(a,c[1]);return g(m,a);case
7:at(a,c[1]);aq(a);return;case
8:var
n=c[2];at(a,c[1]);return aj(n);case
2:case
4:var
k=c[2];at(a,c[1]);return aM(a,k);default:var
l=c[2];at(a,c[1]);dP(a,l);return}}}function
aE(a,b){var
c=b;for(;;){if(typeof
c==="number")return;switch(c[0]){case
0:var
k=c[1],l=eh(c[2]);aE(a,k);return bd(a,l);case
1:var
e=c[2],h=c[1];if(0===e[0]){var
m=e[1];aE(a,h);bd(a,jl);c=m}else{var
n=e[1];aE(a,h);bd(a,jm);c=n}break;case
6:var
p=c[2];aE(a,c[1]);return bd(a,g(p,0));case
7:c=c[1];break;case
8:var
q=c[2];aE(a,c[1]);return aj(q);case
2:case
4:var
o=c[2];aE(a,c[1]);return bd(a,o);default:var
i=c[2];aE(a,c[1]);var
d=a[2],f=a[1],j=f[1];if(f[2]<=d){ec(a,1);az(a[1][1],a[2],i)}else
u(j,d,i);a[2]=d+1|0;return}}}function
bO(c,b){return m(function(a){at(c,a);return 0},0,b[1])}function
cC(e,b){return m(function(a){var
f=64,c=bb<64?bb:f,d=A(c),b=[0,[0,d,c],0,d];aE(b,a);return g(e,d6(b[1][1],0,b[2]))},0,b[1])}function
f(a){return cC(function(a){return a},a)}var
cX=[0,0],jn=[0,[3,0,0],"%S"],jo=gf,jp=[0,[4,0,0,0,0],c1],jq=e,jr=[0,[11,ad,[2,0,[2,0,0]]],", %s%s"],js=[0,[12,40,[2,0,[2,0,[12,41,0]]]],"(%s%s)"],jt=e,ju=e,jv=[0,[12,40,[2,0,[12,41,0]]],"(%s)"],jw="Out of memory",jx="Stack overflow",jy="Pattern matching failed",jz="Assertion failed",jA="Undefined recursive module",jB="Raised at",jC="Re-raised at",jD="Raised by primitive operation at",jE="Called from",jF=" (inlined)",jG=[0,[2,0,[12,32,[2,0,[11,' in file "',[2,0,[12,34,[2,0,[11,", line ",[4,0,0,0,[11,gy,lA]]]]]]]]]],'%s %s in file "%s"%s, line %d, characters %d-%d'],jH=e,jI=[0,[2,0,[11," unknown location",0]],"%s unknown location"],jJ=[0,[2,0,[12,10,0]],"%s\n"];function
cE(a,b){var
c=a[1+b];return 1-(typeof
c==="number"?1:0)?cn(c)===b3?g(f(jn),c):cn(c)===253?bF(c):jo:g(f(jp),c)}function
ej(a,b){if(a.length-1<=b)return jq;var
c=ej(a,b+1|0),d=cE(a,b);return V(f(jr),d,c)}function
P(a){a:{b:{var
b=cf(cX);for(;;){if(!b)break;c:{var
u=b[2],w=b[1];try{var
l=g(w,a)}catch(f){break c}if(l)break b}b=u}var
j=0;break a}var
j=[0,l[1]]}if(j)return j[1];if(a===cu)return jw;if(a===dZ)return jx;if(a[1]===dY){var
e=a[2],o=e[3],y=e[2],z=e[1];return b0(f(cD),z,y,o,o+5|0,jy)}if(a[1]===v){var
h=a[2],p=h[3],A=h[2],B=h[1];return b0(f(cD),B,A,p,p+6|0,jz)}if(a[1]===d1){var
i=a[2],q=i[3],C=i[2],D=i[1];return b0(f(cD),D,C,q,q+6|0,jA)}if(0===cn(a)){var
k=a.length-1,x=a[1][1];if(2<k>>>0)var
r=ej(a,2),s=cE(a,1),c=V(f(js),s,r);else
switch(k){case
0:var
c=jt;break;case
1:var
c=ju;break;default:var
t=cE(a,1),c=g(f(jv),t)}var
d=[0,x,[0,c]]}else
var
d=[0,a[1],0];var
m=d[2],n=d[1];return m?n+m[1]:n}function
bP(a,b){var
h=lX(b),j=h.length-2|0,s=0;if(j>=0){var
d=s;for(;;){var
c=aW(h,d)[1+d];let
b=d;var
i=function(a){return a?0===b?jB:jC:0===b?jD:jE};if(0===c[0])var
k=c[5],l=c[4],m=c[3],n=c[6]?jF:jH,o=c[2],p=c[7],q=i(c[1]),e=[0,lH(f(jG),q,p,o,n,m,l,k)];else if(c[1])var
e=0;else
var
r=i(0),e=[0,g(f(jI),r)];if(e){var
t=e[1];g(bO(a,jJ),t)}var
u=d+1|0;if(j===d)break;d=u}}return 0}function
ek(a){return bP(a,dG(0))}function
el(a){for(;;){var
c=cf(cX),b=1-gE(cX,c,[0,a,c]);if(!b)return b}}var
jK=[0,e,"(Cannot print locations:\n bytecode executable program file not found)","(Cannot print locations:\n bytecode executable program file appears to be corrupt)","(Cannot print locations:\n bytecode executable program file has wrong magic number)","(Cannot print locations:\n bytecode executable program file cannot be opened;\n -- too many open files. Try running with OCAMLRUNPARAM=b=2)"].slice(),jL=[0,[11,b7,[2,0,[12,10,0]]],f5],jM=[0],jN="Fatal error: out of memory in uncaught exception handler",jO=[0,[11,b7,[2,0,[12,10,0]]],f5],jP=[0,[11,"Fatal error in uncaught exception handler: exception ",[2,0,[12,10,0]]],"Fatal error in uncaught exception handler: exception %s\n"];dT(gj,function(a,b){try{try{var
h=b?jM:dG(0);try{bG(0)}catch(f){}try{var
f=P(a);g(bO(J,jL),f);bP(J,h);var
c=mu(0);if(c<0){var
d=bE(c);d3(aW(jK,d)[1+d])}var
o=aq(J),j=o}catch(f){var
l=s(f),m=P(a);g(bO(J,jO),m);bP(J,h);var
n=P(l);g(bO(J,jP),n);bP(J,dG(0));var
j=aq(J)}var
k=j}catch(f){var
e=s(f);if(e!==cu)throw i(e,0);var
k=d3(jN)}return k}catch(f){return 0}});function
em(a){var
b=N(a);return mt(b,0,l(b))}var
jQ=X(1,0,0),jR=X(0,0,0),jS=X(0,0,0),jT=X(2,0,0),jU=X(1,0,0);function
en(a,b,c,d,e){bx(a,0,g0(b,jQ));bx(a,1,c);var
f=aL(d,jR)?d:jU;bx(a,2,f);var
g=aL(e,jS)?e:jT;bx(a,3,g)}function
eo(a,b,c,d){var
e=lP(7,0,[0,4]);en(e,a,b,c,d);return e}var
j2=X(14371852,15349651,22696),j3=X(12230193,11438743,35013),j4=X(1424933,15549263,2083),j5=X(9492471,4696708,43520),cF=ed([0,function(a){var
b=a_(a),d=a_(a),c=a_(a);return eo(b,d,c,a_(a))}],function(a){return eo(j5,j4,j3,j2)}),j1=X(0,0,0);function
bR(a){var
c=bK(cF);for(;;){var
b=g1(a_(c),11);if(aL(b,j1))return me(b)*1.1102230246251565e-16*a}}var
et=[y,"Jsoo_runtime.Error.Exn",cl(0)],cG=[0,et,[0]],j0="Random.int",kq=[0,0],kr=[0,1],ko=[0,0],kp=[0,1],km=[0,0],kn=[0,1],kk=[0,0],kl=[0,1],ki=[0,0],kj=[0,1],kg=[0,0],kh=[0,0],kf=[0,0],kc=[0,[11,f6,[2,0,0]],fm],kd=[0,[11,f6,[2,0,0]],fm],ke=[0,0],j$=b7,ka="Lwt.Resolution_loop.Canceled",ks="Exception during Lwt.async: ",kt="Lwt_js_event",lt=[0,[11,"Difficulty: ",[8,[0,0,0],0,[0,1],[12,b8,0]]],"Difficulty: %.1fx"],ls=[0,[11,"Time: ",[8,[0,0,0],0,[0,1],[12,115,0]]],"Time: %.1fs"],lr=[0,[11,"Healthy Creets: ",[4,0,0,0,0]],"Healthy Creets: %d"],lw=ds,lx=c9,lu=[0,[11,"Game state initialized with ",[4,0,0,0,[11,ff,0]]],"Game state initialized with %d Creets"],lv=[0,[11,"Added ",[4,0,0,0,[11," Creets to DOM",0]]],"Added %d Creets to DOM"],lq=[0,[11,"New creet reproduced at (",[8,[0,0,0],0,[0,1],[11,ad,[8,[0,0,0],0,[0,1],[12,41,0]]]]],"New creet reproduced at (%.1f, %.1f)"],lp=[0,[11,"Starting Lwt threads for ",[4,0,0,0,[11,ff,0]]],"Starting Lwt threads for %d Creets"],lo=[0,[11,"Starting Lwt thread for Creet ",[4,0,0,0,0]],"Starting Lwt thread for Creet %d"],ll=[0,[11,"Element created for Creet ",[4,0,0,0,[11,", adding to game area",0]]],"Element created for Creet %d, adding to game area"],lm=[0,[11,dl,[4,0,0,0,[11," successfully added to DOM with mouse events",0]]],"Creet %d successfully added to DOM with mouse events"],ln=[0,[11,"Failed to create element for Creet ",[4,0,0,0,0]],"Failed to create element for Creet %d"],lk=[0,[11,"Adding Creet ",[4,0,0,0,[11," to DOM at (",[8,[0,0,0],0,[0,1],[11,ad,[8,[0,0,0],0,[0,1],[12,41,0]]]]]]],"Adding Creet %d to DOM at (%.1f, %.1f)"],lj=[0,[11,"Lwt Mouse down on Creet ",[4,0,0,0,0]],"Lwt Mouse down on Creet %d"],li=[0,[11,"Stopping drag for Creet ",[4,0,0,0,0]],"Stopping drag for Creet %d"],lg=[0,[11,"Start drag - Mouse: (",[8,[0,0,0],0,[0,1],[11,ad,[8,[0,0,0],0,[0,1],[11,"), Game area offset: (",[8,[0,0,0],0,[0,1],[11,ad,[8,[0,0,0],0,[0,1],[11,"), Relative: (",[8,lD,0,lC,lB]]]]]]]]]],"Start drag - Mouse: (%.1f, %.1f), Game area offset: (%.1f, %.1f), Relative: (%.1f, %.1f)"],lh=[0,[11,dl,[4,0,0,0,[11," at (",[8,[0,0,0],0,[0,1],[11,ad,[8,[0,0,0],0,[0,1],[11,"), Drag offset: (",[8,[0,0,0],0,[0,1],[11,ad,[8,lG,0,lF,lE]]]]]]]]]],"Creet %d at (%.1f, %.1f), Drag offset: (%.1f, %.1f)"],lf=[0,[11,"Starting drag for Creet ",[4,0,0,0,0]],"Starting drag for Creet %d"],k5=[0,[8,[0,0,0],0,[0,1],[11,ax,0]],aF],k6=[0,[8,[0,0,0],0,[0,1],[11,ax,0]],aF],k7=[0,[8,[0,0,0],0,[0,1],[11,ax,0]],aF],k8=[0,[8,[0,0,0],0,[0,1],[11,ax,0]],aF],k9=fG,k_=gp,k$=gi,la=fi,lb="#B71C1C",lc=f7,ld=gu,le=fZ,k3=[0,[11,"Game state: ",[9,0,0]],"Game state: %b"],k2=[0,[11,"Using fixed game area dimensions: ",[8,[0,0,0],0,[0,1],[12,b8,[8,[0,0,0],0,[0,1],0]]]],"Using fixed game area dimensions: %.1fx%.1f"],k1=[0,e7,dk],kO=[0,[11,"Setting Creet ",[4,0,0,0,[11," position: left=",[8,[0,0,0],0,[0,1],[11,"px, top=",[8,[0,0,0],0,[0,1],[11,ax,0]]]]]]],"Setting Creet %d position: left=%.1fpx, top=%.1fpx"],kP=[0,[8,[0,0,0],0,[0,1],[11,ax,0]],aF],kQ=[0,[8,[0,0,0],0,[0,1],[11,ax,0]],aF],kR=[0,[8,[0,0,0],0,[0,1],[11,ax,0]],aF],kS=[0,[8,[0,0,0],0,[0,1],[11,ax,0]],aF],kT=fG,kU=gp,kV=f7,kW="#FF5722",kX=gu,kY=fZ,kZ=gi,k0=fi,kN=[0,0.,0.],kM=[0,[11,dl,[4,0,0,0,[11," assigned position ",[4,0,0,0,[11,fW,[8,[0,0,0],0,[0,1],[11,ad,[8,[0,0,0],0,[0,1],[12,41,0]]]]]]]]],"Creet %d assigned position %d: (%.1f, %.1f)"],kL=[0,[11,"Selected spawn position ",[4,0,0,0,[11,fW,[8,[0,0,0],0,[0,1],[11,ad,[8,[0,0,0],0,[0,1],[12,41,0]]]]]]],"Selected spawn position %d: (%.1f, %.1f)"],kH="spawn-delay",kG=f4,kF=ga,kE=gn,kD=fg,kC=c9,kB=ds,kA=gB,kz=[0,[2,0,[2,0,[12,61,[2,0,[11,";expires=",[2,0,[11,";path=/",0]]]]]]],"%s%s=%s;expires=%s;path=/"],kx=ca,ky=[0,[0,gB,dj],[0,[0,ds,go],[0,[0,c9,go],[0,[0,fg,"2"],[0,[0,gn,dj],[0,[0,ga,dj],[0,[0,f4,"3"],[0,[0,bt,gz],0]]]]]]]],kK=[0,[0,c4,cb],[0,b6,130.],[0,b$,fR],[0,bo,135.],[0,500.,cb],[0,600.,fn],[0,700.,fR],[0,150.,fe],[0,250.,170.],[0,fT,165.],[0,450.,175.],[0,550.,fe],[0,650.,f2],[0,80.,dh],[0,f2,230.],[0,dx,fd],[0,380.,235.],[0,fU,dh],[0,dk,gb],[0,680.,fd],[0,cb,dx],[0,dh,fA],[0,320.,gl],[0,gh,295.],[0,520.,dx],[0,620.,b$],[0,720.,gl],[0,90.,c8],[0,190.,fT],[0,fA,fX],[0,390.,355.],[0,490.,c8],[0,590.,360.],[0,690.,fX],[0,110.,bo],[0,210.,fM],[0,310.,fq],[0,fM,415.],[0,510.,bo],[0,610.,gh],[0,710.,fq],[0,fn,fS],[0,gb,470.],[0,c8,465.],[0,440.,475.],[0,540.,fS],[0,640.,fU],[0,a3,b6],[0,a3,b$],[0,a3,bo],[0,dv,b6],[0,dv,b$],[0,dv,bo]],hy=[y,"Stdlib.Queue.Empty",cl(0)],j7=cn(cG)===y?cG:cG[1];dT(di,j7);(function(a){throw a});var
Q=c,C=null,eu=undefined;function
cH(a){return 1-(a==C?1:0)}function
ev(a){return 1-(a===eu?1:0)}var
cI=false;Q.String;Q.RegExp;Q.Object;var
cJ=Q.Date;Q.Math;Q.Error;Q.JSON;var
j8=Q.Array;el(function(a){return a[1]===et?[0,E(a[2].toString())]:0});el(function(a){return a
instanceof
j8?0:[0,E(a.toString())]});function
bS(d){return mn(function(a){if(cH(a)){var
e=g(d,a);if(1-(e|0))a.preventDefault();return e}var
c=event,b=g(d,c);if(1-(b|0))c.returnValue=b;return b})}var
q=Q.document,j9="mousedown",lz="mouseup",ly="mousemove";ev(Q.HTMLElement);var
h=mm(0);function
H(a){var
c=a[1];switch(c[0]){case
0:return a;case
1:return a;case
2:return a;default:var
d=c[1],b=H(d);if(1-(b===d?1:0))a[1]=[3,b];return b}}var
aa=[0,0];function
ex(a,b){return typeof
a==="number"?b:typeof
b==="number"?a:[0,a,b]}function
cL(a){if(typeof
a!=="number")switch(a[0]){case
0:var
b=a[2],c=cL(a[1]);return ex(c,cL(b));case
2:if(!a[1][1])return 0;break}return a}function
bg(a,b){var
d=[1,b],c=a[1],e=typeof
c==="number"?d:[0,d,c];a[1]=e;return 0}var
bT=[0,function(a){aM(J,j$);aM(J,P(a));dP(J,10);ek(J);aq(J);bG(0);return mJ(2)}];function
cM(a,b){try{var
d=g(a,b);return d}catch(f){var
c=s(f);return g(bT[1],c)}}var
bh=[y,ka,cl(0)];function
ey(a,i){if(1===i[0])var
f=i[1]===bh?1:0,b=f;else
var
b=0;if(b){var
d=function(a,b,c){var
e=b,d=c;for(;;){if(typeof
e==="number")return a<50?h(a+1|0,d):p(h,[0,d]);switch(e[0]){case
0:var
g=[0,e[2],d];e=e[1];d=g;break;case
1:var
i=e[2];aa[1]=e[1];cM(i,0);return a<50?h(a+1|0,d):p(h,[0,d]);default:var
f=e[1];if(f[4]){f[4]=0;f[1][2]=f[2];f[2][1]=f[1]}return a<50?h(a+1|0,d):p(h,[0,d])}}},h=function(a,b){if(!b)return;var
c=b[2],e=b[1];return a<50?d(a+1|0,e,c):p(d,[0,e,c])};(function(a,b){return bC(d(0,a,b))}(a[2],0))}function
c(a,b,c){var
f=b,d=c;for(;;){if(typeof
f==="number")return a<50?e(a+1|0,d):p(e,[0,d]);switch(f[0]){case
0:var
j=[0,f[2],d];f=f[1];d=j;break;case
1:g(f[1],i);return a<50?e(a+1|0,d):p(e,[0,d]);default:var
h=f[1][1];return h?(g(h[1],i),a<50?e(a+1|0,d):p(e,[0,d])):a<50?e(a+1|0,d):p(e,[0,d])}}}function
e(a,b){if(!b)return 0;var
d=b[2],e=b[1];return a<50?c(a+1|0,e,d):p(c,[0,e,d])}return function(a,b){return bC(c(0,a,b))}(a[1],0)}var
aP=[0,0],al=[0,0,0,0],kb=42;function
ez(a){aP[1]=aP[1]+1|0;var
e=aa[1],f=g(a,0);if(1===aP[1])for(;;){if(0===al[1])break;var
b=al[2];if(!b)throw i(hy,1);var
c=b[1];if(b[2]){var
d=b[2];al[1]=al[1]-1|0;al[2]=d}else{al[1]=0;al[2]=0;al[3]=0}ey(c[1],c[2])}aP[1]=aP[1]-1|0;aa[1]=e;return f}function
eA(a,b,c,d){var
e=a?a[1]:1,f=b?b[1]:kb,g=e?f<=aP[1]?1:0:e;return g?eb([0,c,d],al):ez(function(a){return ey(c,d)})}function
aQ(a,b,c,d){var
e=c[1][1];c[1]=d;eA(a,b,e,d);return c}function
bi(a,b,c){var
e=a?a[1]:0;if(e)return g(b,0);if(42>aP[1])return ez(function(a){return g(b,0)});var
d=g(c,0),f=d[1];eb([0,[0,[1,d[2]],0,0,0],d[3]],al);return f}function
eB(a,b){var
c=[0,b],d=H(a),e=d[1];switch(e[0]){case
1:return e[1]===bh?0:g(cC(aj,kd),eC);case
2:var
f=0===c[0]?[0,c[1]]:[1,c[1]];aQ(ke,0,d,f);return 0;default:return g(cC(aj,kc),eC)}}function
eD(a){var
f=[1,bh];function
g(a,b){var
h=b;for(;;){var
i=H(h),j=i[1];switch(j[0]){case
1:return a;case
2:var
k=j[1],c=k[3];if(typeof
c==="number")return 0===c?a:(i[1]=f,[0,k,a]);if(0===c[0])h=c[1];else{var
e=a,d=c[1];for(;;){if(!d)return e;var
l=d[2];e=g(e,d[1]);d=l}}break;default:return a}}}return bI(function(a){return eA(kf,0,a,f)},g(0,a))}function
L(a){return[0,[0,a]]}function
au(a){return[0,[1,a]]}function
bj(a){return[0,[2,[0,0,0,a,0]]]}function
cN(a){var
b=bj(1);return[0,b,b]}function
bU(a,b){var
d=H(b);if(d===a)return d;var
l=d[1];switch(l[0]){case
1:return aQ(kh,0,a,d[1]);case
2:var
e=l[1],c=a[1][1],h=ex(c[1],e[1]),i=c[4]+e[4]|0;if(42<i)var
k=0,j=cL(h);else
var
k=i,j=h;var
f=e[2],g=c[2],m=typeof
g==="number"?f:typeof
f==="number"?g:[0,g,f];c[1]=j;c[2]=m;c[4]=k;c[3]=e[3];d[1]=[3,a];return a;default:return aQ(kg,0,a,d[1])}}function
eE(a,b){var
d=H(a);function
e(a){var
c=bj([0,d]),f=aa[1];return[0,c,function(a){if(1===a[0]){aQ(ki,0,H(c),a);return 0}var
h=a[1];aa[1]=f;try{var
j=g(b,h),e=j}catch(f){var
d=s(f);if(!1)throw i(d,0);var
e=au(d)}bU(H(c),e);return 0}]}var
c=d[1];switch(c[0]){case
1:return[0,c];case
2:var
j=c[1],f=e(0),k=f[1];bg(j,f[2]);return k;default:var
h=c[1];return bi(kj,function(a){return g(b,h)},function(a){var
b=e(0);return[0,b[1],b[2],d[1]]})}}function
bk(d,b,c){var
e=H(b);function
f(a){var
b=bj([0,e]),f=aa[1];return[0,b,function(a){if(1===a[0]){var
l=a[1],m=H(b);aQ(kk,0,m,[1,g(d,l)]);return 0}var
j=a[1];aa[1]=f;try{var
k=g(c,j),h=k}catch(f){var
e=s(f);if(!1)throw i(e,0);var
h=au(g(d,e))}bU(H(b),h);return 0}]}var
a=e[1];switch(a[0]){case
1:return[0,[1,g(d,a[1])]];case
2:var
k=a[1],h=f(0),l=h[1];bg(k,h[2]);return l;default:var
j=a[1];return bi(kl,function(a){return g(c,j)},function(a){var
b=f(0);return[0,b[1],b[2],e[1]]})}}function
eF(a,b){try{var
n=g(a,0),h=n}catch(f){var
e=s(f);if(!1)throw i(e,0);var
h=au(e)}var
c=H(h);function
f(a){var
d=bj([0,c]),f=aa[1];return[0,d,function(a){if(1!==a[0]){aQ(km,0,H(d),a);return 0}var
h=a[1];aa[1]=f;try{var
j=g(b,h),e=j}catch(f){var
c=s(f);if(!1)throw i(c,0);var
e=au(c)}bU(H(d),e);return 0}]}var
d=c[1];switch(d[0]){case
1:var
k=d[1];return bi(kn,function(a){return g(b,k)},function(a){var
b=f(0);return[0,b[1],b[2],c[1]]});case
2:var
l=d[1],j=f(0),m=j[1];bg(l,j[2]);return m;default:return c}}function
cO(a,b){var
c=H(a)[1];switch(c[0]){case
1:if(c[1]===bh)return bi(kr,function(a){return cM(b,0)},function(a){return[0,0,function(a){return cM(b,0)},kq]});return;case
2:var
d=c[1],e=[1,aa[1],b],f=typeof
d[2]==="number"?e:[0,e,d[2]];d[2]=f;return;default:return}}function
aR(a){try{var
e=g(a,0),d=e}catch(f){var
c=s(f);if(!1)throw i(c,0);var
d=au(c)}var
b=H(d)[1];switch(b[0]){case
1:return g(bT[1],b[1]);case
2:return bg(b[1],function(a){return 1===a[0]?g(bT[1],a[1]):0});default:return 0}}var
cK=[];mM(cK,[0,cK,cK]);var
j_=dd;function
eG(a){var
b=cN(0),d=b[1],f=b[2],c=[0,0];function
h(a){return eB(f,a)}function
e(a,b){if(dd<a)var
d=a-dd,f=j_;else
var
d=0.,f=a;var
g=d===0.?h:function(a){return e(d,a)};c[1]=[0,Q.setTimeout(bA(g),f)];return 0}e(a*gx,0);cO(d,function(a){var
b=c[1];if(!b)return 0;var
d=b[1];c[1]=0;return Q.clearTimeout(d)});return d}function
eH(a){h.log(j(a))}bT[1]=function(a){eH(ks);eH(P(a));return ek(J)};function
eI(a,b){return b?[0,g(a,b[1])]:0}function
cP(f,b,c,d){var
k=[0,C],a=cN(0),l=a[1];function
m(a){var
c=k[1],b=1-(c==C?1:0);return b?g(c,0):b}var
q=a[2];cO(l,m);var
h=bS(function(a){m(0);eB(q,a);return!!1}),n=eI(g4,c),o=eI(g4,b);if(ev(d.addEventListener)){var
e={};if(o)e.capture=o[1];if(n)e.passive=n[1];d.addEventListener(f,h,e);var
p=function(a){return d.removeEventListener(f,h,e)}}else{var
i="on".concat(f),j=function(a){var
c=[0,h,a,[0]];return function(a,b){return mk(c,a,b)}};d.attachEvent(i,j);var
p=function(a){return d.detachEvent(i,j)}}k[1]=p;return l}function
cQ(o,b,c,d,e,f){var
p=b?b[1]:0,l=[0,0],m=[0,au([0,cs,kt])],i=[0,L(0)],k=cN(0)[1];cO(k,function(a){eD(m[1]);if(p)eD(i[1]);l[1]=1;return 0});function
n(a){if(l[1])return L(0);var
b=b1(o,c,d,e);m[1]=b;return eE(b,function(a){var
b=g(f,a);i[1]=eF(function(a){return g(b,k)},function(a){h.log(j(P(a)));return L(0)});return eE(i[1],n)})}aR(function(b){return eF(function(a){return n(b)},function(a){return a===bh?L(0):au(a)})});return k}Q.document;function
eJ(a){bI(function(a){var
g=a[2],c=a[1],b=E(q.cookie),e=ca+c+dn,i=d$(b,R(e,0));if(i)var
o=aO(b,0,d2(l(b),l(e)))===e?1:0,k=o||d$(b,59);else
var
k=i;var
m=1-k;if(m){var
d=new
cJ;d.setTime(d.getTime()+31536000000.);var
p=E(d.toUTCString());q.cookie=j(e4(f(kz),kx,c,g,p));var
n=h.log(j("Set default game setting "+c+cc+g))}else
var
n=m;return n},ky);h.log("Default game settings loaded")}function
am(a,b){try{var
d=ca+a+dn,c=ea(59,E(q.cookie));for(;;){if(c){var
f=c[2],e=d_(c[1]);if(l(d)>l(e)){c=f;continue}if(aO(e,0,l(d))!==d){c=f;continue}var
i=aO(e,l(d),l(e)-l(d)|0);try{var
k=l2(i);h.log(j("Loaded setting "+a+cc+i));var
g=k}catch(f){h.log(j("Invalid setting "+a+c_+bF(b)));var
g=b}}else{h.log(j("Setting "+a+gr+bF(b)));var
g=b}return g}}catch(f){h.log(j("Error loading setting "+a+c_+bF(b)));return b}}function
eK(a){return am(kA,e_)|0}function
eL(a){return am(kB,1.)}function
eM(a){return am(kD,2.)}function
cR(a){return am(kE,b9)}function
cS(a){return am(kF,b9)}var
eN=30.,aS=[0,a3],eO=2.,U=[0,0],eP=[0,0],cT=[0,0],bV=[0,1],es=mL(0),bQ=es.length-1,bf=A((bQ*8|0)+1|0),ep=bQ-1|0;function
ku(a,b,c){return cP(j9,a,b,c)}function
kv(a,b,c){return cP(lz,a,b,c)}function
kw(a,b,c){return cP(ly,a,b,c)}var
kI=0.,kJ=60.,j6=bK(cF),jV=0;if(ep>=0){var
aZ=jV;for(;;){lV(bf,aZ*8|0,bz(aW(es,aZ)[1+aZ]));var
jZ=aZ+1|0;if(ep===aZ)break;aZ=jZ}}az(bf,bQ*8|0,1);var
eq=em(bf);az(bf,bQ*8|0,2);var
er=em(bf),jW=bJ(er,8),jX=bJ(er,0),jY=bJ(eq,8);en(j6,bJ(eq,0),jY,jX,jW);var
bW=kK.slice();function
eQ(a,b){return a+bR(b-a)}function
bl(a){return bR(c4)<a?1:0}function
eR(a,b,c,d){return Math.sqrt((c-a)*(c-a)+(d-b)*(d-b))}function
eS(a,b,c){var
d=bR(gk),f=eL(0),e=aS[1]*f;return[0,a,b,c,Math.cos(d)*e,Math.sin(d)*e,0,0,eN,1.,0.,eQ(0.,eO),0]}function
eT(a){try{var
b=e7,c=dk;h.log(j(V(f(k2),b,c)));var
e=[0,b,c];return e}catch(f){var
d=s(f);h.log(j("Error setting up game area: "+P(d)));return k1}}function
bm(a){var
D=eT(0),v=0,w=eK(0)-1|0,L=bW.length-1,M=0;if(w<0)var
x=v;else{var
e=M,y=v;for(;;){var
p=g9(e,L),z=aW(bW,p)[1+p],A=z[2],B=z[1],C=[0,[0,B,A],y];h.log(j(e4(f(kM),e+1|0,p,B,A)));var
N=e+1|0;if(w===e){var
x=C;break}e=N;y=C}}var
g=x,c=0;for(;;){if(!g)break;var
E=[0,g[1],c];g=g[2];c=E}function
b(a,b){return eS(a+1|0,b[1],b[2])}var
u=0;if(c){var
m=c[2],q=c[1];if(m){var
F=m[2],G=m[1],H=b(u,q),r=[0,b(1,G),a2],l=r,k=1,d=2,i=F;for(;;){if(i){var
n=i[2],s=i[1];if(n){var
I=n[2],J=n[1],K=b(d,s),t=[0,b(d+1|0,J),a2];l[1+k]=[0,K,t];l=t;k=1;d=d+2|0;i=I;continue}l[1+k]=[0,b(d,s),0]}else
l[1+k]=0;var
o=[0,H,r];break}}else
var
o=[0,b(u,q),0]}else
var
o=0;return[0,o,D[1],D[2],kI,kJ,0,0,0.,1.,0,0.]}function
eU(a){var
e=a[12];if(e){var
b=e[1],h=a[2],i=j(g(f(k5),h));b.style.left=i;var
k=a[3],l=j(g(f(k6),k));b.style.top=l;var
m=a[8],n=j(g(f(k7),m));b.style.width=n;var
o=a[8],p=j(g(f(k8),o));b.style.height=p;switch(a[6]){case
0:var
d=k9,c=k_;break;case
1:var
d=k$,c=la;break;case
2:var
d=lb,c=lc;break;default:var
d=ld,c=le}b.style.backgroundColor=j(c);b.style.borderColor=j(d)}}var
bX=[0,0],cU=[0,0.],cV=[0,0.];function
k4(e,b,c,d){if(1===e[7])var
i=e;else{switch(e[6]){case
0:var
o=[0,e[4],e[5]];break;case
1:var
o=[0,e[4]*a0,e[5]*a0];break;case
2:var
o=[0,e[4],e[5]];break;default:var
Y=e[8]*5.,M=bc(function(a){var
b=0===a[6]?1:0,c=b?eR(e[2],e[3],a[2],a[3])<=Y?1:0:b;return c},d[1]);if(M)var
N=M[1],v=N[2]-e[2],w=N[3]-e[3],u=Math.sqrt(v*v+w*w),O=0.<u?[0,v/u,w/u]:kN,o=[0,O[1]*aS[1]*f1,O[2]*aS[1]*f1];else
var
o=[0,e[4],e[5]]}var
q=o[2],r=o[1],x=e[2]+r*c,y=e[3]+q*c;if(x<=0.)var
A=Math.abs(r),z=0.;else if(d[2]-e[8]<=x)var
A=-Math.abs(r),z=d[2]-e[8];else
var
A=r,z=x;if(y<=0.)var
C=Math.abs(q),B=0.;else if(d[3]-e[8]<=y)var
C=-Math.abs(q),B=d[3]-e[8];else
var
C=q,B=y;var
Z=2===e[6]?d2(e[8]+c*5.,cb):e[8],i=[0,e[1],z,B,A,C,e[6],e[7],Z,e[9],e[10],e[11],e[12]]}var
P=i[11]-c;if(P<=0.)var
Q=bR(gk),R=aS[1]*i[9],ae=i[12],af=eQ(1.,eO),l=[0,i[1],i[2],i[3],Math.cos(Q)*R,Math.sin(Q)*R,i[6],i[7],i[8],i[9],i[10],af,ae];else
var
l=[0,i[1],i[2],i[3],i[4],i[5],i[6],i[7],i[8],i[9],i[10],P,i[12]];h.log("Checking toxic collision...");var
_=d[6];h.log(j(g(f(k3),_)));a:{if(l[3]<=0.&&0===l[6]){var
aa=l[9]*a0,ab=l[8],$=1;if(bl(cR(0)))var
E=l[8]*1.5,D=2;else if(bl(cS(0)))var
E=l[8]*a0,D=3;else
var
E=ab,D=$;var
m=[0,l[1],l[2],l[3],l[4],l[5],D,l[7],E,aa,l[10],l[11],l[12]];break a}var
m=l}a:{if(d[3]<=m[3]&&1===m[7]&&0!==m[6]){var
k=[0,m[1],m[2],m[3],m[4],m[5],0,m[7],eN,1.,m[10],m[11],m[12]];break a}var
k=m}a:{if(0===k[6]&&1!==k[7]){if(!d5(function(a){var
b=0!==a[6]?1:0,c=b?a[1]!==k[1]?1:0:b;if(c){var
f=eR(k[2],k[3],a[2],a[3]),d=f<(k[8]+a[8])/2.?1:0;if(d)return bl(eM(0));var
e=d}else
var
e=c;return e},b)){var
L=k;break a}var
ad=k[9]*a0,ac=1;if(bl(cR(0)))var
G=k[8],F=2;else if(bl(cS(0)))var
G=k[8]*a0,F=3;else
var
G=k[8],F=ac;var
L=[0,k[1],k[2],k[3],k[4],k[5],F,k[7],G,ad,k[10],k[11],k[12]];break a}var
L=k}var
a=L,p=b;for(;;){if(!p)return a;var
X=p[2],n=p[1];if(a[1]!==n[1]&&1!==a[7]&&1!==n[7]){var
S=a[2]-n[2],T=a[3]-n[3];if(Math.sqrt(S*S+T*T)<(a[8]+n[8])/2.){var
H=a[2]-n[2],I=a[3]-n[3],s=Math.sqrt(H*H+I*I);if(0.<s){var
J=H/s,K=I/s,U=(a[4]-n[4])*J+(a[5]-n[5])*K;if(0.<U)var
V=2.*U/2.,W=((a[8]+n[8])/2.-s)/2.,t=[0,[0,a[1],a[2]+J*W,a[3]+K*W,a[4]-V*J,a[5]-V*K,a[6],a[7],a[8],a[9],a[10],a[11],a[12]]];else
var
t=[0,a]}else
var
t=[0,a]}else
var
t=[0,a];a=t[1];p=X;continue}p=X}}function
eV(c){try{var
F=c[3],G=c[2],H=c[1];h.log(j(b1(f(lk),H,G,F)));var
l=q.getElementById(b5);if(l==C){h.log("Game area not found for creet");var
m=c}else{h.log("Game area found, creating element...");try{var
a=q.createElement("div");a.className="creet";var
p=c[3],r=c[2],t=c[1];h.log(j(b1(f(kO),t,r,p)));var
u=c[2],v=j(g(f(kP),u));a.style.left=v;var
w=c[3],x=j(g(f(kQ),w));a.style.top=x;var
y=c[8],z=j(g(f(kR),y));a.style.width=z;var
A=c[8],B=j(g(f(kS),A));a.style.height=B;a.style.position="absolute";a.style.borderRadius="50%";a.style.border="2px solid";a.style.cursor="pointer";a.style.setProperty("transition","all 0.1s ease",eu);switch(c[6]){case
0:var
d=kT,b=kU;break;case
1:var
d=kV,b=kW;break;case
2:var
d=kX,b=kY;break;default:var
d=kZ,b=k0}a.style.backgroundColor=j(b);a.style.borderColor=j(d);var
D=[0,a],e=D}catch(f){var
o=s(f);h.log(j("Error creating creet element: "+P(o)));var
e=0}if(e){var
k=e[1],I=c[1];h.log(j(g(f(ll),I)));l.appendChild(k);aR(function(a){return bk(function(a){try{throw i(a,0)}catch(f){var
b=s(f);return b}},cQ(ku,0,0,0,k,function(a,b){var
B=c[1];h.log(j(g(f(lj),B)));var
e=c[1];try{h.log(j(g(f(lf),e)));bX[1]=[0,e];var
i=a.clientX,k=a.clientY,l=q.getElementById(b5);if(l==C)h.log("Game area not found for drag start");else{var
m=l.getBoundingClientRect(),n=m.left,o=m.top,p=i-n,r=k-o;h.log(j(lI(f(lg),i,k,n,o,p,r)));var
t=U[1];if(t){var
u=bc(function(a){return a[1]===e?1:0},t[1][1]);if(u){var
d=u[1];cU[1]=p-d[2];cV[1]=r-d[3];var
w=cV[1],x=cU[1],y=d[3],z=d[2],A=d[1];h.log(j(b0(f(lh),A,z,y,x,w)))}else
h.log("Creet not found for drag start")}else
h.log("No game state for drag start")}}catch(f){var
v=s(f);h.log(j("Error starting drag: "+P(v)))}return L(0)}),function(a){return L(0)})});var
J=c[1];h.log(j(g(f(lm),J)));var
n=[0,c[1],c[2],c[3],c[4],c[5],c[6],c[7],c[8],c[9],c[10],c[11],[0,k]]}else{var
K=c[1];h.log(j(g(f(ln),K)));var
n=c}var
m=n}return m}catch(f){var
E=s(f);h.log(j("Error adding creet to DOM: "+P(E)));return c}}function
bY(c,b){return bV[1]?bk(function(a){try{throw i(a,0)}catch(f){var
b=s(f);return b}},eG(dp),function(a){var
d=U[1];if(d){var
b=d[1];if(b[6]){var
e=bc(function(a){return a[1]===c?1:0},b[1]);if(e){var
f=e[1];if(1!==f[7]){var
g=k4(f,b[1],dp,b),h=bH(function(a){return a[1]===c?g:a},b[1]);U[1]=[0,[0,h,b[2],b[3],b[4],b[5],b[6],b[7],b[8],b[9],b[10],b[11]]];eU(g);return bY(c,0)}}return bY(c,0)}}return bY(c,0)}):L(0)}function
eW(a){var
c=a[1];h.log(j(g(f(lo),c)));var
b=bY(a[1],0);cT[1]=[0,b,cT[1]];return b}function
eX(a){h.log("Stopping all Creet Lwt threads");bV[1]=0;cT[1]=0}function
eY(_,b){return bV[1]?bk(function(a){try{throw i(a,0)}catch(f){var
b=s(f);return b}},eG(dp),function(a){var
D=U[1];if(!D)return L(0);var
b=D[1];if(!b[6])return L(0);function
E(a){h.log(j("Game coordination loop error: "+P(a)));return L(0)}function
N(a){try{throw i(a,0)}catch(f){var
b=s(f);return b}}try{var
F=(new
cJ).getTime(),p=(F-_)/gx,G=cw(function(a){return 0===a[6]?1:0},b[1]),r=0===ar(G)?1:0,l=b[9]+0.1*p,$=l<2.?b9:l<4.?a3:l<6.?c4:b6,ab=aS[1]+$*p,T=cw(function(a){return 0===a[6]?1:0},b[1]),W=b[8]-b[11];a:{if(0<ar(T)&&am(kH,e_)<=W){var
k=bW.length-1,X=ar(b[1])+1|0;b:{var
O=bK(cF);if(dc>=k&&0<k){for(;;){var
u=g2(a_(O))&dc,v=g9(u,k);if(((dc-k|0)+1|0)>=(u-v|0))break}var
d=v;break b}var
d=aj(j0)}var
B=aW(bW,d)[1+d],n=B[2],o=B[1];h.log(j(b1(f(kL),d,o,n)));var
Y=eS(X,o,n);h.log(j(V(f(lq),o,n)));var
Z=eV(Y),c=[0,[0,Z,b[1]],b[2],b[3],b[4],b[5],,,,,b[10],b[8]];break a}var
c=b}var
ac=ar(b[1]);if(ac<ar(c[1]))bI(function(b){return aR(function(a){return eW(b)})},cw(function(c){return 1-d5(function(a){return a[1]===c[1]?1:0},b[1])},c[1]));var
t=[0,c[1],c[2],c[3],c[4],c[5],1-r,r,b[8]+p,l,c[10],c[11]];aS[1]=ab;U[1]=[0,t];var
I=q.getElementById("score");if(1-(I==C?1:0)){var
ad=ar(G);I.innerHTML=j(g(f(lr),ad))}var
J=q.getElementById(fH);if(1-(J==C?1:0)){var
ae=t[8];J.innerHTML=j(g(f(ls),ae))}var
K=q.getElementById("difficulty");if(1-(K==C?1:0)){var
af=t[9];K.innerHTML=j(g(f(lt),af))}if(r){h.log("Game Over! Stopping all Creet threads.");eX(0);var
M=q.getElementById(df);if(1-(M==C?1:0))M.style.display=fh;var
A=L(0)}else
var
A=eY(F,0);var
x=A}catch(f){var
w=s(f);if(!1)throw i(w,0);var
x=au(w)}var
e=H(x);function
y(a){var
b=bj([0,e]),f=aa[1];return[0,b,function(a){if(1!==a[0]){aQ(ko,0,H(b),a);return 0}var
e=a[1];aa[1]=f;try{var
g=E(e),d=g}catch(f){var
c=s(f);if(!1)throw i(c,0);var
d=au(N(c))}bU(H(b),d);return 0}]}var
m=e[1];switch(m[0]){case
1:var
Q=m[1];return bi(kp,function(a){return E(N(Q))},function(a){var
b=y(0);return[0,b[1],b[2],e[1]]});case
2:var
R=m[1],z=y(0),S=z[1];bg(R,z[2]);return S;default:return e}}):L(0)}function
cW(a){try{h.log("Starting H42N42 game...");var
b=bm(0),l=ar(b[1]);h.log(j(g(f(lu),l)));h.log("Adding Creets to DOM...");var
c=bH(eV,b[1]),m=ar(c);h.log(j(g(f(lv),m)));U[1]=[0,[0,c,b[2],b[3],b[4],b[5],1,b[7],b[8],b[9],b[10],b[11]]];var
d=q.querySelector(fJ);if(1-(d==C?1:0))d.style.display=dw;var
e=q.getElementById("game-container");if(1-(e==C?1:0))e.style.display=fh;h.log("Starting individual Lwt threads for each Creet...");var
i=ar(c);h.log(j(g(f(lp),i)));bV[1]=1;bI(function(b){return aR(function(a){return eW(b)})},c);var
n=(new
cJ).getTime();aR(function(a){return eY(n,0)});var
o=h.log("Game started successfully with Lwt threads!");return o}catch(f){var
k=s(f);return h.log(j("Error starting game: "+P(k)))}}function
eZ(a){try{h.log("Stopping game and all Lwt threads...");eX(0);var
c=eP[1];if(c)Q.clearTimeout(c[1]);eP[1]=0;var
d=U[1];if(d){var
b=d[1];U[1]=[0,[0,b[1],b[2],b[3],b[4],b[5],0,b[7],b[8],b[9],b[10],b[11]]]}var
e=q.querySelector(fJ);if(1-(e==C?1:0))e.style.display="flex";var
f=q.getElementById(df);if(1-(f==C?1:0))f.style.display=dw;var
i=h.log("Game stopped");return i}catch(f){var
g=s(f);return h.log(j("Error stopping game: "+P(g)))}}function
e0(a){try{h.log("Restarting game...");eZ(0);var
c=q.getElementById(b5);if(1-(c==C?1:0))c.innerHTML=e;var
d=q.getElementById(df);if(1-(d==C?1:0))d.style.display=dw;aS[1]=am(lw,a3);var
f=am(lx,1.),b=bm(0);U[1]=[0,[0,b[1],b[2],b[3],b[4],b[5],b[6],b[7],b[8],f,b[10],b[11]]];cW(0);h.log("Game restarted successfully with default settings!");return}catch(f){var
g=s(f);h.log(j("Error restarting game: "+P(g)));return}}function
e1(a){try{h.log("Initializing game controls...");var
b=q.getElementById("start-game");if(1-(b==C?1:0))b.onclick=bS(function(a){cW(0);return cI});var
c=q.getElementById("restart-game");if(1-(c==C?1:0))c.onclick=bS(function(a){e0(0);return cI});var
d=q.getElementById("restart-game-over");if(1-(d==C?1:0))d.onclick=bS(function(a){e0(0);return cI});aR(function(a){return bk(function(a){try{throw i(a,0)}catch(f){var
b=s(f);return b}},cQ(kw,0,0,0,q,function(a,b){var
f=bX[1];a:{var
o=a.clientX,p=a.clientY;if(f){var
d=f[1],g=U[1];if(g){var
c=g[1],i=q.getElementById(b5);if(i==C)h.log("Game area not found for drag update");else{var
j=i.getBoundingClientRect(),l=o-j.left-cU[1],m=p-j.top-cV[1],e=bH(function(a){return a[1]===d?[0,a[1],l,m,a[4],a[5],a[6],1,a[8],a[9],a[10],a[11],a[12]]:a},c[1]),n=bc(function(a){return a[1]===d?1:0},e);U[1]=[0,[0,e,c[2],c[3],c[4],c[5],c[6],c[7],c[8],c[9],n,c[11]]];var
k=bc(function(a){return a[1]===d?1:0},e);if(k)eU(k[1])}break a}}}return L(0)}),function(a){return L(0)})});aR(function(a){return bk(function(a){try{throw i(a,0)}catch(f){var
b=s(f);return b}},cQ(kv,0,0,0,q,function(a,b){var
d=bX[1];if(d){var
e=d[1];h.log(j(g(f(li),e)));var
i=U[1];if(i){var
c=i[1],k=bH(function(a){return a[1]===e?[0,a[1],a[2],a[3],a[4],a[5],a[6],0,a[8],a[9],a[10],a[11],a[12]]:a},c[1]);U[1]=[0,[0,k,c[2],c[3],c[4],c[5],c[6],c[7],c[8],c[9],0,c[11]]]}bX[1]=0}return L(0)}),function(a){return L(0)})});h.log("Game controls and drag handlers initialized");return}catch(f){var
e=s(f);h.log(j("Error initializing game controls: "+P(e)));return}}var
bZ={getSettings:a9(function(a){var
e=0;try{var
c=ca+bt+dn;a:{b:{var
b=ea(59,E(q.cookie));for(;;){if(!b)break;var
f=b[2],d=d_(b[1]);if(l(c)<=l(d)){if(aO(d,0,l(c))===c)break b;b=f}else
b=f}h.log(j("Bool setting "+bt+gr+cv(e)));var
i=e;break a}var
g=aO(d,l(c),l(d)-l(c)|0);h.log(j("Loaded bool setting "+bt+cc+g));var
i=g===fz?1:0}var
k=i}catch(f){h.log(j("Error loading bool setting "+bt+c_+cv(e)));var
k=e}var
n=am(kG,3.),o=cS(0),p=cR(0),r=eM(0),m=am(kC,1.),s=eL(0);return{initialCreets:eK(0),gameSpeed:s,difficultyProgression:m,infectionRate:r,berserkChance:p,meanChance:o,healTime:n,autoHeal:k}}),setupGameArea:a9(function(a){return eT}),initGameState:a9(function(a){return bm}),startGame:a9(function(a){return cW}),stopGame:a9(function(a){return eZ}),getCurrentState:a9(function(a,b){return U[1]})};a:{if(E(typeof
bZ)==="function"&&0<bZ.length){var
ew=bA(bZ);break a}var
ew=bZ}mO.H42N42GameDebug=ew;h.log("H42N42 Game Logic loaded");if(E(q.readyState)===e$){var
e2=function(a){if(E(q.readyState)===e$){Q.setTimeout(bA(e2),b9);return 0}h.log("Game DOM ready, initializing...");eJ(0);bm(0);if(cH(q.querySelector(fL)))e1(0);return 0};e2(0)}else{h.log("Game DOM already loaded");eJ(0);bm(0);if(cH(q.querySelector(fL)))e1(0)}bG(0);return}(globalThis));
