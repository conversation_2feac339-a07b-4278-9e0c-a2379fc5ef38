// Generated by js_of_ocaml
//# buildInfo:effects=false, kind=exe, use-js-string=true, version=5.9.1
(function(a){typeof
globalThis!=="object"&&(this?b():(a.defineProperty(a.prototype,"_T_",{configurable:true,get:b}),_T_));function
b(){var
b=this||self;b.globalThis=b;delete
a.prototype._T_}}(Object));(function(c){"use strict";var
h3=typeof
module==="object"&&module.exports||c,dm="Sys_error",U=0x80,ad=" ",L="0",dB="ENOTEMPTY",b1="compare: functional value",dO="EEXIST",bY=1255,aU=1000,b0="mkdir",dH="@[",bO=" : flags Open_text and Open_binary are not compatible",dc="reduce-motion",dl="sunset",bU="e",dx="%u",dG="/static/",bN=": Not a directory",bT="ENOENT",dA="Stack_overflow",b6="node:fs",da="%i",db="dir",dX="%ni",af=0xff,dN="Undefined_recursive_module",dF="Assert_failure",c$="0x",Z=0x8000,dk=0x800,dw=0x7ff0,dz=-48,c0="error",c1=" : is a directory",c_=0xdfff,di="@{",dj="Division_by_zero",av=".",dv="End_of_file",cZ="nature",c9=": closedir failed",aF=0x3f,dW="Out_of_memory",c8="Not_found",bS=" : file already exists",b5="Failure",aS=128,aV=": No such file or directory",bd="Unix.Unix_error",du="^",dh=255,bW="ENOTDIR",dM=256,dg=100,bM="index out of bounds",ae="/",ba=252,c7="%Li",dV="neon",c6="Invalid_argument",bb=254,aE="+",bL="%d",dU=", characters ",cY=": file descriptor already closed",au="-",bK="EBADF",ac=0xffffff,bR="true",cX="loading",dE=120,dT="dark",dt="theme",dD=1027,aT=1024,dS="theme-",df=-97,ds="Pervasives.do_at_exit",dL="Printexc.handle_uncaught_exception",b4="h42n42_",cW=12520,dr=65536,b3=" : flags Open_rdonly and Open_wronly are not compatible",bV=0xf,c4=", ",c5=512,dy="Fatal error: exception %s\n",dQ="Match_failure",dR="closedir",c2="%li",c3=1026,dK="high-contrast",cV=250,bQ="nan",f="",bP="rmdir",de="([^/]+)",bZ="Fatal error: exception ",dq="infinity",bX="jsError",a="camlinternalFormat.ml",cT="-theme",cU=103,a$='"',dp="fd ",_=0xffff,b2=127,dd="ocean",cS=0xdc00,dJ="Sys_blocked_io",dC="active",w=248,dI="_",dP=0xe0,dn=0xf0,bc="_bigarr02";function
hf(a,b,c){if(a[1]===b){a[1]=c;return 1}return 0}function
b7(a){return a[1]}function
bp(a,b,c){var
d=String.fromCharCode;if(b===0&&c<=4096&&c===a.length)return d.apply(null,a);var
e=f;for(;0<c;b+=aT,c-=aT)e+=d.apply(null,a.slice(b,b+Math.min(c,aT)));return e}function
bg(a){var
c=new
Uint8Array(a.l),e=a.c,d=e.length,b=0;for(;b<d;b++)c[b]=e.charCodeAt(b);for(d=a.l;b<d;b++)c[b]=0;a.c=c;a.t=4;return c}function
ao(a,b,c,d,e){if(e===0)return 0;if(d===0&&(e>=c.l||c.t===2&&e>=c.c.length)){c.c=a.t===4?bp(a.c,b,e):b===0&&a.c.length===e?a.c:a.c.slice(b,b+e);c.t=c.c.length===c.l?0:2}else if(c.t===2&&d===c.c.length){c.c+=a.t===4?bp(a.c,b,e):b===0&&a.c.length===e?a.c:a.c.slice(b,b+e);c.t=c.c.length===c.l?0:2}else{if(c.t!==4)bg(c);var
g=a.c,h=c.c;if(a.t===4)if(d<=b)for(var
f=0;f<e;f++)h[d+f]=g[b+f];else
for(var
f=e-1;f>=0;f--)h[d+f]=g[b+f];else{var
i=Math.min(e,g.length-b);for(var
f=0;f<i;f++)h[d+f]=g.charCodeAt(b+f);for(;f<e;f++)h[d+f]=0}}return 0}function
aJ(a,b){if(a===0)return f;if(b.repeat)return b.repeat(a);var
d=f,c=0;for(;;){if(a&1)d+=b;a>>=1;if(a===0)return d;b+=b;c++;if(c===9)b.slice(0,1)}}function
bh(a){if(a.t===2)a.c+=aJ(a.l-a.c.length,"\0");else
a.c=bp(a.c,0,a.c.length);a.t=0}function
cm(a){if(a.length<24){for(var
b=0;b<a.length;b++)if(a.charCodeAt(b)>b2)return false;return true}else
return!/[^\x00-\x7f]/.test(a)}function
eo(a){for(var
k=f,d=f,h,g,i,b,c=0,j=a.length;c<j;c++){g=a.charCodeAt(c);if(g<U){for(var
e=c+1;e<j&&(g=a.charCodeAt(e))<U;e++);if(e-c>c5){d.slice(0,1);k+=d;d=f;k+=a.slice(c,e)}else
d+=a.slice(c,e);if(e===j)break;c=e}b=1;if(++c<j&&((i=a.charCodeAt(c))&-64)===aS){h=i+(g<<6);if(g<dP){b=h-0x3080;if(b<U)b=1}else{b=2;if(++c<j&&((i=a.charCodeAt(c))&-64)===aS){h=i+(h<<6);if(g<dn){b=h-0xe2080;if(b<dk||b>=0xd7ff&&b<0xe000)b=2}else{b=3;if(++c<j&&((i=a.charCodeAt(c))&-64)===aS&&g<0xf5){b=i-0x3c82080+(h<<6);if(b<0x10000||b>0x10ffff)b=3}}}}}if(b<4){c-=b;d+="\ufffd"}else if(b>_)d+=String.fromCharCode(0xd7c0+(b>>10),cS+(b&0x3ff));else
d+=String.fromCharCode(b);if(d.length>aT){d.slice(0,1);k+=d;d=f}}return k+d}function
ag(a,b,c){this.t=a;this.c=b;this.l=c}ag.prototype.toString=function(){switch(this.t){case
9:case
8:return this.c;case
4:case
2:bh(this);case
0:if(cm(this.c))this.t=9;else
this.t=8;return this.c}};ag.prototype.toUtf16=function(){var
a=this.toString();if(this.t===9)return a;return eo(a)};ag.prototype.slice=function(){var
a=this.t===4?this.c.slice():this.c;return new
ag(this.t,a,this.l)};function
d5(a){return new
ag(0,a,a.length)}function
aq(a){return a}function
ah(a){return d5(aq(a))}function
bf(a,b,c,d,e){ao(ah(a),b,c,d,e);return 0}var
eq={};function
h5(a){if(eq[a])return eq[a];var
b=c.process;if(b&&b.env&&b.env[a]!==undefined)return b.env[a];if(c.jsoo_env&&typeof
c.jsoo_env[a]==="string")return c.jsoo_env[a]}var
bn=0;(function(){var
c=h5("OCAMLRUNPARAM");if(c!==undefined){var
b=c.split(",");for(var
a=0;a<b.length;a++)if(b[a]==="b"){bn=1;break}else if(b[a].startsWith("b="))bn=+b[a].slice(2);else
continue}}());var
hX=bn,C=[0];function
hp(a,b){if(!a.js_error||b||a[0]===w)a.js_error=new
c.Error("Js exception containing backtrace");return a}function
h(a,b){return bn&&hX?hp(a,b):a}function
hW(a,b){throw h([0,a,b])}function
R(a){return a}function
cj(a,b){hW(a,R(b))}function
t(a){cj(C.Invalid_argument,a)}function
hj(){t(bM)}function
p(a,b,c){c&=af;if(a.t!==4){if(b===a.c.length){a.c+=String.fromCharCode(c);if(b+1===a.l)a.t=0;return 0}bg(a)}a.c[b]=c;return 0}function
ax(a,b,c){if(b>>>0>=a.l)hj();return p(a,b,c)}function
aX(a,b){switch(a.t&6){case
0:return a.c.charCodeAt(b);case
2:if(b>=a.c.length)return 0;return a.c.charCodeAt(b);case
4:return a.c[b]}}function
ap(d,c){var
f=d.l>=0?d.l:d.l=d.length,e=c.length,b=f-e;if(b===0)return d.apply(null,c);else if(b<0){var
a=d.apply(null,c.slice(0,f));if(typeof
a!=="function")return a;return ap(a,c.slice(f))}else{switch(b){case
1:{var
a=function(a){var
f=new
Array(e+1);for(var
b=0;b<e;b++)f[b]=c[b];f[e]=a;return d.apply(null,f)};break}case
2:{var
a=function(a,b){var
g=new
Array(e+2);for(var
f=0;f<e;f++)g[f]=c[f];g[e]=a;g[e+1]=b;return d.apply(null,g)};break}default:var
a=function(){var
e=arguments.length===0?1:arguments.length,b=new
Array(c.length+e);for(var
a=0;a<c.length;a++)b[a]=c[a];for(var
a=0;a<arguments.length;a++)b[c.length+a]=arguments[a];return ap(d,b)}}a.l=b;return a}}function
be(){t(bM)}function
d6(a,b){if(b>>>0>=a.length-1)be();return a}function
hl(a){if(Number.isFinite(a)){if(Math.abs(a)>=2.2250738585072014e-308)return 0;if(a!==0)return 1;return 2}return Number.isNaN(a)?4:3}function
hn(){return[0]}function
y(a){if(a<0)t("Bytes.create");return new
ag(a?2:9,f,a)}function
hq(a,b,c,d){if(c>0)if(b===0&&(c>=a.l||a.t===2&&c>=a.c.length))if(d===0){a.c=f;a.t=2}else{a.c=aJ(c,String.fromCharCode(d));a.t=c===a.l?0:2}else{if(a.t!==4)bg(a);for(c+=b;b<c;b++)a.c[b]=d}return 0}function
ci(a){a=aq(a);var
e=a.length;if(e>31)t("format_int: format too long");var
b={justify:aE,signstyle:au,filler:ad,alternate:false,base:0,signedconv:false,width:0,uppercase:false,sign:1,prec:-1,conv:"f"};for(var
d=0;d<e;d++){var
c=a.charAt(d);switch(c){case"-":b.justify=au;break;case"+":case" ":b.signstyle=c;break;case"0":b.filler=L;break;case"#":b.alternate=true;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":b.width=0;while(c=a.charCodeAt(d)-48,c>=0&&c<=9){b.width=b.width*10+c;d++}d--;break;case".":b.prec=0;d++;while(c=a.charCodeAt(d)-48,c>=0&&c<=9){b.prec=b.prec*10+c;d++}d--;break;case"d":case"i":b.signedconv=true;b.base=10;break;case"u":b.base=10;break;case"x":b.base=16;break;case"X":b.base=16;b.uppercase=true;break;case"o":b.base=8;break;case"e":case"f":case"g":b.signedconv=true;b.conv=c;break;case"E":case"F":case"G":b.signedconv=true;b.uppercase=true;b.conv=c.toLowerCase();break}}return b}function
b$(a,b){if(a.uppercase)b=b.toUpperCase();var
e=b.length;if(a.signedconv&&(a.sign<0||a.signstyle!==au))e++;if(a.alternate){if(a.base===8)e+=1;if(a.base===16)e+=2}var
c=f;if(a.justify===aE&&a.filler===ad)for(var
d=e;d<a.width;d++)c+=ad;if(a.signedconv)if(a.sign<0)c+=au;else if(a.signstyle!==au)c+=a.signstyle;if(a.alternate&&a.base===8)c+=L;if(a.alternate&&a.base===16)c+=a.uppercase?"0X":c$;if(a.justify===aE&&a.filler===L)for(var
d=e;d<a.width;d++)c+=L;c+=b;if(a.justify===au)for(var
d=e;d<a.width;d++)c+=ad;return R(c)}function
ca(a,b){function
j(a,b){if(Math.abs(a)<1.0)return a.toFixed(b);else{var
c=Number.parseInt(a.toString().split(aE)[1]);if(c>20){c-=20;a/=Math.pow(10,c);a+=new
Array(c+1).join(L);if(b>0)a=a+av+new
Array(b+1).join(L);return a}else
return a.toFixed(b)}}var
c,f=ci(a),e=f.prec<0?6:f.prec;if(b<0||b===0&&1/b===Number.NEGATIVE_INFINITY){f.sign=-1;b=-b}if(Number.isNaN(b)){c=bQ;f.filler=ad}else if(!Number.isFinite(b)){c="inf";f.filler=ad}else
switch(f.conv){case"e":var
c=b.toExponential(e),d=c.length;if(c.charAt(d-3)===bU)c=c.slice(0,d-1)+L+c.slice(d-1);break;case"f":c=j(b,e);break;case"g":e=e?e:1;c=b.toExponential(e-1);var
i=c.indexOf(bU),h=+c.slice(i+1);if(h<-4||b>=1e21||b.toFixed(0).length>e){var
d=i-1;while(c.charAt(d)===L)d--;if(c.charAt(d)===av)d--;c=c.slice(0,d+1)+c.slice(i);d=c.length;if(c.charAt(d-3)===bU)c=c.slice(0,d-1)+L+c.slice(d-1);break}else{var
g=e;if(h<0){g-=h+1;c=b.toFixed(g)}else
while(c=b.toFixed(g),c.length>e+1)g--;if(g){var
d=c.length-1;while(c.charAt(d)===L)d--;if(c.charAt(d)===av)d--;c=c.slice(0,d+1)}}break}return b$(f,c)}function
bi(a,b){if(aq(a)===bL)return R(f+b);var
c=ci(a);if(b<0)if(c.signedconv){c.sign=-1;b=-b}else
b>>>=0;var
d=b.toString(c.base);if(c.prec>=0){c.filler=ad;var
e=c.prec-d.length;if(e>0)d=aJ(e,L)+d}return b$(c,d)}var
hU=0;function
d$(){return hU++}function
ay(a){if(!C.Failure)C.Failure=[w,R(b5),-3];cj(C.Failure,a)}function
j(a){if(cm(a))return a;return eo(a)}function
a2(){return typeof
c.process!=="undefined"&&typeof
c.process.versions!=="undefined"&&typeof
c.process.versions.node!=="undefined"}function
h7(){function
a(a){if(a.charAt(0)===ae)return[f,a.slice(1)];return}function
b(a){var
h=/^([a-zA-Z]:|[\\/]{2}[^\\/]+[\\/]+[^\\/]+)?([\\/])?([\s\S]*?)$/,b=h.exec(a),c=b[1]||f,e=c.length>0&&c.charAt(1)!==":";if(b[2]||e){var
d=b[1]||f,g=b[2]||f;return[d,a.slice(d.length+g.length)]}return}return a2()&&c.process&&c.process.platform?c.process.platform==="win32"?b:a:a}var
cn=h7();function
em(a){return a.slice(-1)!==ae?a+ae:a}if(a2()&&c.process&&c.process.cwd)var
aY=c.process.cwd().replace(/\\/g,ae);else
var
aY="/static";aY=em(aY);function
hN(a){a=j(a);if(!cn(a))a=aY+a;var
e=cn(a),d=e[1].split(/[/\\]/),b=[];for(var
c=0;c<d.length;c++)switch(d[c]){case"..":if(b.length>1)b.pop();break;case".":break;case"":break;default:b.push(d[c]);break}b.unshift(e[0]);b.orig=a;return b}function
h2(a){for(var
g=f,c=g,b,i,d=0,h=a.length;d<h;d++){b=a.charCodeAt(d);if(b<U){for(var
e=d+1;e<h&&(b=a.charCodeAt(e))<U;e++);if(e-d>c5){c.slice(0,1);g+=c;c=f;g+=a.slice(d,e)}else
c+=a.slice(d,e);if(e===h)break;d=e}if(b<dk){c+=String.fromCharCode(0xc0|b>>6);c+=String.fromCharCode(U|b&aF)}else if(b<0xd800||b>=c_)c+=String.fromCharCode(dP|b>>12,U|b>>6&aF,U|b&aF);else if(b>=0xdbff||d+1===h||(i=a.charCodeAt(d+1))<cS||i>c_)c+="\xef\xbf\xbd";else{d++;b=(b<<10)+i-0x35fdc00;c+=String.fromCharCode(dn|b>>18,U|b>>12&aF,U|b>>6&aF,U|b&aF)}if(c.length>aT){c.slice(0,1);g+=c;c=f}}return g+c}function
I(a){return cm(a)?R(a):R(h2(a))}var
h8=["E2BIG","EACCES","EAGAIN",bK,"EBUSY","ECHILD","EDEADLK","EDOM",dO,"EFAULT","EFBIG","EINTR","EINVAL","EIO","EISDIR","EMFILE","EMLINK","ENAMETOOLONG","ENFILE","ENODEV",bT,"ENOEXEC","ENOLCK","ENOMEM","ENOSPC","ENOSYS",bW,dB,"ENOTTY","ENXIO","EPERM","EPIPE","ERANGE","EROFS","ESPIPE","ESRCH","EXDEV","EWOULDBLOCK","EINPROGRESS","EALREADY","ENOTSOCK","EDESTADDRREQ","EMSGSIZE","EPROTOTYPE","ENOPROTOOPT","EPROTONOSUPPORT","ESOCKTNOSUPPORT","EOPNOTSUPP","EPFNOSUPPORT","EAFNOSUPPORT","EADDRINUSE","EADDRNOTAVAIL","ENETDOWN","ENETUNREACH","ENETRESET","ECONNABORTED","ECONNRESET","ENOBUFS","EISCONN","ENOTCONN","ESHUTDOWN","ETOOMANYREFS","ETIMEDOUT","ECONNREFUSED","EHOSTDOWN","EHOSTUNREACH","ELOOP","EOVERFLOW"];function
al(a,b,c,d){var
e=h8.indexOf(a);if(e<0){if(d==null)d=-9999;e=[0,d]}var
g=[e,I(b||f),I(c||f)];return g}var
ej={};function
as(a){return ej[a]}function
ak(a,b){throw h([0,a].concat(b))}function
cg(a){return a
instanceof
ag}function
ch(a){return typeof
a==="string"&&!/[^\x00-\xff]/.test(a)}function
b9(a){if(!(a
instanceof
Uint8Array))a=new
Uint8Array(a);return new
ag(4,a,a.length)}function
e(a){cj(C.Sys_error,a)}function
ek(a){e(a+aV)}function
en(a){if(a.t!==4)bg(a);return a.c}function
W(a){return a.l}function
dY(){}function
F(a){this.data=a}F.prototype=new
dY();F.prototype.constructor=F;F.prototype.truncate=function(a){var
b=this.data;this.data=y(a|0);ao(b,0,this.data,0,a)};F.prototype.length=function(){return W(this.data)};F.prototype.write=function(a,b,c,d){var
e=this.length();if(a+d>=e){var
f=y(a+d),g=this.data;this.data=f;ao(g,0,this.data,0,e)}ao(b9(b),c,this.data,a,d);return 0};F.prototype.read=function(a,b,c,d){var
e=this.length();if(a+d>=e)d=e-a;if(d){var
f=y(d|0);ao(this.data,a,f,0,d);b.set(en(f),c)}return d};function
aw(a,b,c){this.file=b;this.name=a;this.flags=c}aw.prototype.err_closed=function(){e(this.name+cY)};aw.prototype.length=function(){if(this.file)return this.file.length();this.err_closed()};aw.prototype.write=function(a,b,c,d){if(this.file)return this.file.write(a,b,c,d);this.err_closed()};aw.prototype.read=function(a,b,c,d){if(this.file)return this.file.read(a,b,c,d);this.err_closed()};aw.prototype.close=function(){this.file=undefined};function
x(a,b){this.content={};this.root=a;this.lookupFun=b}x.prototype.nm=function(a){return this.root+a};x.prototype.create_dir_if_needed=function(a){var
d=a.split(ae),c=f;for(var
b=0;b<d.length-1;b++){c+=d[b]+ae;if(this.content[c])continue;this.content[c]=Symbol("directory")}};x.prototype.slash=function(a){return/\/$/.test(a)?a:a+ae};x.prototype.lookup=function(a){if(!this.content[a]&&this.lookupFun){var
b=this.lookupFun(R(this.root),R(a));if(b!==0){this.create_dir_if_needed(a);this.content[a]=new
F(ah(b[1]))}}};x.prototype.exists=function(a,b){if(a===f)return 1;var
c=this.slash(a);if(this.content[c])return 1;if(!b)this.lookup(a);return this.content[a]?1:0};x.prototype.isFile=function(a){return this.exists(a)&&!this.is_dir(a)?1:0};x.prototype.mkdir=function(a,b,c){var
g=c&&as(bd);if(this.exists(a))if(g)ak(g,al(dO,b0,this.nm(a)));else
e(a+": File exists");var
d=/^(.*)\/[^/]+/.exec(a);d=d&&d[1]||f;if(!this.exists(d))if(g)ak(g,al(bT,b0,this.nm(d)));else
e(d+aV);if(!this.is_dir(d))if(g)ak(g,al(bW,b0,this.nm(d)));else
e(d+bN);this.create_dir_if_needed(this.slash(a))};x.prototype.rmdir=function(a,b){var
c=b&&as(bd),d=a===f?f:this.slash(a),h=new
RegExp(du+d+de);if(!this.exists(a))if(c)ak(c,al(bT,bP,this.nm(a)));else
e(a+aV);if(!this.is_dir(a))if(c)ak(c,al(bW,bP,this.nm(a)));else
e(a+bN);for(var
g
in
this.content)if(g.match(h))if(c)ak(c,al(dB,bP,this.nm(a)));else
e(this.nm(a)+": Directory not empty");delete
this.content[d]};x.prototype.readdir=function(a){var
h=a===f?f:this.slash(a);if(!this.exists(a))e(a+aV);if(!this.is_dir(a))e(a+bN);var
i=new
RegExp(du+h+de),d={},c=[];for(var
g
in
this.content){var
b=g.match(i);if(b&&!d[b[1]]){d[b[1]]=true;c.push(b[1])}}return c};x.prototype.opendir=function(a,b){var
c=b&&as(bd),d=this.readdir(a),f=false,g=0;return{readSync:function(){if(f)if(c)ak(c,al(bK,dR,this.nm(a)));else
e(a+c9);if(g===d.length)return null;var
b=d[g];g++;return{name:b}},closeSync:function(){if(f)if(c)ak(c,al(bK,dR,this.nm(a)));else
e(a+c9);f=true;d=[]}}};x.prototype.is_dir=function(a){if(a===f)return true;var
b=this.slash(a);return this.content[b]?1:0};x.prototype.unlink=function(a){if(!this.exists(a,true))e(a+aV);delete
this.content[a];return 0};x.prototype.open=function(a,b){var
c;if(b.rdonly&&b.wronly)e(this.nm(a)+b3);if(b.text&&b.binary)e(this.nm(a)+bO);this.lookup(a);if(this.content[a]){if(this.is_dir(a))e(this.nm(a)+c1);if(b.create&&b.excl)e(this.nm(a)+bS);c=this.content[a];if(b.truncate)c.truncate()}else if(b.create){this.create_dir_if_needed(a);this.content[a]=new
F(y(0));c=this.content[a]}else
ek(this.nm(a));return new
aw(this.nm(a),c,b)};x.prototype.open=function(a,b){var
c;if(b.rdonly&&b.wronly)e(this.nm(a)+b3);if(b.text&&b.binary)e(this.nm(a)+bO);this.lookup(a);if(this.content[a]){if(this.is_dir(a))e(this.nm(a)+c1);if(b.create&&b.excl)e(this.nm(a)+bS);c=this.content[a];if(b.truncate)c.truncate()}else if(b.create){this.create_dir_if_needed(a);this.content[a]=new
F(y(0));c=this.content[a]}else
ek(this.nm(a));return new
aw(this.nm(a),c,b)};x.prototype.register=function(a,b){var
c;if(this.content[a])e(this.nm(a)+bS);if(cg(b))c=new
F(b);if(ch(b))c=new
F(ah(b));else if(Array.isArray(b))c=new
F(b9(b));else if(typeof
b==="string")c=new
F(d5(b));else if(b.toString){var
d=ah(I(b.toString()));c=new
F(d)}if(c){this.create_dir_if_needed(a);this.content[a]=c}else
e(this.nm(a)+" : registering file with invalid content type")};x.prototype.constructor=x;function
o(a){return a.length}function
az(a,b){return a.charCodeAt(b)}function
ia(a){var
d=o(a),c=new
Uint8Array(d),b=0;for(;b<d;b++)c[b]=az(a,b);return c}function
V(a,b){this.fs=require(b6);this.fd=a;this.flags=b}V.prototype=new
dY();V.prototype.constructor=V;V.prototype.truncate=function(a){try{this.fs.ftruncateSync(this.fd,a|0)}catch(f){e(f.toString())}};V.prototype.length=function(){try{return this.fs.fstatSync(this.fd).size}catch(f){e(f.toString())}};V.prototype.write=function(a,b,c,d){try{if(this.flags.isCharacterDevice)this.fs.writeSync(this.fd,b,c,d);else
this.fs.writeSync(this.fd,b,c,d,a)}catch(f){e(f.toString())}return 0};V.prototype.read=function(a,b,c,d){try{if(this.flags.isCharacterDevice)var
f=this.fs.readSync(this.fd,b,c,d);else
var
f=this.fs.readSync(this.fd,b,c,d,a);return f}catch(f){e(f.toString())}};V.prototype.close=function(){try{this.fs.closeSync(this.fd);return 0}catch(f){e(f.toString())}};function
b(a){this.fs=require(b6);this.root=a}b.prototype.nm=function(a){return this.root+a};b.prototype.exists=function(a){try{return this.fs.existsSync(this.nm(a))?1:0}catch(f){return 0}};b.prototype.isFile=function(a){try{return this.fs.statSync(this.nm(a)).isFile()?1:0}catch(f){e(f.toString())}};b.prototype.mkdir=function(a,b,c){try{this.fs.mkdirSync(this.nm(a),{mode:b});return 0}catch(f){this.raise_nodejs_error(f,c)}};b.prototype.rmdir=function(a,b){try{this.fs.rmdirSync(this.nm(a));return 0}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.readdir=function(a,b){try{return this.fs.readdirSync(this.nm(a))}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.is_dir=function(a){try{return this.fs.statSync(this.nm(a)).isDirectory()?1:0}catch(f){e(f.toString())}};b.prototype.unlink=function(a,b){try{this.fs.unlinkSync(this.nm(a));return 0}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.open=function(a,b,c){var
d=require("node:constants"),e=0;for(var
h
in
b)switch(h){case"rdonly":e|=d.O_RDONLY;break;case"wronly":e|=d.O_WRONLY;break;case"append":e|=d.O_WRONLY|d.O_APPEND;break;case"create":e|=d.O_CREAT;break;case"truncate":e|=d.O_TRUNC;break;case"excl":e|=d.O_EXCL;break;case"binary":e|=d.O_BINARY;break;case"text":e|=d.O_TEXT;break;case"nonblock":e|=d.O_NONBLOCK;break}try{var
f=this.fs.openSync(this.nm(a),e),g=this.fs.lstatSync(this.nm(a)).isCharacterDevice();b.isCharacterDevice=g;return new
V(f,b)}catch(f){this.raise_nodejs_error(f,c)}};b.prototype.rename=function(a,b,c){try{this.fs.renameSync(this.nm(a),this.nm(b))}catch(f){this.raise_nodejs_error(f,c)}};b.prototype.stat=function(a,b){try{var
c=this.fs.statSync(this.nm(a));return this.stats_from_js(c)}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.lstat=function(a,b){try{var
c=this.fs.lstatSync(this.nm(a));return this.stats_from_js(c)}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.symlink=function(a,b,c,d){try{this.fs.symlinkSync(this.nm(b),this.nm(c),a?db:"file");return 0}catch(f){this.raise_nodejs_error(f,d)}};b.prototype.readlink=function(a,b){try{var
c=this.fs.readlinkSync(this.nm(a),"utf8");return I(c)}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.opendir=function(a,b){try{return this.fs.opendirSync(this.nm(a))}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.raise_nodejs_error=function(a,b){var
c=as(bd);if(b&&c){var
d=al(a.code,a.syscall,a.path,a.errno);ak(c,d)}else
e(a.toString())};b.prototype.stats_from_js=function(a){var
b;if(a.isFile())b=0;else if(a.isDirectory())b=1;else if(a.isCharacterDevice())b=2;else if(a.isBlockDevice())b=3;else if(a.isSymbolicLink())b=4;else if(a.isFIFO())b=5;else if(a.isSocket())b=6;return[0,a.dev,a.ino,b,a.mode,a.nlink,a.uid,a.gid,a.rdev,a.size,a.atimeMs,a.mtimeMs,a.ctimeMs]};b.prototype.constructor=b;function
eb(a){var
b=cn(a);if(!b)return;return b[0]+ae}var
bo=eb(aY)||ay("unable to compute caml_root"),aK=[];if(a2())aK.push({path:bo,device:new
b(bo)});else
aK.push({path:bo,device:new
x(bo)});aK.push({path:dG,device:new
x(dG)});function
es(a){var
i=hN(a),a=i.join(ae),h=em(a),d;for(var
g=0;g<aK.length;g++){var
c=aK[g];if(h.search(c.path)===0&&(!d||d.path.length<c.path.length))d={path:c.path,device:c.device,rest:a.slice(c.path.length,a.length)}}if(!d&&a2()){var
f=eb(a);if(f&&f.match(/^[a-zA-Z]:\/$/)){var
c={path:f,device:new
b(f)};aK.push(c);d={path:c.path,device:c.device,rest:a.slice(c.path.length,a.length)}}}if(d)return d;e("no device found for "+h)}function
ho(a,b){var
c=es(a);if(!c.device.register)ay("cannot register file");c.device.register(c.rest,b);return 0}function
ep(a,b){var
a=R(a),b=R(b);return ho(a,b)}function
hs(){var
b=c.jsoo_fs_tmp;if(b)for(var
a=0;a<b.length;a++)ep(b[a].name,b[a].content);c.jsoo_create_file=ep;c.jsoo_fs_tmp=[];return 0}function
ea(){return[0]}function
hu(a,b,c){if(!Number.isFinite(a)){if(Number.isNaN(a))return I(bQ);return I(a>0?dq:"-infinity")}var
k=a===0&&1/a===Number.NEGATIVE_INFINITY?1:a>=0?0:1;if(k)a=-a;var
e=0;if(a===0);else if(a<1)while(a<1&&e>-1022){a*=2;e--}else
while(a>=2){a/=2;e++}var
l=e<0?f:aE,g=f;if(k)g=au;else
switch(c){case
43:g=aE;break;case
32:g=ad;break;default:break}if(b>=0&&b<13){var
i=Math.pow(2,b*4);a=Math.round(a*i)/i}var
d=a.toString(16);if(b>=0){var
j=d.indexOf(av);if(j<0)d+=av+aJ(b,L);else{var
h=j+1+b;if(d.length<h)d+=aJ(h-d.length,L);else
d=d.slice(0,h)}}return I(g+c$+d+"p"+l+e.toString(10))}function
hB(a){return+a.isZero()}var
ed=Math.pow(2,-24);function
hV(a){throw a}function
el(){hV(C.Division_by_zero)}function
d(a,b,c){this.lo=a&ac;this.mi=b&ac;this.hi=c&_}d.prototype.caml_custom="_j";d.prototype.copy=function(){return new
d(this.lo,this.mi,this.hi)};d.prototype.ucompare=function(a){if(this.hi>a.hi)return 1;if(this.hi<a.hi)return-1;if(this.mi>a.mi)return 1;if(this.mi<a.mi)return-1;if(this.lo>a.lo)return 1;if(this.lo<a.lo)return-1;return 0};d.prototype.compare=function(a){var
b=this.hi<<16,c=a.hi<<16;if(b>c)return 1;if(b<c)return-1;if(this.mi>a.mi)return 1;if(this.mi<a.mi)return-1;if(this.lo>a.lo)return 1;if(this.lo<a.lo)return-1;return 0};d.prototype.neg=function(){var
a=-this.lo,b=-this.mi+(a>>24),c=-this.hi+(b>>24);return new
d(a,b,c)};d.prototype.add=function(a){var
b=this.lo+a.lo,c=this.mi+a.mi+(b>>24),e=this.hi+a.hi+(c>>24);return new
d(b,c,e)};d.prototype.sub=function(a){var
b=this.lo-a.lo,c=this.mi-a.mi+(b>>24),e=this.hi-a.hi+(c>>24);return new
d(b,c,e)};d.prototype.mul=function(a){var
b=this.lo*a.lo,c=(b*ed|0)+this.mi*a.lo+this.lo*a.mi,e=(c*ed|0)+this.hi*a.lo+this.mi*a.mi+this.lo*a.hi;return new
d(b,c,e)};d.prototype.isZero=function(){return(this.lo|this.mi|this.hi)===0};d.prototype.isNeg=function(){return this.hi<<16<0};d.prototype.and=function(a){return new
d(this.lo&a.lo,this.mi&a.mi,this.hi&a.hi)};d.prototype.or=function(a){return new
d(this.lo|a.lo,this.mi|a.mi,this.hi|a.hi)};d.prototype.xor=function(a){return new
d(this.lo^a.lo,this.mi^a.mi,this.hi^a.hi)};d.prototype.shift_left=function(a){a=a&63;if(a===0)return this;if(a<24)return new
d(this.lo<<a,this.mi<<a|this.lo>>24-a,this.hi<<a|this.mi>>24-a);if(a<48)return new
d(0,this.lo<<a-24,this.mi<<a-24|this.lo>>48-a);return new
d(0,0,this.lo<<a-48)};d.prototype.shift_right_unsigned=function(a){a=a&63;if(a===0)return this;if(a<24)return new
d(this.lo>>a|this.mi<<24-a,this.mi>>a|this.hi<<24-a,this.hi>>a);if(a<48)return new
d(this.mi>>a-24|this.hi<<48-a,this.hi>>a-24,0);return new
d(this.hi>>a-48,0,0)};d.prototype.shift_right=function(a){a=a&63;if(a===0)return this;var
c=this.hi<<16>>16;if(a<24)return new
d(this.lo>>a|this.mi<<24-a,this.mi>>a|c<<24-a,this.hi<<16>>a>>>16);var
b=this.hi<<16>>31;if(a<48)return new
d(this.mi>>a-24|this.hi<<48-a,this.hi<<16>>a-24>>16,b&_);return new
d(this.hi<<16>>a-32,b,b)};d.prototype.lsl1=function(){this.hi=this.hi<<1|this.mi>>23;this.mi=(this.mi<<1|this.lo>>23)&ac;this.lo=this.lo<<1&ac};d.prototype.lsr1=function(){this.lo=(this.lo>>>1|this.mi<<23)&ac;this.mi=(this.mi>>>1|this.hi<<23)&ac;this.hi=this.hi>>>1};d.prototype.udivmod=function(a){var
e=0,c=this.copy(),b=a.copy(),f=new
d(0,0,0);while(c.ucompare(b)>0){e++;b.lsl1()}while(e>=0){e--;f.lsl1();if(c.ucompare(b)>=0){f.lo++;c=c.sub(b)}b.lsr1()}return{quotient:f,modulus:c}};d.prototype.div=function(a){var
b=this;if(a.isZero())el();var
d=b.hi^a.hi;if(b.hi&Z)b=b.neg();if(a.hi&Z)a=a.neg();var
c=b.udivmod(a).quotient;if(d&Z)c=c.neg();return c};d.prototype.mod=function(a){var
b=this;if(a.isZero())el();var
d=b.hi;if(b.hi&Z)b=b.neg();if(a.hi&Z)a=a.neg();var
c=b.udivmod(a).modulus;if(d&Z)c=c.neg();return c};d.prototype.toInt=function(){return this.lo|this.mi<<24};d.prototype.toFloat=function(){return(this.hi<<16)*Math.pow(2,32)+this.mi*Math.pow(2,24)+this.lo};d.prototype.toArray=function(){return[this.hi>>8,this.hi&af,this.mi>>16,this.mi>>8&af,this.mi&af,this.lo>>16,this.lo>>8&af,this.lo&af]};d.prototype.lo32=function(){return this.lo|(this.mi&af)<<24};d.prototype.hi32=function(){return this.mi>>>8&_|this.hi<<16};function
hE(a){return new
d(a&ac,a>>24&ac,a>>31&_)}function
hF(a){return a.toInt()}function
hA(a){return+a.isNeg()}function
hD(a){return a.neg()}function
hy(a,b){var
c=ci(a);if(c.signedconv&&hA(b)){c.sign=-1;b=hD(b)}var
d=f,i=hE(c.base),h="0123456789abcdef";do{var
g=b.udivmod(i);b=g.quotient;d=h.charAt(hF(g.modulus))+d}while(!hB(b));if(c.prec>=0){c.filler=ad;var
e=c.prec-d.length;if(e>0)d=aJ(e,L)+d}return b$(c,d)}function
hL(){var
b=console,c=["log","debug","info","warn",c0,"assert",db,"dirxml","trace","group","groupCollapsed","groupEnd","time","timeEnd"];function
d(){}for(var
a=0;a<c.length;a++)if(!b[c[a]])b[c[a]]=d;return b}var
aH=ap;function
bl(a){return function(){var
d=arguments.length;if(d>0){var
c=new
Array(d);for(var
b=0;b<d;b++)c[b]=arguments[b]}else
c=[undefined];var
e=aH(a,c);return e
instanceof
Function?bl(e):e}}function
hK(a){return a.l>=0?a.l:a.l=a.length}function
hM(a){return function(){var
d=hK(a),c=new
Array(d);for(var
b=0;b<d;b++)c[b]=arguments[b];return aH(a,c)}}function
aI(a){return function(){var
e=arguments.length,c=new
Array(e+1);c[0]=this;for(var
b=0;b<e;b++)c[b+1]=arguments[b];var
d=aH(a,c);return d
instanceof
Function?bl(d):d}}function
hO(){return 0}var
ar=new
Array();function
ee(a){return ar[a]}function
aj(a){var
b=ee(a);if(!b.opened)e("Cannot flush a closed channel");if(!b.buffer||b.buffer_curr===0)return 0;if(b.output)b.output(bp(b.buffer,0,b.buffer_curr));else
b.file.write(b.offset,b.buffer,0,b.buffer_curr);b.offset+=b.buffer_curr;b.buffer_curr=0;return 0}function
h1(a,b){if(b.name)try{var
d=require(b6),c=d.openSync(b.name,"rs");return new
V(c,b)}catch(f){}return new
V(a,b)}var
bq=new
Array(3);function
aW(a,b){F.call(this,y(0));this.log=function(a){return 0};if(a===1&&typeof
console.log==="function")this.log=console.log;else if(a===2&&typeof
console.error==="function")this.log=console.error;else if(typeof
console.log==="function")this.log=console.log;this.flags=b}aW.prototype.length=function(){return 0};aW.prototype.write=function(a,b,c,d){if(this.log){if(d>0&&c>=0&&c+d<=b.length&&b[c+d-1]===10)d--;var
f=y(d);ao(b9(b),c,f,0,d);this.log(f.toUtf16());return 0}e(this.fd+cY)};aW.prototype.read=function(a,b,c,d){e(this.fd+": file descriptor is write only")};aW.prototype.close=function(){this.log=undefined};function
br(a,b){if(b===undefined)b=bq.length;bq[b]=a;return b|0}function
h$(a,b,c){var
d={};while(b){switch(b[1]){case
0:d.rdonly=1;break;case
1:d.wronly=1;break;case
2:d.append=1;break;case
3:d.create=1;break;case
4:d.truncate=1;break;case
5:d.excl=1;break;case
6:d.binary=1;break;case
7:d.text=1;break;case
8:d.nonblock=1;break}b=b[2]}if(d.rdonly&&d.wronly)e(aq(a)+b3);if(d.text&&d.binary)e(aq(a)+bO);var
f=es(a),g=f.device.open(f.rest,d);return br(g,undefined)}(function(){function
a(a,b){return a2()?h1(a,b):new
aW(a,b)}br(a(0,{rdonly:1,altname:"/dev/stdin",isCharacterDevice:true}),0);br(a(1,{buffered:2,wronly:1,isCharacterDevice:true}),1);br(a(2,{buffered:2,wronly:1,isCharacterDevice:true}),2)}());function
hP(a){var
b=bq[a];if(b.flags.wronly)e(dp+a+" is writeonly");var
d=null,c={file:b,offset:b.flags.append?b.length():0,fd:a,opened:true,out:false,buffer_curr:0,buffer_max:0,buffer:new
Uint8Array(dr),refill:d};ar[c.fd]=c;return c.fd}function
ef(a){var
b=bq[a];if(b.flags.rdonly)e(dp+a+" is readonly");var
d=b.flags.buffered!==undefined?b.flags.buffered:1,c={file:b,offset:b.flags.append?b.length():0,fd:a,opened:true,out:true,buffer_curr:0,buffer:new
Uint8Array(dr),buffered:d};ar[c.fd]=c;return c.fd}function
hQ(){var
b=0;for(var
a=0;a<ar.length;a++)if(ar[a]&&ar[a].opened&&ar[a].out)b=[0,ar[a].fd,b];return b}function
hS(a,b,c,d){var
f=ee(a);if(!f.opened)e("Cannot output to a closed channel");b=b.subarray(c,c+d);if(f.buffer_curr+b.length>f.buffer.length){var
h=new
Uint8Array(f.buffer_curr+b.length);h.set(f.buffer);f.buffer=h}switch(f.buffered){case
0:f.buffer.set(b,f.buffer_curr);f.buffer_curr+=b.length;aj(a);break;case
1:f.buffer.set(b,f.buffer_curr);f.buffer_curr+=b.length;if(f.buffer_curr>=f.buffer.length)aj(a);break;case
2:var
g=b.lastIndexOf(10);if(g<0){f.buffer.set(b,f.buffer_curr);f.buffer_curr+=b.length;if(f.buffer_curr>=f.buffer.length)aj(a)}else{f.buffer.set(b.subarray(0,g+1),f.buffer_curr);f.buffer_curr+=g+1;aj(a);f.buffer.set(b.subarray(g+1),f.buffer_curr);f.buffer_curr+=b.length-g-1}break}return 0}function
hR(a,b,c,d){var
b=en(b);return hS(a,b,c,d)}function
eg(a,b,c,d){return hR(a,ah(b),c,d)}function
eh(a,b){var
c=R(String.fromCharCode(b));eg(a,c,0,1);return 0}function
hI(a){return a===245?1:0}var
h6=Math.log2&&Math.log2(1.1235582092889474e307)===1020;function
h4(a){if(h6)return Math.floor(Math.log2(a));var
b=0;if(a===0)return Number.NEGATIVE_INFINITY;if(a>=1)while(a>=2){a/=2;b++}else
while(a<1){a*=2;b--}return b}function
cb(a){var
b=new
Float32Array(1);b[0]=a;var
c=new
Int32Array(b.buffer);return c[0]|0}function
bk(a,b,c){return new
d(a,b,c)}function
bj(a){if(!Number.isFinite(a)){if(Number.isNaN(a))return bk(1,0,dw);return a>0?bk(0,0,dw):bk(0,0,0xfff0)}var
f=a===0&&1/a===Number.NEGATIVE_INFINITY?Z:a>=0?0:Z;if(f)a=-a;var
b=h4(a)+1023;if(b<=0){b=0;a/=Math.pow(2,-c3)}else{a/=Math.pow(2,b-dD);if(a<16){a*=2;b-=1}if(b===0)a/=2}var
d=Math.pow(2,24),c=a|0;a=(a-c)*d;var
e=a|0;a=(a-e)*d;var
g=a|0;c=c&bV|f|b<<4;return bk(g,e,c)}function
a0(a){return a.toArray()}function
d4(a,b,c){a.write(32,b.dims.length);a.write(32,b.kind|b.layout<<8);if(b.caml_custom===bc)for(var
d=0;d<b.dims.length;d++)if(b.dims[d]<_)a.write(16,b.dims[d]);else{a.write(16,_);a.write(32,0);a.write(32,b.dims[d])}else
for(var
d=0;d<b.dims.length;d++)a.write(32,b.dims[d]);switch(b.kind){case
2:case
3:case
12:for(var
d=0;d<b.data.length;d++)a.write(8,b.data[d]);break;case
4:case
5:for(var
d=0;d<b.data.length;d++)a.write(16,b.data[d]);break;case
6:for(var
d=0;d<b.data.length;d++)a.write(32,b.data[d]);break;case
8:case
9:a.write(8,0);for(var
d=0;d<b.data.length;d++)a.write(32,b.data[d]);break;case
7:for(var
d=0;d<b.data.length/2;d++){var
f=a0(b.get(d));for(var
e=0;e<8;e++)a.write(8,f[e])}break;case
1:for(var
d=0;d<b.data.length;d++){var
f=a0(bj(b.get(d)));for(var
e=0;e<8;e++)a.write(8,f[e])}break;case
0:for(var
d=0;d<b.data.length;d++){var
f=cb(b.get(d));a.write(32,f)}break;case
10:for(var
d=0;d<b.data.length/2;d++){var
e=b.get(d);a.write(32,cb(e[1]));a.write(32,cb(e[2]))}break;case
11:for(var
d=0;d<b.data.length/2;d++){var
g=b.get(d),f=a0(bj(g[1]));for(var
e=0;e<8;e++)a.write(8,f[e]);var
f=a0(bj(g[2]));for(var
e=0;e<8;e++)a.write(8,f[e])}break}c[0]=(4+b.dims.length)*4;c[1]=(4+b.dims.length)*8}function
d2(a){switch(a){case
7:case
10:case
11:return 2;default:return 1}}function
hg(a,b){var
c;switch(a){case
0:c=Float32Array;break;case
1:c=Float64Array;break;case
2:c=Int8Array;break;case
3:c=Uint8Array;break;case
4:c=Int16Array;break;case
5:c=Uint16Array;break;case
6:c=Int32Array;break;case
7:c=Int32Array;break;case
8:c=Int32Array;break;case
9:c=Int32Array;break;case
10:c=Float32Array;break;case
11:c=Float64Array;break;case
12:c=Uint8Array;break}if(!c)t("Bigarray.create: unsupported kind");var
d=new
c(b*d2(a));return d}function
cc(a){var
b=new
Int32Array(1);b[0]=a;var
c=new
Float32Array(b.buffer);return c[0]}function
aZ(a){return new
d(a[7]<<0|a[6]<<8|a[5]<<16,a[4]<<0|a[3]<<8|a[2]<<16,a[1]<<0|a[0]<<8)}function
cd(a){var
f=a.lo,g=a.mi,c=a.hi,d=(c&0x7fff)>>4;if(d===2047)return(f|g|c&bV)===0?c&Z?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY:Number.NaN;var
e=Math.pow(2,-24),b=(f*e+g)*e+(c&bV);if(d>0){b+=16;b*=Math.pow(2,d-dD)}else
b*=Math.pow(2,-c3);if(c&Z)b=-b;return b}function
b8(a){var
d=a.length,c=1;for(var
b=0;b<d;b++){if(a[b]<0)t("Bigarray.create: negative dimension");c=c*a[b]}return c}function
hx(a,b){return new
d(a&ac,a>>>24&af|(b&_)<<8,b>>>16&_)}function
ce(a){return a.hi32()}function
cf(a){return a.lo32()}var
hh=bc;function
an(a,b,c,d){this.kind=a;this.layout=b;this.dims=c;this.data=d}an.prototype.caml_custom=hh;an.prototype.offset=function(a){var
c=0;if(typeof
a==="number")a=[a];if(!Array.isArray(a))t("bigarray.js: invalid offset");if(this.dims.length!==a.length)t("Bigarray.get/set: bad number of dimensions");if(this.layout===0)for(var
b=0;b<this.dims.length;b++){if(a[b]<0||a[b]>=this.dims[b])be();c=c*this.dims[b]+a[b]}else
for(var
b=this.dims.length-1;b>=0;b--){if(a[b]<1||a[b]>this.dims[b])be();c=c*this.dims[b]+(a[b]-1)}return c};an.prototype.get=function(a){switch(this.kind){case
7:var
d=this.data[a*2+0],b=this.data[a*2+1];return hx(d,b);case
10:case
11:var
e=this.data[a*2+0],c=this.data[a*2+1];return[bb,e,c];default:return this.data[a]}};an.prototype.set=function(a,b){switch(this.kind){case
7:this.data[a*2+0]=cf(b);this.data[a*2+1]=ce(b);break;case
10:case
11:this.data[a*2+0]=b[1];this.data[a*2+1]=b[2];break;default:this.data[a]=b;break}return 0};an.prototype.fill=function(a){switch(this.kind){case
7:var
c=cf(a),e=ce(a);if(c===e)this.data.fill(c);else
for(var
b=0;b<this.data.length;b++)this.data[b]=b%2===0?c:e;break;case
10:case
11:var
d=a[1],f=a[2];if(d===f)this.data.fill(d);else
for(var
b=0;b<this.data.length;b++)this.data[b]=b%2===0?d:f;break;default:this.data.fill(a);break}};an.prototype.compare=function(a,b){if(this.layout!==a.layout||this.kind!==a.kind){var
f=this.kind|this.layout<<8,g=a.kind|a.layout<<8;return g-f}if(this.dims.length!==a.dims.length)return a.dims.length-this.dims.length;for(var
c=0;c<this.dims.length;c++)if(this.dims[c]!==a.dims[c])return this.dims[c]<a.dims[c]?-1:1;switch(this.kind){case
0:case
1:case
10:case
11:var
d,e;for(var
c=0;c<this.data.length;c++){d=this.data[c];e=a.data[c];if(d<e)return-1;if(d>e)return 1;if(d!==e){if(!b)return Number.NaN;if(!Number.isNaN(d))return 1;if(!Number.isNaN(e))return-1}}break;case
7:for(var
c=0;c<this.data.length;c+=2){if(this.data[c+1]<a.data[c+1])return-1;if(this.data[c+1]>a.data[c+1])return 1;if(this.data[c]>>>0<a.data[c]>>>0)return-1;if(this.data[c]>>>0>a.data[c]>>>0)return 1}break;case
2:case
3:case
4:case
5:case
6:case
8:case
9:case
12:for(var
c=0;c<this.data.length;c++){if(this.data[c]<a.data[c])return-1;if(this.data[c]>a.data[c])return 1}break}return 0};function
aG(a,b,c,d){this.kind=a;this.layout=b;this.dims=c;this.data=d}aG.prototype=new
an();aG.prototype.offset=function(a){if(typeof
a!=="number")if(Array.isArray(a)&&a.length===1)a=a[0];else
t("Ml_Bigarray_c_1_1.offset");if(a<0||a>=this.dims[0])be();return a};aG.prototype.get=function(a){return this.data[a]};aG.prototype.set=function(a,b){this.data[a]=b;return 0};aG.prototype.fill=function(a){this.data.fill(a);return 0};function
d0(a,b,c,d){var
e=d2(a);if(b8(c)*e!==d.length)t("length doesn't match dims");if(b===0&&c.length===1&&e===1)return new
aG(a,b,c,d);return new
an(a,b,c,d)}function
d1(a,b,c){var
k=a.read32s();if(k<0||k>16)ay("input_value: wrong number of bigarray dimensions");var
r=a.read32s(),l=r&af,q=r>>8&1,j=[];if(c===bc)for(var
d=0;d<k;d++){var
p=a.read16u();if(p===_){var
u=a.read32u(),v=a.read32u();if(u!==0)ay("input_value: bigarray dimension overflow in 32bit");p=v}j.push(p)}else
for(var
d=0;d<k;d++)j.push(a.read32u());var
f=b8(j),h=hg(l,f),i=d0(l,q,j,h);switch(l){case
2:for(var
d=0;d<f;d++)h[d]=a.read8s();break;case
3:case
12:for(var
d=0;d<f;d++)h[d]=a.read8u();break;case
4:for(var
d=0;d<f;d++)h[d]=a.read16s();break;case
5:for(var
d=0;d<f;d++)h[d]=a.read16u();break;case
6:for(var
d=0;d<f;d++)h[d]=a.read32s();break;case
8:case
9:var
t=a.read8u();if(t)ay("input_value: cannot read bigarray with 64-bit OCaml ints");for(var
d=0;d<f;d++)h[d]=a.read32s();break;case
7:var
g=new
Array(8);for(var
d=0;d<f;d++){for(var
e=0;e<8;e++)g[e]=a.read8u();var
s=aZ(g);i.set(d,s)}break;case
1:var
g=new
Array(8);for(var
d=0;d<f;d++){for(var
e=0;e<8;e++)g[e]=a.read8u();var
m=cd(aZ(g));i.set(d,m)}break;case
0:for(var
d=0;d<f;d++){var
m=cc(a.read32s());i.set(d,m)}break;case
10:for(var
d=0;d<f;d++){var
o=cc(a.read32s()),n=cc(a.read32s());i.set(d,[bb,o,n])}break;case
11:var
g=new
Array(8);for(var
d=0;d<f;d++){for(var
e=0;e<8;e++)g[e]=a.read8u();var
o=cd(aZ(g));for(var
e=0;e<8;e++)g[e]=a.read8u();var
n=cd(aZ(g));i.set(d,[bb,o,n])}break}b[0]=(4+k)*4;return d0(l,q,j,h)}function
dZ(a,b,c){return a.compare(b,c)}function
ei(a,b){return Math.imul(a,b)}function
ai(a,b){b=ei(b,0xcc9e2d51|0);b=b<<15|b>>>32-15;b=ei(b,0x1b873593);a^=b;a=a<<13|a>>>32-13;return(a+(a<<2)|0)+(0xe6546b64|0)|0}function
ht(a,b){a=ai(a,cf(b));a=ai(a,ce(b));return a}function
ec(a,b){return ht(a,bj(b))}function
d3(a){var
c=b8(a.dims),d=0;switch(a.kind){case
2:case
3:case
12:if(c>dM)c=dM;var
e=0,b=0;for(b=0;b+4<=a.data.length;b+=4){e=a.data[b+0]|a.data[b+1]<<8|a.data[b+2]<<16|a.data[b+3]<<24;d=ai(d,e)}e=0;switch(c&3){case
3:e=a.data[b+2]<<16;case
2:e|=a.data[b+1]<<8;case
1:e|=a.data[b+0];d=ai(d,e)}break;case
4:case
5:if(c>aS)c=aS;var
e=0,b=0;for(b=0;b+2<=a.data.length;b+=2){e=a.data[b+0]|a.data[b+1]<<16;d=ai(d,e)}if((c&1)!==0)d=ai(d,a.data[b]);break;case
6:if(c>64)c=64;for(var
b=0;b<c;b++)d=ai(d,a.data[b]);break;case
8:case
9:if(c>64)c=64;for(var
b=0;b<c;b++)d=ai(d,a.data[b]);break;case
7:if(c>32)c=32;c*=2;for(var
b=0;b<c;b++)d=ai(d,a.data[b]);break;case
10:c*=2;case
0:if(c>64)c=64;for(var
b=0;b<c;b++)d=ec(d,a.data[b]);break;case
11:c*=2;case
1:if(c>32)c=32;for(var
b=0;b<c;b++)d=ec(d,a.data[b]);break}return d}function
hv(a,b){b[0]=4;return a.read32s()}function
hT(a,b){switch(a.read8u()){case
1:b[0]=4;return a.read32s();case
2:ay("input_value: native integer value too large");break;default:ay("input_value: ill-formed native integer")}}function
hG(a,b){var
d=new
Array(8);for(var
c=0;c<8;c++)d[c]=a.read8u();b[0]=8;return aZ(d)}function
hC(a,b,c){var
e=a0(b);for(var
d=0;d<8;d++)a.write(8,e[d]);c[0]=8;c[1]=8}function
hw(a,b,c){return a.compare(b)}function
hz(a){return a.lo32()^a.hi32()}var
d9={_j:{deserialize:hG,serialize:hC,fixed_length:8,compare:hw,hash:hz},_i:{deserialize:hv,fixed_length:4},_n:{deserialize:hT,fixed_length:4},_bigarray:{deserialize:function(a,b){return d1(a,b,"_bigarray")},serialize:d4,compare:dZ,hash:d3},_bigarr02:{deserialize:function(a,b){return d1(a,b,bc)},serialize:d4,compare:dZ,hash:d3}};function
b_(a){return d9[a.caml_custom]&&d9[a.caml_custom].compare}function
d7(a,b,c,d){var
f=b_(b);if(f){var
e=c>0?f(b,a,d):f(a,b,d);if(d&&Number.isNaN(e))return c;if(Number.isNaN(+e))return+e;if((e|0)!==0)return e|0}return c}function
d8(a){if(typeof
a==="number")return aU;else if(cg(a))return ba;else if(ch(a))return 1252;else if(Array.isArray(a)&&a[0]===a[0]>>>0&&a[0]<=dh){var
b=a[0]|0;return b===bb?0:b}else if(a
instanceof
String)return cW;else if(typeof
a==="string")return cW;else if(a
instanceof
Number)return aU;else if(a&&a.caml_custom)return bY;else if(a&&a.compare)return 1256;else if(typeof
a==="function")return 1247;else if(typeof
a==="symbol")return 1251;return 1001}function
hH(a,b){if(a<b)return-1;if(a===b)return 0;return 1}function
h_(a,b){return a<b?-1:a>b?1:0}function
hk(a,b){a.t&6&&bh(a);b.t&6&&bh(b);return a.c<b.c?-1:a.c>b.c?1:0}function
hm(a,b,c){var
f=[];for(;;){if(!(c&&a===b)){var
e=d8(a);if(e===cV){a=a[1];continue}var
g=d8(b);if(g===cV){b=b[1];continue}if(e!==g){if(e===aU){if(g===bY)return d7(a,b,-1,c);return-1}if(g===aU){if(e===bY)return d7(b,a,1,c);return 1}return e<g?-1:1}switch(e){case
247:t(b1);break;case
248:var
d=hH(a[2],b[2])|0;if(d!==0)return d;break;case
249:t(b1);break;case
250:t("equal: got Forward_tag, should not happen");break;case
251:t("equal: abstract value");break;case
252:if(a!==b){var
d=hk(a,b)|0;if(d!==0)return d}break;case
253:t("equal: got Double_tag, should not happen");break;case
254:t("equal: got Double_array_tag, should not happen");break;case
255:t("equal: got Custom_tag, should not happen");break;case
1247:t(b1);break;case
1255:var
i=b_(a);if(i!==b_(b))return a.caml_custom<b.caml_custom?-1:1;if(!i)t("compare: abstract value");var
d=i(a,b,c);if(Number.isNaN(d))return c?-1:d;if(d!==(d|0))return-1;if(d!==0)return d|0;break;case
1256:var
d=a.compare(b,c);if(Number.isNaN(d))return c?-1:d;if(d!==(d|0))return-1;if(d!==0)return d|0;break;case
1000:a=+a;b=+b;if(a<b)return-1;if(a>b)return 1;if(a!==b){if(!c)return Number.NaN;if(!Number.isNaN(a))return 1;if(!Number.isNaN(b))return-1}break;case
1001:if(a<b)return-1;if(a>b)return 1;if(a!==b)return c?1:Number.NaN;break;case
1251:if(a!==b)return c?1:Number.NaN;break;case
1252:var
a=aq(a),b=aq(b);if(a!==b){if(a<b)return-1;if(a>b)return 1}break;case
12520:var
a=a.toString(),b=b.toString();if(a!==b){if(a<b)return-1;if(a>b)return 1}break;default:if(hI(e)){t("compare: continuation value");break}if(a.length!==b.length)return a.length<b.length?-1:1;if(a.length>1)f.push(a,b,1);break}}if(f.length===0)return 0;var
h=f.pop();b=f.pop();a=f.pop();if(h+1<a.length)f.push(a,b,h+1);a=a[h];b=b[h]}}function
a1(a,b){return+(hm(a,b,false)!==0)}function
bm(a){if(Array.isArray(a)&&a[0]===a[0]>>>0)return a[0];else if(cg(a))return ba;else if(ch(a))return ba;else if(a
instanceof
Function||typeof
a==="function")return 247;else if(a&&a.caml_custom)return dh;else
return aU}var
er=undefined;function
hi(a){var
d={},c=-1;if(a)for(var
b=1;b<a.length;b++){var
e=a[b][2];c=Math.max(c,e);d[j(a[b][1])]=e}d.next_idx=c+1;return d}function
Q(a,b,c){if(c){var
d=c;if(er)a=aH(er,[d]);else if(C.symbols){if(!C.symidx)C.symidx=hi(C.symbols);var
e=C.symidx[d];if(e>=0)a=e;else{var
a=C.symidx.next_idx++;C.symidx[d]=a}}}C[a+1]=b;if(c)C[c]=b}function
ck(a,b){ej[aq(a)]=b;return 0}function
hZ(){t(bM)}function
M(a,b){if(b>>>0>=o(a))hZ();return az(a,b)}function
H(a){a.t&6&&bh(a);return R(a.c)}function
h0(){return 0x7fffffff/4|0}function
cl(a){var
b=1;while(a&&a.joo_tramp){a=a.joo_tramp.apply(null,a.joo_args);b++}return a}function
z(a,b){return{joo_tramp:a,joo_args:b}}function
$(a){{if(Array.isArray(a))return a;var
b;if(c.RangeError&&a
instanceof
c.RangeError&&a.message&&a.message.match(/maximum call stack/i))b=C.Stack_overflow;else if(c.InternalError&&a
instanceof
c.InternalError&&a.message&&a.message.match(/too much recursion/i))b=C.Stack_overflow;else if(a
instanceof
c.Error&&as(bX))b=[0,as(bX),a];else
b=[0,C.Failure,I(String(a))];if(a
instanceof
c.Error)b.js_error=a;return b}}function
hJ(a){switch(a[2]){case-8:case-11:case-12:return 1;default:return 0}}function
hr(a){var
b=f;if(a[0]===0){b+=a[1][1];if(a.length===3&&a[2][0]===0&&hJ(a[1]))var
e=a[2],g=1;else
var
g=2,e=a;b+="(";for(var
d=g;d<e.length;d++){if(d>g)b+=c4;var
c=e[d];if(typeof
c==="number")b+=c.toString();else if(c
instanceof
ag)b+=a$+c.toString()+a$;else if(typeof
c==="string")b+=a$+c.toString()+a$;else
b+=dI}b+=")"}else if(a[0]===w)b+=a[1];return b}function
d_(a){if(Array.isArray(a)&&(a[0]===0||a[0]===w)){var
c=as(dL);if(c)aH(c,[a,false]);else{var
d=hr(a),b=as(ds);if(b)aH(b,[0]);console.error(bZ+d);if(a.js_error)throw a.js_error}}else
throw a}function
hY(){var
d=c.process;if(d&&d.on)d.on("uncaughtException",function(a,b){d_(a);d.exit(2)});else if(c.addEventListener)c.addEventListener(c0,function(a){if(a.error)d_(a.error)})}hY();function
s(a,b){return(a.l>=0?a.l:a.l=a.length)===1?a(b):ap(a,[b])}function
P(a,b,c){return(a.l>=0?a.l:a.l=a.length)===2?a(b,c):ap(a,[b,c])}function
cR(a,b,c,d,e){return(a.l>=0?a.l:a.l=a.length)===4?a(b,c,d,e):ap(a,[b,c,d,e])}function
bJ(a,b,c,d,e,f){return(a.l>=0?a.l:a.l=a.length)===5?a(b,c,d,e,f):ap(a,[b,c,d,e,f])}function
he(a,b,c,d,e,f,g,h){return(a.l>=0?a.l:a.l=a.length)===7?a(b,c,d,e,f,g,h):ap(a,[b,c,d,e,f,g,h])}var
h9=0;hs();var
bs=[w,dW,-1],cs=[w,dm,-2],cp=[w,b5,-3],co=[w,c6,-4],cq=[w,dQ,-8],cr=[w,dA,-9],q=[w,dF,-11],ct=[w,dN,-12],hd=[4,0,0,0,[12,45,[4,0,0,0,0]]],bA=[0,[11,'File "',[2,0,[11,'", line ',[4,0,0,0,[11,dU,[4,0,0,0,[12,45,[4,0,0,0,[11,": ",[2,0,0]]]]]]]]]],'File "%s", line %d, characters %d-%d: %s'],bF=[0,0,[0,1,[0,2,[0,3,[0,4,[0,5,0]]]]]];Q(11,ct,dN);Q(10,q,dF);Q(9,[w,dJ,-10],dJ);Q(8,cr,dA);Q(7,cq,dQ);Q(6,[w,c8,-7],c8);Q(5,[w,dj,-6],dj);Q(4,[w,dv,-5],dv);Q(3,co,c6);Q(2,cp,b5);Q(1,cs,dm);Q(0,bs,dW);function
n(a){if(typeof
a==="number")return 0;switch(a[0]){case
0:return[0,n(a[1])];case
1:return[1,n(a[1])];case
2:return[2,n(a[1])];case
3:return[3,n(a[1])];case
4:return[4,n(a[1])];case
5:return[5,n(a[1])];case
6:return[6,n(a[1])];case
7:return[7,n(a[1])];case
8:var
c=a[1];return[8,c,n(a[2])];case
9:var
b=a[1];return[9,b,b,n(a[3])];case
10:return[10,n(a[1])];case
11:return[11,n(a[1])];case
12:return[12,n(a[1])];case
13:return[13,n(a[1])];default:return[14,n(a[1])]}}function
E(a,b){if(typeof
a==="number")return b;switch(a[0]){case
0:return[0,E(a[1],b)];case
1:return[1,E(a[1],b)];case
2:return[2,E(a[1],b)];case
3:return[3,E(a[1],b)];case
4:return[4,E(a[1],b)];case
5:return[5,E(a[1],b)];case
6:return[6,E(a[1],b)];case
7:return[7,E(a[1],b)];case
8:var
c=a[1];return[8,c,E(a[2],b)];case
9:var
d=a[2],e=a[1];return[9,e,d,E(a[3],b)];case
10:return[10,E(a[1],b)];case
11:return[11,E(a[1],b)];case
12:return[12,E(a[1],b)];case
13:return[13,E(a[1],b)];default:return[14,E(a[1],b)]}}function
m(a,b){if(typeof
a==="number")return b;switch(a[0]){case
0:return[0,m(a[1],b)];case
1:return[1,m(a[1],b)];case
2:var
c=a[1];return[2,c,m(a[2],b)];case
3:var
d=a[1];return[3,d,m(a[2],b)];case
4:var
e=a[3],f=a[2],g=a[1];return[4,g,f,e,m(a[4],b)];case
5:var
h=a[3],i=a[2],j=a[1];return[5,j,i,h,m(a[4],b)];case
6:var
k=a[3],l=a[2],n=a[1];return[6,n,l,k,m(a[4],b)];case
7:var
o=a[3],p=a[2],q=a[1];return[7,q,p,o,m(a[4],b)];case
8:var
r=a[3],s=a[2],t=a[1];return[8,t,s,r,m(a[4],b)];case
9:var
u=a[1];return[9,u,m(a[2],b)];case
10:return[10,m(a[1],b)];case
11:var
v=a[1];return[11,v,m(a[2],b)];case
12:var
w=a[1];return[12,w,m(a[2],b)];case
13:var
x=a[2],y=a[1];return[13,y,x,m(a[3],b)];case
14:var
z=a[2],A=a[1];return[14,A,z,m(a[3],b)];case
15:return[15,m(a[1],b)];case
16:return[16,m(a[1],b)];case
17:var
B=a[1];return[17,B,m(a[2],b)];case
18:var
C=a[1];return[18,C,m(a[2],b)];case
19:return[19,m(a[1],b)];case
20:var
D=a[2],E=a[1];return[20,E,D,m(a[3],b)];case
21:var
F=a[1];return[21,F,m(a[2],b)];case
22:return[22,m(a[1],b)];case
23:var
G=a[1];return[23,G,m(a[2],b)];default:var
H=a[2],I=a[1];return[24,I,H,m(a[3],b)]}}var
eu=bR,ev="false";function
aL(a){throw h([0,co,a],1)}function
a3(a){return 0<=a?a:-a|0}hP(0);ef(1);var
S=ef(2);function
aM(a,b){eg(a,b,0,o(b))}function
cu(a){aM(S,a);eh(S,10);return aj(S)}var
ew=[0,function(a){var
b=hQ(0);for(;;){if(!b)return 0;var
d=b[2],e=b[1];try{aj(e)}catch(f){var
c=$(f);if(c[1]!==cs)throw h(c,0)}b=d}}];function
bt(a){return s(b7(ew),0)}ck(ds,bt);var
aN=(4*h0(0)|0)-1|0;function
bv(a,b){var
c=b;for(;;){if(!c)return;var
d=c[2];s(a,c[1]);c=d}}function
at(a,b){var
c=y(a);hq(c,0,a,b);return c}var
eE="String.sub / Bytes.sub",eF="Bytes.blit",eG="String.blit / Bytes.blit_string";function
bw(a,b,c){if(0<=b&&0<=c&&(W(a)-c|0)>=b){var
d=y(c);ao(a,b,d,0,c);return d}return aL(eE)}function
cv(a,b,c){return H(bw(a,b,c))}function
cw(a,b,c,d,e){if(0<=e&&0<=b&&(W(a)-e|0)>=b&&0<=d&&(W(c)-e|0)>=d){ao(a,b,c,d,e);return}return aL(eF)}function
X(a,b,c,d,e){if(0<=e&&0<=b&&(o(a)-e|0)>=b&&0<=d&&(W(c)-e|0)>=d){bf(a,b,c,d,e);return}return aL(eG)}function
cx(a){var
b=a-9|0;a:{if(4<b>>>0){if(23!==b)break a}else if(2===b)break a;return 1}return 0}function
a4(a,b,c){return H(bw(ah(a),b,c))}function
cy(a){var
b=a-9|0;a:{if(4<b>>>0){if(23!==b)break a}else if(2===b)break a;return 1}return 0}var
eH="Buffer.add: cannot grow buffer";function
cz(a,b){var
d=a[2],c=[0,a[1][2]];for(;;){if(c[1]>=(d+b|0))break;c[1]=2*c[1]|0}if(aN<c[1]){if((d+b|0)>aN)throw h([0,cp,eH],1);c[1]=aN}var
e=y(c[1]);cw(a[1][1],0,e,0,a[2]);a[1]=[0,e,c[1]]}function
aO(a,b){var
c=o(b),d=a[2],e=a[1],f=d+c|0,g=e[1];if(e[2]<f){cz(a,c);X(b,0,a[1][1],a[2],c)}else
bf(b,0,g,d,c);a[2]=f}var
eI="@]",eJ="@}",eK="@?",eL="@\n",eM="@.",eN="@@",eO="@%",eP="%c",eQ="%s",eR=da,eS=c2,eT=dX,eU=c7,eV="%f",eW="%B",eX="%{",eY="%}",eZ="%(",e0="%)",e1="%a",e2="%t",e3="%?",e4="%r",e5="%_r",e6=[0,a,850,23],e7=[0,a,837,26],e8=[0,a,847,28],e9=[0,a,815,21],e_=[0,a,819,21],e$=[0,a,823,19],fa=[0,a,827,22],fb=[0,a,832,30],fc=[0,a,851,23],fd=[0,a,836,26],fe=[0,a,846,28],ff=[0,a,814,21],fg=[0,a,818,21],fh=[0,a,822,19],fi=[0,a,826,22],fj=[0,a,831,30];function
bx(a){return 5===a[2]?12:-6}function
cA(a){return[0,0,y(a)]}function
cB(a,b){var
c=W(a[2]),d=a[1]+b|0;if(c<d){var
e=c*2|0,g=d<=e?e:d,f=y(g);cw(a[2],0,f,0,c);a[2]=f}}function
aA(a,b){cB(a,1);ax(a[2],a[1],b);a[1]=a[1]+1|0}function
A(a,b){var
c=o(b);cB(a,c);X(b,0,a[2],a[1],c);a[1]=a[1]+c|0}function
cC(a){return cv(a[2],0,a[1])}function
cD(a){if(typeof
a==="number")switch(a){case
0:return eI;case
1:return eJ;case
2:return eK;case
3:return eL;case
4:return eM;case
5:return eN;default:return eO}switch(a[0]){case
0:return a[1];case
1:return a[1];default:return"@"+H(at(1,a[1]))}}function
by(a,b){var
c=b;for(;;){if(typeof
c==="number")return;switch(c[0]){case
0:var
d=c[1];A(a,eP);c=d;break;case
1:var
e=c[1];A(a,eQ);c=e;break;case
2:var
f=c[1];A(a,eR);c=f;break;case
3:var
g=c[1];A(a,eS);c=g;break;case
4:var
h=c[1];A(a,eT);c=h;break;case
5:var
i=c[1];A(a,eU);c=i;break;case
6:var
j=c[1];A(a,eV);c=j;break;case
7:var
k=c[1];A(a,eW);c=k;break;case
8:var
l=c[2],m=c[1];A(a,eX);by(a,m);A(a,eY);c=l;break;case
9:var
n=c[3],o=c[1];A(a,eZ);by(a,o);A(a,e0);c=n;break;case
10:var
p=c[1];A(a,e1);c=p;break;case
11:var
q=c[1];A(a,e2);c=q;break;case
12:var
r=c[1];A(a,e3);c=r;break;case
13:var
s=c[1];A(a,e4);c=s;break;default:var
t=c[1];A(a,e5);c=t}}}function
r(a){if(typeof
a==="number")return 0;switch(a[0]){case
0:return[0,r(a[1])];case
1:return[1,r(a[1])];case
2:return[2,r(a[1])];case
3:return[3,r(a[1])];case
4:return[4,r(a[1])];case
5:return[5,r(a[1])];case
6:return[6,r(a[1])];case
7:return[7,r(a[1])];case
8:var
b=a[1];return[8,b,r(a[2])];case
9:var
c=a[2],d=a[1];return[9,c,d,r(a[3])];case
10:return[10,r(a[1])];case
11:return[11,r(a[1])];case
12:return[12,r(a[1])];case
13:return[13,r(a[1])];default:return[14,r(a[1])]}}function
B(a){if(typeof
a==="number")return[0,,function(a){},,function(a){}];switch(a[0]){case
0:var
b=B(a[1]),s=b[2];return[0,,function(a){s(0)},,b[4]];case
1:var
c=B(a[1]),t=c[2];return[0,,function(a){t(0)},,c[4]];case
2:var
d=B(a[1]),v=d[2];return[0,,function(a){v(0)},,d[4]];case
3:var
e=B(a[1]),w=e[2];return[0,,function(a){w(0)},,e[4]];case
4:var
f=B(a[1]),x=f[2];return[0,,function(a){x(0)},,f[4]];case
5:var
g=B(a[1]),y=g[2];return[0,,function(a){y(0)},,g[4]];case
6:var
h=B(a[1]),z=h[2];return[0,,function(a){z(0)},,h[4]];case
7:var
i=B(a[1]),A=i[2];return[0,,function(a){A(0)},,i[4]];case
8:var
j=B(a[2]),C=j[2];return[0,,function(a){C(0)},,j[4]];case
9:var
D=a[2],E=a[1],k=B(a[3]),F=k[4],G=k[2],l=B(u(r(E),D)),H=l[4],I=l[2];return[0,,function(a){I(0);G(0)},,function(a){H(0);F(0)}];case
10:var
m=B(a[1]),J=m[2];return[0,,function(a){J(0)},,m[4]];case
11:var
n=B(a[1]),K=n[2];return[0,,function(a){K(0)},,n[4]];case
12:var
o=B(a[1]),L=o[2];return[0,,function(a){L(0)},,o[4]];case
13:var
p=B(a[1]),M=p[4],N=p[2];return[0,,function(a){N(0)},,function(a){M(0)}];default:var
q=B(a[1]),O=q[4],P=q[2];return[0,,function(a){P(0)},,function(a){O(0)}]}}function
u(a,b){a:{b:{c:{d:{e:{f:{g:{if(typeof
a!=="number"){switch(a[0]){case
0:var
d=a[1];if(typeof
b!=="number")switch(b[0]){case
0:return[0,u(d,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
1:var
e=a[1];if(typeof
b!=="number")switch(b[0]){case
1:return[1,u(e,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
2:var
f=a[1];if(typeof
b!=="number")switch(b[0]){case
2:return[2,u(f,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
3:var
g=a[1];if(typeof
b!=="number")switch(b[0]){case
3:return[3,u(g,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
4:var
i=a[1];if(typeof
b!=="number")switch(b[0]){case
4:return[4,u(i,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
5:var
j=a[1];if(typeof
b!=="number")switch(b[0]){case
5:return[5,u(j,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
6:var
k=a[1];if(typeof
b!=="number")switch(b[0]){case
6:return[6,u(k,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
7:var
l=a[1];if(typeof
b!=="number")switch(b[0]){case
7:return[7,u(l,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
8:var
m=a[2],n=a[1];if(typeof
b!=="number")switch(b[0]){case
8:var
o=b[1],p=u(m,b[2]);return[8,u(n,o),p];case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}throw h([0,q,fd],1);case
9:var
s=a[3],t=a[2],v=a[1];if(typeof
b!=="number")switch(b[0]){case
8:break f;case
9:var
w=b[3],x=b[2],y=b[1],c=B(u(r(t),y)),z=c[4];c[2].call(null,0);z(0);return[9,v,x,u(s,w)];case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}throw h([0,q,fe],1);case
10:var
A=a[1];if(typeof
b!=="number"&&10===b[0])return[10,u(A,b[1])];throw h([0,q,ff],1);case
11:var
C=a[1];if(typeof
b!=="number")switch(b[0]){case
10:break a;case
11:return[11,u(C,b[1])]}throw h([0,q,fg],1);case
12:var
D=a[1];if(typeof
b!=="number")switch(b[0]){case
10:break a;case
11:break b;case
12:return[12,u(D,b[1])]}throw h([0,q,fh],1);case
13:var
E=a[1];if(typeof
b!=="number")switch(b[0]){case
10:break a;case
11:break b;case
12:break c;case
13:return[13,u(E,b[1])]}throw h([0,q,fi],1);default:var
F=a[1];if(typeof
b!=="number")switch(b[0]){case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:return[14,u(F,b[1])]}throw h([0,q,fj],1)}throw h([0,q,fc],1)}if(typeof
b==="number")return 0;switch(b[0]){case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e;case
8:break f;case
9:break;default:throw h([0,q,e6],1)}}throw h([0,q,e8],1)}throw h([0,q,e7],1)}throw h([0,q,fb],1)}throw h([0,q,fa],1)}throw h([0,q,e$],1)}throw h([0,q,e_],1)}throw h([0,q,e9],1)}var
v=[w,"CamlinternalFormat.Type_mismatch",d$(0)];function
et(a){return a?eu:ev}var
ex="\\\\",ey="\\'",ez="\\b",eA="\\t",eB="\\n",eC="\\r",fl=bL,fm="%+d",fn="% d",fo=da,fp="%+i",fq="% i",fr="%x",fs="%#x",ft="%X",fu="%#X",fv="%o",fw="%#o",fx=dx,fy="%Ld",fz="%+Ld",fA="% Ld",fB=c7,fC="%+Li",fD="% Li",fE="%Lx",fF="%#Lx",fG="%LX",fH="%#LX",fI="%Lo",fJ="%#Lo",fK="%Lu",fL="%ld",fM="%+ld",fN="% ld",fO=c2,fP="%+li",fQ="% li",fR="%lx",fS="%#lx",fT="%lX",fU="%#lX",fV="%lo",fW="%#lo",fX="%lu",fY="%nd",fZ="%+nd",f0="% nd",f1=dX,f2="%+ni",f3="% ni",f4="%nx",f5="%#nx",f6="%nX",f7="%#nX",f8="%no",f9="%#no",f_="%nu",f$=[0,cU],ge="neg_infinity",gf=dq,gg=bQ,gi=[0,a,1558,4],gj="Printf: bad conversion %[",gk=[0,a,1626,39],gl=[0,a,1649,31],gm=[0,a,1650,31],gn="Printf: bad conversion %_",go=di,gp=dH,gq=di,gr=dH;function
a5(a,b){if(typeof
a==="number")return[0,0,b];if(0===a[0])return[0,[0,a[1],a[2]],b];if(typeof
b!=="number"&&2===b[0])return[0,[1,a[1]],b[1]];throw h(v,1)}function
aP(a,b,c){var
d=a5(a,c);if(typeof
b!=="number")return[0,d[1],[0,b[1]],d[2]];if(!b)return[0,d[1],0,d[2]];var
e=d[2];if(typeof
e!=="number"&&2===e[0])return[0,d[1],1,e[1]];throw h(v,1)}function
l(a,b){if(typeof
a==="number")return[0,0,b];switch(a[0]){case
0:if(typeof
b!=="number"&&0===b[0]){var
w=l(a[1],b[1]);return[0,[0,w[1]],w[2]]}break;case
1:if(typeof
b!=="number"&&0===b[0]){var
x=l(a[1],b[1]);return[0,[1,x[1]],x[2]]}break;case
2:var
ag=a[2],y=a5(a[1],b),e=y[2],ah=y[1];if(typeof
e!=="number"&&1===e[0]){var
z=l(ag,e[1]);return[0,[2,ah,z[1]],z[2]]}throw h(v,1);case
3:var
ai=a[2],A=a5(a[1],b),f=A[2],aj=A[1];if(typeof
f!=="number"&&1===f[0]){var
B=l(ai,f[1]);return[0,[3,aj,B[1]],B[2]]}throw h(v,1);case
4:var
ak=a[4],al=a[1],g=aP(a[2],a[3],b),i=g[3],am=g[1];if(typeof
i!=="number"&&2===i[0]){var
an=g[2],C=l(ak,i[1]);return[0,[4,al,am,an,C[1]],C[2]]}throw h(v,1);case
5:var
ao=a[4],ap=a[1],j=aP(a[2],a[3],b),k=j[3],aq=j[1];if(typeof
k!=="number"&&3===k[0]){var
ar=j[2],D=l(ao,k[1]);return[0,[5,ap,aq,ar,D[1]],D[2]]}throw h(v,1);case
6:var
as=a[4],at=a[1],m=aP(a[2],a[3],b),o=m[3],au=m[1];if(typeof
o!=="number"&&4===o[0]){var
av=m[2],E=l(as,o[1]);return[0,[6,at,au,av,E[1]],E[2]]}throw h(v,1);case
7:var
aw=a[4],ax=a[1],p=aP(a[2],a[3],b),q=p[3],ay=p[1];if(typeof
q!=="number"&&5===q[0]){var
az=p[2],F=l(aw,q[1]);return[0,[7,ax,ay,az,F[1]],F[2]]}throw h(v,1);case
8:var
aA=a[4],aB=a[1],r=aP(a[2],a[3],b),s=r[3],aC=r[1];if(typeof
s!=="number"&&6===s[0]){var
aD=r[2],H=l(aA,s[1]);return[0,[8,aB,aC,aD,H[1]],H[2]]}throw h(v,1);case
9:var
aE=a[2],I=a5(a[1],b),t=I[2],aF=I[1];if(typeof
t!=="number"&&7===t[0]){var
K=l(aE,t[1]);return[0,[9,aF,K[1]],K[2]]}throw h(v,1);case
10:var
L=l(a[1],b);return[0,[10,L[1]],L[2]];case
11:var
aG=a[1],M=l(a[2],b);return[0,[11,aG,M[1]],M[2]];case
12:var
aH=a[1],N=l(a[2],b);return[0,[12,aH,N[1]],N[2]];case
13:if(typeof
b!=="number"&&8===b[0]){var
O=b[1],aI=b[2],aJ=a[3],aK=a[1];if(a1([0,a[2]],[0,O]))throw h(v,1);var
P=l(aJ,aI);return[0,[13,aK,O,P[1]],P[2]]}break;case
14:if(typeof
b!=="number"&&9===b[0]){var
Q=b[1],aL=b[3],aM=a[3],aN=a[2],aO=a[1],aQ=[0,n(Q)];if(a1([0,n(aN)],aQ))throw h(v,1);var
R=l(aM,n(aL));return[0,[14,aO,Q,R[1]],R[2]]}break;case
15:if(typeof
b!=="number"&&10===b[0]){var
S=l(a[1],b[1]);return[0,[15,S[1]],S[2]]}break;case
16:if(typeof
b!=="number"&&11===b[0]){var
T=l(a[1],b[1]);return[0,[16,T[1]],T[2]]}break;case
17:var
aR=a[1],U=l(a[2],b);return[0,[17,aR,U[1]],U[2]];case
18:var
V=a[2],u=a[1];if(0===u[0]){var
Z=u[1],aV=Z[2],_=l(Z[1],b),aW=_[1],$=l(V,_[2]);return[0,[18,[0,[0,aW,aV]],$[1]],$[2]]}var
aa=u[1],aX=aa[2],ab=l(aa[1],b),aY=ab[1],ac=l(V,ab[2]);return[0,[18,[1,[0,aY,aX]],ac[1]],ac[2]];case
19:if(typeof
b!=="number"&&13===b[0]){var
W=l(a[1],b[1]);return[0,[19,W[1]],W[2]]}break;case
20:if(typeof
b!=="number"&&1===b[0]){var
aS=a[2],aT=a[1],X=l(a[3],b[1]);return[0,[20,aT,aS,X[1]],X[2]]}break;case
21:if(typeof
b!=="number"&&2===b[0]){var
aU=a[1],Y=l(a[2],b[1]);return[0,[21,aU,Y[1]],Y[2]]}break;case
23:var
d=a[2],c=a[1];if(typeof
c!=="number")switch(c[0]){case
0:return J(c,d,b);case
1:return J(c,d,b);case
2:return J(c,d,b);case
3:return J(c,d,b);case
4:return J(c,d,b);case
5:return J(c,d,b);case
6:return J(c,d,b);case
7:return J(c,d,b);case
8:return J([8,c[1],c[2]],d,b);case
9:var
aZ=c[1],ae=G(c[2],d,b),af=ae[2];return[0,[23,[9,aZ,ae[1]],af[1]],af[2]];case
10:return J(c,d,b);default:return J(c,d,b)}switch(c){case
0:return J(c,d,b);case
1:return J(c,d,b);case
2:if(typeof
b!=="number"&&14===b[0]){var
ad=l(d,b[1]);return[0,[23,2,ad[1]],ad[2]]}throw h(v,1);default:return J(c,d,b)}}throw h(v,1)}function
J(a,b,c){var
d=l(b,c);return[0,[23,a,d[1]],d[2]]}function
G(a,b,c){if(typeof
a==="number")return[0,0,l(b,c)];switch(a[0]){case
0:if(typeof
c!=="number"&&0===c[0]){var
f=G(a[1],b,c[1]);return[0,[0,f[1]],f[2]]}break;case
1:if(typeof
c!=="number"&&1===c[0]){var
g=G(a[1],b,c[1]);return[0,[1,g[1]],g[2]]}break;case
2:if(typeof
c!=="number"&&2===c[0]){var
i=G(a[1],b,c[1]);return[0,[2,i[1]],i[2]]}break;case
3:if(typeof
c!=="number"&&3===c[0]){var
j=G(a[1],b,c[1]);return[0,[3,j[1]],j[2]]}break;case
4:if(typeof
c!=="number"&&4===c[0]){var
k=G(a[1],b,c[1]);return[0,[4,k[1]],k[2]]}break;case
5:if(typeof
c!=="number"&&5===c[0]){var
m=G(a[1],b,c[1]);return[0,[5,m[1]],m[2]]}break;case
6:if(typeof
c!=="number"&&6===c[0]){var
o=G(a[1],b,c[1]);return[0,[6,o[1]],o[2]]}break;case
7:if(typeof
c!=="number"&&7===c[0]){var
p=G(a[1],b,c[1]);return[0,[7,p[1]],p[2]]}break;case
8:if(typeof
c!=="number"&&8===c[0]){var
q=c[1],C=c[2],D=a[2];if(a1([0,a[1]],[0,q]))throw h(v,1);var
s=G(D,b,C);return[0,[8,q,s[1]],s[2]]}break;case
9:if(typeof
c!=="number"&&9===c[0]){var
d=c[2],e=c[1],E=c[3],F=a[3],H=a[2],I=a[1],J=[0,n(e)];if(a1([0,n(I)],J))throw h(v,1);var
K=[0,n(d)];if(a1([0,n(H)],K))throw h(v,1);var
t=B(u(r(e),d)),L=t[4];t[2].call(null,0);L(0);var
w=G(n(F),b,E),M=w[2];return[0,[9,e,d,r(w[1])],M]}break;case
10:if(typeof
c!=="number"&&10===c[0]){var
x=G(a[1],b,c[1]);return[0,[10,x[1]],x[2]]}break;case
11:if(typeof
c!=="number"&&11===c[0]){var
y=G(a[1],b,c[1]);return[0,[11,y[1]],y[2]]}break;case
13:if(typeof
c!=="number"&&13===c[0]){var
z=G(a[1],b,c[1]);return[0,[13,z[1]],z[2]]}break;case
14:if(typeof
c!=="number"&&14===c[0]){var
A=G(a[1],b,c[1]);return[0,[14,A[1]],A[2]]}break}throw h(v,1)}function
K(a,b,c){var
d=o(c),g=0<=b?a:0,f=a3(b);if(f<=d)return c;var
h=2===g?48:32,e=at(f,h);switch(g){case
0:X(c,0,e,0,d);break;case
1:X(c,0,e,f-d|0,d);break;default:a:if(0<d){if(43!==M(c,0)&&45!==M(c,0)&&32!==M(c,0))break a;ax(e,0,M(c,0));X(c,1,e,(f-d|0)+1|0,d-1|0);break}a:if(1<d&&48===M(c,0)){if(dE!==M(c,1)&&88!==M(c,1))break a;ax(e,1,M(c,1));X(c,2,e,(f-d|0)+2|0,d-2|0);break}X(c,0,e,f-d|0,d)}return H(e)}function
aB(a,b){var
d=a3(a),c=o(b),e=M(b,0);a:{b:{if(58>e){if(32!==e){if(43>e)break a;switch(e-43|0){case
5:c:if(c<(d+2|0)&&1<c){if(dE!==M(b,1)&&88!==M(b,1))break c;var
g=at(d+2|0,48);ax(g,1,M(b,1));X(b,2,g,(d-c|0)+4|0,c-2|0);return H(g)}break b;case
0:case
2:break;case
1:case
3:case
4:break a;default:break b}}if(c>=(d+1|0))break a;var
f=at(d+1|0,48);ax(f,0,e);X(b,1,f,(d-c|0)+2|0,c-1|0);return H(f)}if(71<=e){if(5<e+df>>>0)break a}else if(65>e)break a}if(c<d){var
h=at(d,48);X(b,0,h,d-c|0,c);return H(h)}}return b}function
fk(a){var
e=ah(a),b=[0,0],k=W(e)-1|0,s=0;if(k>=0){var
h=s;for(;;){var
f=aX(e,h);a:{b:{c:{if(32<=f){var
i=f-34|0;if(58<i>>>0){if(93<=i)break c}else if(56<i-1>>>0)break b;var
j=1;break a}if(11<=f){if(13===f)break b}else if(8<=f)break b}var
j=4;break a}var
j=2}b[1]=b[1]+j|0;var
v=h+1|0;if(k===h)break;h=v}}if(b[1]===W(e))var
m=e;else{var
c=y(b[1]);b[1]=0;var
l=W(e)-1|0,t=0;if(l>=0){var
g=t;for(;;){var
d=aX(e,g);a:{b:{c:{if(35<=d){if(92!==d){if(b2<=d)break c;break b}}else{if(32>d){if(14<=d)break c;switch(d){case
8:p(c,b[1],92);b[1]++;p(c,b[1],98);break a;case
9:p(c,b[1],92);b[1]++;p(c,b[1],116);break a;case
10:p(c,b[1],92);b[1]++;p(c,b[1],110);break a;case
13:p(c,b[1],92);b[1]++;p(c,b[1],114);break a;default:break c}}if(34>d)break b}p(c,b[1],92);b[1]++;p(c,b[1],d);break a}p(c,b[1],92);b[1]++;p(c,b[1],48+(d/dg|0)|0);b[1]++;p(c,b[1],48+((d/10|0)%10|0)|0);b[1]++;p(c,b[1],48+(d%10|0)|0);break a}p(c,b[1],d)}b[1]++;var
u=g+1|0;if(l===g)break;g=u}}var
m=c}var
r=H(m),n=o(r),q=at(n+2|0,34);bf(r,0,q,1,n);return H(q)}function
cE(a,b){var
g=a3(b),e=f$[1];switch(a[2]){case
0:var
c=102;break;case
1:var
c=101;break;case
2:var
c=69;break;case
3:var
c=cU;break;case
4:var
c=71;break;case
5:var
c=e;break;case
6:var
c=104;break;case
7:var
c=72;break;default:var
c=70}var
d=cA(16);aA(d,37);switch(a[1]){case
0:break;case
1:aA(d,43);break;default:aA(d,32)}if(8<=a[2])aA(d,35);aA(d,46);A(d,f+g);aA(d,c);return cC(d)}function
a6(a,b){if(13>a)return b;var
h=[0,0],i=o(b)-1|0,n=0;if(i>=0){var
d=n;for(;;){if(9>=az(b,d)+dz>>>0)h[1]++;var
r=d+1|0;if(i===d)break;d=r}}var
j=h[1],k=y(o(b)+((j-1|0)/3|0)|0),l=[0,0];function
e(a){ax(k,l[1],a);l[1]++}var
f=[0,((j-1|0)%3|0)+1|0],m=o(b)-1|0,p=0;if(m>=0){var
c=p;for(;;){var
g=az(b,c);if(9<g+dz>>>0)e(g);else{if(0===f[1]){e(95);f[1]=3}f[1]--;e(g)}var
q=c+1|0;if(m===c)break;c=q}}return H(k)}function
ga(a,b){switch(a){case
1:var
c=fm;break;case
2:var
c=fn;break;case
4:var
c=fp;break;case
5:var
c=fq;break;case
6:var
c=fr;break;case
7:var
c=fs;break;case
8:var
c=ft;break;case
9:var
c=fu;break;case
10:var
c=fv;break;case
11:var
c=fw;break;case
0:case
13:var
c=fl;break;case
3:case
14:var
c=fo;break;default:var
c=fx}return a6(a,bi(c,b))}function
gb(a,b){switch(a){case
1:var
c=fM;break;case
2:var
c=fN;break;case
4:var
c=fP;break;case
5:var
c=fQ;break;case
6:var
c=fR;break;case
7:var
c=fS;break;case
8:var
c=fT;break;case
9:var
c=fU;break;case
10:var
c=fV;break;case
11:var
c=fW;break;case
0:case
13:var
c=fL;break;case
3:case
14:var
c=fO;break;default:var
c=fX}return a6(a,bi(c,b))}function
gc(a,b){switch(a){case
1:var
c=fZ;break;case
2:var
c=f0;break;case
4:var
c=f2;break;case
5:var
c=f3;break;case
6:var
c=f4;break;case
7:var
c=f5;break;case
8:var
c=f6;break;case
9:var
c=f7;break;case
10:var
c=f8;break;case
11:var
c=f9;break;case
0:case
13:var
c=fY;break;case
3:case
14:var
c=f1;break;default:var
c=f_}return a6(a,bi(c,b))}function
gd(a,b){switch(a){case
1:var
c=fz;break;case
2:var
c=fA;break;case
4:var
c=fC;break;case
5:var
c=fD;break;case
6:var
c=fE;break;case
7:var
c=fF;break;case
8:var
c=fG;break;case
9:var
c=fH;break;case
10:var
c=fI;break;case
11:var
c=fJ;break;case
0:case
13:var
c=fy;break;case
3:case
14:var
c=fB;break;default:var
c=fK}return a6(a,hy(c,b))}function
aa(d,b,c){function
j(a){switch(d[1]){case
0:var
e=45;break;case
1:var
e=43;break;default:var
e=32}return hu(c,b,e)}function
r(a){var
b=hl(c);return 3===b?c<0.?ge:gf:4<=b?gg:a}switch(d[2]){case
5:var
f=ca(cE(d,b),c),e=0,v=o(f);for(;;){if(e===v)var
q=0;else{var
k=M(f,e)-46|0;a:{if(23<k>>>0){if(55===k)break a}else if(21<k-1>>>0)break a;e=e+1|0;continue}var
q=1}var
w=q?f:f+av;return r(w)}case
6:return j(0);case
7:var
i=ah(j(0)),g=W(i);if(0===g)var
n=i;else{var
l=y(g),m=g-1|0,s=0;if(m>=0){var
a=s;for(;;){var
h=aX(i,a),t=25<h+df>>>0?h:h-32|0;p(l,a,t);var
u=a+1|0;if(m===a)break;a=u}}var
n=l}return H(n);case
8:return r(j(0));default:return ca(cE(d,b),c)}}function
aR(a,b,c,d){var
f=b,e=c,g=d;for(;;){if(typeof
g==="number")return s(f,e);switch(g[0]){case
0:var
N=g[1];return function(a){return i(f,[5,e,a],N)};case
1:var
O=g[1];return function(a){a:{b:{if(40<=a){if(92===a){var
b=ex;break a}if(b2>a)break b}else{if(32<=a){if(39>a)break b;var
b=ey;break a}if(14>a)switch(a){case
8:var
b=ez;break a;case
9:var
b=eA;break a;case
10:var
b=eB;break a;case
13:var
b=eC;break a}}var
c=y(4);p(c,0,92);p(c,1,48+(a/dg|0)|0);p(c,2,48+((a/10|0)%10|0)|0);p(c,3,48+(a%10|0)|0);var
b=H(c);break a}var
d=y(1);p(d,0,a);var
b=H(d)}var
g=o(b),h=at(g+2|0,39);bf(b,0,h,1,g);return i(f,[4,e,H(h)],O)};case
2:return bz(f,e,g[2],g[1],function(a){return a});case
3:return bz(f,e,g[2],g[1],fk);case
4:return a7(f,e,g[4],g[2],g[3],ga,g[1]);case
5:return a7(f,e,g[4],g[2],g[3],gb,g[1]);case
6:return a7(f,e,g[4],g[2],g[3],gc,g[1]);case
7:return a7(f,e,g[4],g[2],g[3],gd,g[1]);case
8:var
u=g[4],w=g[3],x=g[2],t=g[1];if(typeof
x==="number"){if(typeof
w==="number")return w?function(a,b){return i(f,[4,e,aa(t,a,b)],u)}:function(a){return i(f,[4,e,aa(t,bx(t),a)],u)};var
_=w[1];return function(a){return i(f,[4,e,aa(t,_,a)],u)}}if(0===x[0]){var
C=x[2],D=x[1];if(typeof
w==="number")return w?function(a,b){return i(f,[4,e,K(D,C,aa(t,a,b))],u)}:function(a){return i(f,[4,e,K(D,C,aa(t,bx(t),a))],u)};var
$=w[1];return function(a){return i(f,[4,e,K(D,C,aa(t,$,a))],u)}}var
E=x[1];if(typeof
w==="number")return w?function(a,b,c){return i(f,[4,e,K(E,a,aa(t,b,c))],u)}:function(a,b){return i(f,[4,e,K(E,a,aa(t,bx(t),b))],u)};var
ab=w[1];return function(a,b){return i(f,[4,e,K(E,a,aa(t,ab,b))],u)};case
9:return bz(f,e,g[2],g[1],et);case
10:e=[7,e];g=g[1];break;case
11:e=[2,e,g[1]];g=g[2];break;case
12:e=[3,e,g[1]];g=g[2];break;case
13:var
Q=g[3],R=g[2],F=cA(16);by(F,R);var
M=cC(F);return function(a){return i(f,[4,e,M],Q)};case
14:var
S=g[3],T=g[2];return function(a){var
c=a[1],b=l(c,n(r(T)));if(typeof
b[2]==="number")return i(f,e,m(b[1],S));throw h(v,1)};case
15:var
U=g[1];return function(c,b){return i(f,[6,e,function(a){return P(c,a,b)}],U)};case
16:var
V=g[1];return function(a){return i(f,[6,e,a],V)};case
17:e=[0,e,g[1]];g=g[2];break;case
18:var
B=g[1];if(0===B[0]){let
b=e,c=f,d=g[2];f=function(a){return i(c,[1,b,[0,a]],d)};e=0;g=B[1][1]}else{let
b=e,c=f,d=g[2];f=function(a){return i(c,[1,b,[1,a]],d)};e=0;g=B[1][1]}break;case
19:throw h([0,q,gi],1);case
20:var
W=g[3],X=[8,e,gj];return function(a){return i(f,X,W)};case
21:var
Y=g[2];return function(a){return i(f,[4,e,bi(dx,a)],Y)};case
22:var
Z=g[1];return function(a){return i(f,[5,e,a],Z)};case
23:var
j=g[2],A=g[1];if(typeof
A==="number")switch(A){case
0:return a<50?k(a+1|0,f,e,j):z(k,[0,f,e,j]);case
1:return a<50?k(a+1|0,f,e,j):z(k,[0,f,e,j]);case
2:throw h([0,q,gk],1);default:return a<50?k(a+1|0,f,e,j):z(k,[0,f,e,j])}switch(A[0]){case
0:return a<50?k(a+1|0,f,e,j):z(k,[0,f,e,j]);case
1:return a<50?k(a+1|0,f,e,j):z(k,[0,f,e,j]);case
2:return a<50?k(a+1|0,f,e,j):z(k,[0,f,e,j]);case
3:return a<50?k(a+1|0,f,e,j):z(k,[0,f,e,j]);case
4:return a<50?k(a+1|0,f,e,j):z(k,[0,f,e,j]);case
5:return a<50?k(a+1|0,f,e,j):z(k,[0,f,e,j]);case
6:return a<50?k(a+1|0,f,e,j):z(k,[0,f,e,j]);case
7:return a<50?k(a+1|0,f,e,j):z(k,[0,f,e,j]);case
8:return a<50?k(a+1|0,f,e,j):z(k,[0,f,e,j]);case
9:var
L=A[2];return a<50?bI(a+1|0,f,e,L,j):z(bI,[0,f,e,L,j]);case
10:return a<50?k(a+1|0,f,e,j):z(k,[0,f,e,j]);default:return a<50?k(a+1|0,f,e,j):z(k,[0,f,e,j])}default:var
G=g[3],I=g[1],J=s(g[2],0);return a<50?bH(a+1|0,f,e,G,I,J):z(bH,[0,f,e,G,I,J])}}}function
i(a,b,c){return cl(aR(0,a,b,c))}function
bI(a,f,c,d,e){if(typeof
d==="number")return a<50?k(a+1|0,f,c,e):z(k,[0,f,c,e]);switch(d[0]){case
0:var
b=d[1];return function(a){return N(f,c,b,e)};case
1:var
g=d[1];return function(a){return N(f,c,g,e)};case
2:var
i=d[1];return function(a){return N(f,c,i,e)};case
3:var
j=d[1];return function(a){return N(f,c,j,e)};case
4:var
l=d[1];return function(a){return N(f,c,l,e)};case
5:var
m=d[1];return function(a){return N(f,c,m,e)};case
6:var
n=d[1];return function(a){return N(f,c,n,e)};case
7:var
o=d[1];return function(a){return N(f,c,o,e)};case
8:var
p=d[2];return function(a){return N(f,c,p,e)};case
9:var
s=d[3],t=d[2],v=u(r(d[1]),t);return function(a){return N(f,c,E(v,s),e)};case
10:var
w=d[1];return function(a,b){return N(f,c,w,e)};case
11:var
x=d[1];return function(a){return N(f,c,x,e)};case
12:var
y=d[1];return function(a){return N(f,c,y,e)};case
13:throw h([0,q,gl],1);default:throw h([0,q,gm],1)}}function
N(a,b,c,d){return cl(bI(0,a,b,c,d))}function
k(a,b,c,d){var
e=[8,c,gn];return a<50?aR(a+1|0,b,e,d):z(aR,[0,b,e,d])}function
bz(g,f,c,d,e){if(typeof
d==="number")return function(a){return i(g,[4,f,s(e,a)],c)};if(0===d[0]){var
b=d[2],h=d[1];return function(a){return i(g,[4,f,K(h,b,s(e,a))],c)}}var
j=d[1];return function(a,b){return i(g,[4,f,K(j,a,s(e,b))],c)}}function
a7(k,j,h,d,e,f,g){if(typeof
d==="number"){if(typeof
e==="number")return e?function(a,b){return i(k,[4,j,aB(a,P(f,g,b))],h)}:function(a){return i(k,[4,j,P(f,g,a)],h)};var
b=e[1];return function(a){return i(k,[4,j,aB(b,P(f,g,a))],h)}}if(0===d[0]){var
c=d[2],l=d[1];if(typeof
e==="number")return e?function(a,b){return i(k,[4,j,K(l,c,aB(a,P(f,g,b)))],h)}:function(a){return i(k,[4,j,K(l,c,P(f,g,a))],h)};var
n=e[1];return function(a){return i(k,[4,j,K(l,c,aB(n,P(f,g,a)))],h)}}var
m=d[1];if(typeof
e==="number")return e?function(a,b,c){return i(k,[4,j,K(m,a,aB(b,P(f,g,c)))],h)}:function(a,b){return i(k,[4,j,K(m,a,P(f,g,b))],h)};var
o=e[1];return function(a,b){return i(k,[4,j,K(m,a,aB(o,P(f,g,b)))],h)}}function
bH(a,b,c,d,e,f){if(e){var
h=e[1];return function(a){return gh(b,c,d,h,s(f,a))}}var
g=[4,c,f];return a<50?aR(a+1|0,b,g,d):z(aR,[0,b,g,d])}function
gh(a,b,c,d,e){return cl(bH(0,a,b,c,d,e))}function
ab(a,b){var
c=b;for(;;){if(typeof
c==="number")return;switch(c[0]){case
0:var
f=c[1],g=cD(c[2]);ab(a,f);return aM(a,g);case
1:var
d=c[2],e=c[1];if(0===d[0]){var
h=d[1];ab(a,e);aM(a,go);c=h}else{var
i=d[1];ab(a,e);aM(a,gp);c=i}break;case
6:var
l=c[2];ab(a,c[1]);return s(l,a);case
7:ab(a,c[1]);aj(a);return;case
8:var
m=c[2];ab(a,c[1]);return aL(m);case
2:case
4:var
j=c[2];ab(a,c[1]);return aM(a,j);default:var
k=c[2];ab(a,c[1]);eh(a,k);return}}}function
am(a,b){var
c=b;for(;;){if(typeof
c==="number")return;switch(c[0]){case
0:var
j=c[1],k=cD(c[2]);am(a,j);return aO(a,k);case
1:var
e=c[2],g=c[1];if(0===e[0]){var
l=e[1];am(a,g);aO(a,gq);c=l}else{var
m=e[1];am(a,g);aO(a,gr);c=m}break;case
6:var
o=c[2];am(a,c[1]);return aO(a,s(o,0));case
7:c=c[1];break;case
8:var
q=c[2];am(a,c[1]);return aL(q);case
2:case
4:var
n=c[2];am(a,c[1]);return aO(a,n);default:var
h=c[2];am(a,c[1]);var
d=a[2],f=a[1],i=f[1];if(f[2]<=d){cz(a,1);ax(a[1][1],a[2],h)}else
p(i,d,h);a[2]=d+1|0;return}}}function
a8(c,b){return i(function(a){ab(c,a);return 0},0,b[1])}function
O(a){return i(function(a){var
e=64,c=aN<64?aN:e,d=y(c),b=[0,[0,d,c],0,d];am(b,a);return cv(b[1][1],0,b[2])},0,a[1])}var
bu=[0,0],gs=[0,[3,0,0],"%S"],gt=dI,gu=[0,[4,0,0,0,0],bL],gv=f,gw=[0,[11,c4,[2,0,[2,0,0]]],", %s%s"],gx=[0,[12,40,[2,0,[2,0,[12,41,0]]]],"(%s%s)"],gy=f,gz=f,gA=[0,[12,40,[2,0,[12,41,0]]],"(%s)"],gB="Out of memory",gC="Stack overflow",gD="Pattern matching failed",gE="Assertion failed",gF="Undefined recursive module",gG="Raised at",gH="Re-raised at",gI="Raised by primitive operation at",gJ="Called from",gK=" (inlined)",gL=[0,[2,0,[12,32,[2,0,[11,' in file "',[2,0,[12,34,[2,0,[11,", line ",[4,0,0,0,[11,dU,hd]]]]]]]]]],'%s %s in file "%s"%s, line %d, characters %d-%d'],gM=f,gN=[0,[2,0,[11," unknown location",0]],"%s unknown location"],gO=[0,[2,0,[12,10,0]],"%s\n"];function
bB(a,b){var
c=a[1+b];if(!(1-(typeof
c==="number"?1:0)))return s(O(gu),c);if(bm(c)===ba)return s(O(gs),c);if(bm(c)!==253)return gt;var
e=ca("%.12g",c),d=0,g=o(e);for(;;){if(g<=d)return e+av;var
f=M(e,d);a:{if(48<=f){if(58>f)break a}else if(45===f)break a;return e}d=d+1|0}}function
cF(a,b){if(a.length-1<=b)return gv;var
c=cF(a,b+1|0),d=bB(a,b);return P(O(gw),d,c)}function
Y(a){a:{b:{var
b=b7(bu);for(;;){if(!b)break;c:{var
u=b[2],v=b[1];try{var
j=s(v,a)}catch(f){break c}if(j)break b}b=u}var
h=0;break a}var
h=[0,j[1]]}if(h)return h[1];if(a===bs)return gB;if(a===cr)return gC;if(a[1]===cq){var
e=a[2],m=e[3],x=e[2],y=e[1];return bJ(O(bA),y,x,m,m+5|0,gD)}if(a[1]===q){var
f=a[2],n=f[3],z=f[2],A=f[1];return bJ(O(bA),A,z,n,n+6|0,gE)}if(a[1]===ct){var
g=a[2],o=g[3],B=g[2],C=g[1];return bJ(O(bA),C,B,o,o+6|0,gF)}if(0===bm(a)){var
i=a.length-1,w=a[1][1];if(2<i>>>0)var
p=cF(a,2),r=bB(a,1),c=P(O(gx),r,p);else
switch(i){case
0:var
c=gy;break;case
1:var
c=gz;break;default:var
t=bB(a,1),c=s(O(gA),t)}var
d=[0,w,[0,c]]}else
var
d=[0,a[1],0];var
k=d[2],l=d[1];return k?l+k[1]:l}function
bC(a,b){var
f=hn(b),h=f.length-2|0,q=0;if(h>=0){var
d=q;for(;;){var
c=d6(f,d)[1+d];let
b=d;var
g=function(a){return a?0===b?gG:gH:0===b?gI:gJ};if(0===c[0])var
i=c[5],j=c[4],k=c[3],l=c[6]?gK:gM,m=c[2],n=c[7],o=g(c[1]),e=[0,he(O(gL),o,n,m,l,k,j,i)];else if(c[1])var
e=0;else
var
p=g(0),e=[0,s(O(gN),p)];if(e){var
r=e[1];s(a8(a,gO),r)}var
t=d+1|0;if(h===d)break;d=t}}}function
cG(a){for(;;){var
b=b7(bu),c=1-hf(bu,b,[0,a,b]);if(!c)return c}}var
gP=[0,f,"(Cannot print locations:\n bytecode executable program file not found)","(Cannot print locations:\n bytecode executable program file appears to be corrupt)","(Cannot print locations:\n bytecode executable program file has wrong magic number)","(Cannot print locations:\n bytecode executable program file cannot be opened;\n -- too many open files. Try running with OCAMLRUNPARAM=b=2)"].slice(),gQ=[0,[11,bZ,[2,0,[12,10,0]]],dy],gR=[0],gS="Fatal error: out of memory in uncaught exception handler",gT=[0,[11,bZ,[2,0,[12,10,0]]],dy],gU=[0,[11,"Fatal error in uncaught exception handler: exception ",[2,0,[12,10,0]]],"Fatal error in uncaught exception handler: exception %s\n"];ck(dL,function(a,b){try{try{var
g=b?gR:ea(0);try{bt(0)}catch(f){}try{var
f=Y(a);s(a8(S,gQ),f);bC(S,g);var
c=hO(0);if(c<0){var
d=a3(c);cu(d6(gP,d)[1+d])}var
n=aj(S),i=n}catch(f){var
k=$(f),l=Y(a);s(a8(S,gT),l);bC(S,g);var
m=Y(k);s(a8(S,gU),m);bC(S,ea(0));var
i=aj(S)}var
j=i}catch(f){var
e=$(f);if(e!==bs)throw h(e,0);var
j=cu(gS)}return j}catch(f){return 0}});var
cH=[w,"Jsoo_runtime.Error.Exn",d$(0)],bD=[0,cH,[0]],g8="font_size",hc="16px",g9="animation_speed",hb="1.0",g_="reduce_motion",g$="high_contrast",ha=[0,[11,"Applying customizations: font_size=",[2,0,[11,", animation_speed=",[2,0,[11,", reduce_motion=",[9,0,[11,", high_contrast=",[9,0,0]]]]]]]],"Applying customizations: font_size=%s, animation_speed=%s, reduce_motion=%b, high_contrast=%b"],g7=dt,g6=dt,g5=[0,[2,0,[2,0,[12,61,[2,0,[11,";expires=",[2,0,[11,";path=/",0]]]]]]],"%s%s=%s;expires=%s;path=/"],gY="default",gZ=dT,g0=cZ,g1=dl,g2=dd,g3=dV,g4=b4,eD=y(0),gV=bm(bD)===w?bD:bD[1];ck(bX,gV);(function(a){throw a});var
T=c,aC=null,bE=undefined;function
cI(a){return 1-(a==aC?1:0)}var
cJ=false;T.String;T.RegExp;T.Object;T.Math;T.Error;T.JSON;var
gW=T.Array,gX=T.Date;cG(function(a){return a[1]===cH?[0,I(a[2].toString())]:0});cG(function(a){return a
instanceof
gW?0:[0,I(a.toString())]});function
cL(d){return hM(function(a){if(cI(a)){var
e=s(d,a);if(1-(e|0))a.preventDefault();return e}var
c=event,b=s(d,c);if(1-(b|0))c.returnValue=b;return b})}var
D=T.document;1-(T.HTMLElement===bE?1:0);var
g=hL(0);function
aQ(a){switch(a){case
0:return gY;case
1:return gZ;case
2:return g0;case
3:return g1;case
4:return g2;default:return g3}}function
cM(a){return a!==dT?a!==cZ?a!==dV?a!==dd?a!==dl?0:3:4:5:2:1}function
cN(a,b){try{var
c=new
gX;c.setTime(c.getTime()+31536000000.);var
e=I(c.toUTCString());D.cookie=j(cR(O(g5),g4,a,b,e));var
f=g.log(j("Cookie set: "+b4+a+" = "+b));return f}catch(f){var
d=$(f);return g.log(j("Error setting cookie: "+Y(d)))}}function
aD(a){try{var
k=I(D.cookie),l=b4+a+"=",n=[0,0],p=[0,o(k)],r=o(k)-1|0;if(r>=0){var
c=r;for(;;){if(az(k,c)===59){var
w=n[1];n[1]=[0,a4(k,c+1|0,(p[1]-c|0)-1|0),w];p[1]=c}var
x=c-1|0;if(0===c)break;c=x}}var
v=n[1],e=[0,a4(k,0,p[1]),v];for(;;){if(e){var
s=e[2],d=e[1];a:if(d===f)var
h=d;else{if(!cy(az(d,0))&&!cy(az(d,o(d)-1|0))){var
h=d;break a}var
m=ah(d),q=W(m),b=[0,0];for(;;){if(b[1]>=q)break;if(!cx(aX(m,b[1])))break;b[1]++}var
i=[0,q-1|0];for(;;){if(b[1]>i[1])break;if(!cx(aX(m,i[1])))break;i[1]--}var
u=b[1]<=i[1]?bw(m,b[1],(i[1]-b[1]|0)+1|0):eD,h=H(u)}if(o(l)>o(h)){e=s;continue}if(a4(h,0,o(l))!==l){e=s;continue}var
t=[0,a4(h,o(l),o(h)-o(l)|0)]}else
var
t=e;return t}}catch(f){var
y=$(f);g.log(j("Error getting cookie: "+Y(y)));return 0}}function
a9(a){try{var
b=aQ(a);g.log(j("Applying theme: "+b));bv(function(a){var
b=j(aQ(a)+cT);return D.body.classList.remove(b)},bF);if(0!==a)D.body.classList.add(j(b+cT));cN(g6,b);bv(function(a){var
b=D.getElementById(j(dS+aQ(a))),c=1-(b==aC?1:0);return c?b.classList.remove(dC):c},bF);var
c=D.getElementById(j(dS+b));if(1-(c==aC?1:0))c.classList.add(dC);var
e=g.log(j("Theme applied successfully: "+b));return e}catch(f){var
d=$(f);return g.log(j("Error applying theme: "+Y(d)))}}function
cO(a){try{var
b=aD(g7),c=b?cM(b[1]):b;g.log(j("Loading saved theme: "+aQ(c)));var
e=a9(c);return e}catch(f){var
d=$(f);return g.log(j("Error loading theme: "+Y(d)))}}function
cP(a){try{var
d=aD(g8),e=d?d[1]+"px":hc,f=aD(g9),h=f?f[1]:hb,i=aD(g_);a:{if(i&&i[1]===bR){var
b=1;break a}var
b=0}var
k=aD(g$);a:{if(k&&k[1]===bR){var
c=1;break a}var
c=0}g.log(j(cR(O(ha),e,h,b,c)));D.documentElement.style.setProperty("--base-font-size",j(e),bE);D.documentElement.style.setProperty("--animation-speed",j(h),bE);if(b)D.body.classList.add(dc);else
D.body.classList.remove(dc);var
m=c?D.body.classList.add(dK):D.body.classList.remove(dK);return m}catch(f){var
l=$(f);return g.log(j("Error applying customizations: "+Y(l)))}}function
bG(a){try{g.log("H42N42 Standalone Theme System initializing...");cO(0);cP(0);if(cI(D.querySelector(".theme-content"))){g.log("Theme page detected, initializing theme controls...");try{g.log("Initializing theme controls...");bv(function(b){var
c=aQ(b),e="apply-"+c,a=D.getElementById(j(e)),d=1-(a==aC?1:0);return d?(g.log(j("Found theme button: "+e)),a.onclick=cL(function(a){g.log(j("Theme button clicked: "+c));a9(b);return cJ})):d},bF);var
b=D.getElementById("reset-theme");if(1-(b==aC?1:0)){g.log("Found reset button");b.onclick=cL(function(a){g.log("Reset button clicked");a9(0);return cJ})}g.log("Theme controls initialized")}catch(f){var
c=$(f);g.log(j("Error initializing theme controls: "+Y(c)))}}else
g.log("No theme page detected");var
e=g.log("H42N42 Standalone Theme System initialized successfully");return e}catch(f){var
d=$(f);return g.log(j("Error initializing theme system: "+Y(d)))}}var
a_={loadTheme:aI(function(a){return cO}),applyTheme:aI(function(a,b){return a9(cM(b))}),applyCustomizations:aI(function(a){return cP}),init:aI(function(a){return bG}),getCookie:aI(function(a,b){var
c=aD(b);return c?j(c[1]):aC}),setCookie:aI(function(a,b,c){return cN(b,c)})};a:{if(I(typeof
a_)==="function"&&0<a_.length){var
cK=bl(a_);break a}var
cK=a_}h3.H42N42ThemeDebug=cK;g.log("Theme standalone script loaded");if(I(D.readyState)===cX){g.log("DOM still loading, waiting...");var
cQ=function(a){return I(D.readyState)===cX?(T.setTimeout(bl(cQ),10.),0):(g.log("DOM ready detected"),bG(0))};cQ(0)}else{g.log("DOM already loaded, initializing immediately");bG(0)}bt(0);return}(globalThis));
