// Generated by js_of_ocaml
//# buildInfo:effects=false, kind=exe, use-js-string=true, version=5.9.1
(function(a){typeof
globalThis!=="object"&&(this?b():(a.defineProperty(a.prototype,"_T_",{configurable:true,get:b}),_T_));function
b(){var
b=this||self;b.globalThis=b;delete
a.prototype._T_}}(Object));(function(c){"use strict";var
il=typeof
module==="object"&&module.exports||c,aN=10.,ac=" ",dI="Invalid ",O="0",cf="compare: functional value",a0=1000,ce="mkdir",dV="@[",dH="%u",dU="/static/",dT="s",cm="node:fs",dm="%i",dn="dir",d_="%ni",ae=0xff,cl="initial-creets",dS="Assert_failure",dl="0x",dG=0x7ff0,dk=0xdfff,dF="End_of_file",dR=0.5,dj=": closedir failed",d9="Out_of_memory",aq="auto-heal",di="Not_found",ck="Failure",bn="Unix.Unix_error",dE="^",b_="ENOTDIR",cd="mean-chance",dh="%Li",dg="Invalid_argument",bi=254,aL="+",dQ="2",d8="false",b5=20.,d7=", characters ",ab=0xffffff,d6=".settings-content",dP=120,dO=1027,aZ=1024,df="15",dD="Pervasives.do_at_exit",dC=65536,b9=0xf,dd=", ",de=512,d4="Match_failure",d5="closedir",db="%li",dc=1026,b4="nan",e="",b3="rmdir",cc="Fatal error: exception ",dB="infinity",a="camlinternalFormat.ml",dA="fd ",d3=": ",w=248,d2=0xe0,dN=24029,dz=0xf0,b2="infection-rate",dy="Sys_error",V=0x80,dM="ENOTEMPTY",d1="EEXIST",cb=1255,b1=" : flags Open_text and Open_binary are not compatible",b8="e",b0=": Not a directory",b7="ENOENT",dL="Stack_overflow",d0="Undefined_recursive_module",Z=0x8000,dx=0x800,dK=-48,bm="1.0",c$="error",da=" : is a directory",dv="@{",dw="Division_by_zero",aA=".",aO=0x3f,cj="berserk-chance",b6=" : file already exists",aY=128,a1=": No such file or directory",du="spawn-delay",dt=255,c_=",",dZ=256,ds=100,bZ="index out of bounds",ad="/",bh=252,bY="%d",bX="%",c9=": file descriptor already closed",az="-",bW="EBADF",dr="true",c8="loading",dq=-97,aM="10",dY="Printexc.handle_uncaught_exception",bl="h42n42_",c7=12520,ci=" : flags Open_rdonly and Open_wronly are not compatible",bj="difficulty-progression",dJ="Fatal error: exception %s\n",c5="3",c6=250,dp="([^/]+)",ca="jsError",c4=103,bg='"',_=0xffff,ch=127,c3=0xdc00,dX="Sys_blocked_io",b$="heal-time",cg="game-speed",dW="_",bk="_bigarr02";function
hx(a,b,c){if(a[1]===b){a[1]=c;return 1}return 0}function
cn(a){return a[1]}function
by(a,b,c){var
d=String.fromCharCode;if(b===0&&c<=4096&&c===a.length)return d.apply(null,a);var
f=e;for(;0<c;b+=aZ,c-=aZ)f+=d.apply(null,a.slice(b,b+Math.min(c,aZ)));return f}function
bp(a){var
c=new
Uint8Array(a.l),e=a.c,d=e.length,b=0;for(;b<d;b++)c[b]=e.charCodeAt(b);for(d=a.l;b<d;b++)c[b]=0;a.c=c;a.t=4;return c}function
as(a,b,c,d,e){if(e===0)return 0;if(d===0&&(e>=c.l||c.t===2&&e>=c.c.length)){c.c=a.t===4?by(a.c,b,e):b===0&&a.c.length===e?a.c:a.c.slice(b,b+e);c.t=c.c.length===c.l?0:2}else if(c.t===2&&d===c.c.length){c.c+=a.t===4?by(a.c,b,e):b===0&&a.c.length===e?a.c:a.c.slice(b,b+e);c.t=c.c.length===c.l?0:2}else{if(c.t!==4)bp(c);var
g=a.c,h=c.c;if(a.t===4)if(d<=b)for(var
f=0;f<e;f++)h[d+f]=g[b+f];else
for(var
f=e-1;f>=0;f--)h[d+f]=g[b+f];else{var
i=Math.min(e,g.length-b);for(var
f=0;f<i;f++)h[d+f]=g.charCodeAt(b+f);for(;f<e;f++)h[d+f]=0}}return 0}function
aR(a,b){if(a===0)return e;if(b.repeat)return b.repeat(a);var
d=e,c=0;for(;;){if(a&1)d+=b;a>>=1;if(a===0)return d;b+=b;c++;if(c===9)b.slice(0,1)}}function
bq(a){if(a.t===2)a.c+=aR(a.l-a.c.length,"\0");else
a.c=by(a.c,0,a.c.length);a.t=0}function
cE(a){if(a.length<24){for(var
b=0;b<a.length;b++)if(a.charCodeAt(b)>ch)return false;return true}else
return!/[^\x00-\x7f]/.test(a)}function
eC(a){for(var
k=e,d=e,h,g,i,b,c=0,j=a.length;c<j;c++){g=a.charCodeAt(c);if(g<V){for(var
f=c+1;f<j&&(g=a.charCodeAt(f))<V;f++);if(f-c>de){d.slice(0,1);k+=d;d=e;k+=a.slice(c,f)}else
d+=a.slice(c,f);if(f===j)break;c=f}b=1;if(++c<j&&((i=a.charCodeAt(c))&-64)===aY){h=i+(g<<6);if(g<d2){b=h-0x3080;if(b<V)b=1}else{b=2;if(++c<j&&((i=a.charCodeAt(c))&-64)===aY){h=i+(h<<6);if(g<dz){b=h-0xe2080;if(b<dx||b>=0xd7ff&&b<0xe000)b=2}else{b=3;if(++c<j&&((i=a.charCodeAt(c))&-64)===aY&&g<0xf5){b=i-0x3c82080+(h<<6);if(b<0x10000||b>0x10ffff)b=3}}}}}if(b<4){c-=b;d+="\ufffd"}else if(b>_)d+=String.fromCharCode(0xd7c0+(b>>10),c3+(b&0x3ff));else
d+=String.fromCharCode(b);if(d.length>aZ){d.slice(0,1);k+=d;d=e}}return k+d}function
af(a,b,c){this.t=a;this.c=b;this.l=c}af.prototype.toString=function(){switch(this.t){case
9:case
8:return this.c;case
4:case
2:bq(this);case
0:if(cE(this.c))this.t=9;else
this.t=8;return this.c}};af.prototype.toUtf16=function(){var
a=this.toString();if(this.t===9)return a;return eC(a)};af.prototype.slice=function(){var
a=this.t===4?this.c.slice():this.c;return new
af(this.t,a,this.l)};function
eg(a){return new
af(0,a,a.length)}function
aj(a){return a}function
ag(a){return eg(aj(a))}function
aC(a,b,c,d,e){as(ag(a),b,c,d,e);return 0}var
eE={};function
io(a){if(eE[a])return eE[a];var
b=c.process;if(b&&b.env&&b.env[a]!==undefined)return b.env[a];if(c.jsoo_env&&typeof
c.jsoo_env[a]==="string")return c.jsoo_env[a]}var
bw=0;(function(){var
c=io("OCAMLRUNPARAM");if(c!==undefined){var
b=c.split(c_);for(var
a=0;a<b.length;a++)if(b[a]==="b"){bw=1;break}else if(b[a].startsWith("b="))bw=+b[a].slice(2);else
continue}}());var
ie=bw,C=[0];function
hH(a,b){if(!a.js_error||b||a[0]===w)a.js_error=new
c.Error("Js exception containing backtrace");return a}function
h(a,b){return bw&&ie?hH(a,b):a}function
id(a,b){throw h([0,a,b])}function
T(a){return a}function
cB(a,b){id(a,T(b))}function
f(a){cB(C.Invalid_argument,a)}function
hB(){f(bZ)}function
p(a,b,c){c&=ae;if(a.t!==4){if(b===a.c.length){a.c+=String.fromCharCode(c);if(b+1===a.l)a.t=0;return 0}bp(a)}a.c[b]=c;return 0}function
aD(a,b,c){if(b>>>0>=a.l)hB();return p(a,b,c)}function
a3(a,b){switch(a.t&6){case
0:return a.c.charCodeAt(b);case
2:if(b>=a.c.length)return 0;return a.c.charCodeAt(b);case
4:return a.c[b]}}function
ah(d,c){var
f=d.l>=0?d.l:d.l=d.length,e=c.length,b=f-e;if(b===0)return d.apply(null,c);else if(b<0){var
a=d.apply(null,c.slice(0,f));if(typeof
a!=="function")return a;return ah(a,c.slice(f))}else{switch(b){case
1:{var
a=function(a){var
f=new
Array(e+1);for(var
b=0;b<e;b++)f[b]=c[b];f[e]=a;return d.apply(null,f)};break}case
2:{var
a=function(a,b){var
g=new
Array(e+2);for(var
f=0;f<e;f++)g[f]=c[f];g[e]=a;g[e+1]=b;return d.apply(null,g)};break}default:var
a=function(){var
e=arguments.length===0?1:arguments.length,b=new
Array(c.length+e);for(var
a=0;a<c.length;a++)b[a]=c[a];for(var
a=0;a<arguments.length;a++)b[c.length+a]=arguments[a];return ah(d,b)}}a.l=b;return a}}function
bo(){f(bZ)}function
eh(a,b){if(b>>>0>=a.length-1)bo();return a}function
hD(a){if(Number.isFinite(a)){if(Math.abs(a)>=2.2250738585072014e-308)return 0;if(a!==0)return 1;return 2}return Number.isNaN(a)?4:3}function
hF(){return[0]}function
u(a){if(a<0)f("Bytes.create");return new
af(a?2:9,e,a)}function
hI(a,b,c,d){if(c>0)if(b===0&&(c>=a.l||a.t===2&&c>=a.c.length))if(d===0){a.c=e;a.t=2}else{a.c=aR(c,String.fromCharCode(d));a.t=c===a.l?0:2}else{if(a.t!==4)bp(a);for(c+=b;b<c;b++)a.c[b]=d}return 0}function
at(a){if(!C.Failure)C.Failure=[w,T(ck),-3];cB(C.Failure,a)}function
em(a){var
b,f=/^ *[-+]?(?:\d*\.?\d+|\d+\.?\d*)(?:[eE][-+]?\d+)?$/;a=aj(a);b=+a;if(!Number.isNaN(b)&&f.test(a))return b;a=a.replace(/_/g,e);b=+a;if(!Number.isNaN(b)&&f.test(a)||/^[+-]?nan$/i.test(a))return b;var
c=/^ *([+-]?)0x([0-9a-f]+)\.?([0-9a-f]*)(p([+-]?[0-9]+))?$/i.exec(a);if(c){var
d=c[3].replace(/0+$/,e),h=Number.parseInt(c[1]+c[2]+d,16),g=(c[5]|0)-4*d.length;b=h*Math.pow(2,g);return b}if(/^\+?inf(inity)?$/i.test(a))return Number.POSITIVE_INFINITY;if(/^-inf(inity)?$/i.test(a))return Number.NEGATIVE_INFINITY;at("float_of_string")}function
cA(a){a=aj(a);var
e=a.length;if(e>31)f("format_int: format too long");var
b={justify:aL,signstyle:az,filler:ac,alternate:false,base:0,signedconv:false,width:0,uppercase:false,sign:1,prec:-1,conv:"f"};for(var
d=0;d<e;d++){var
c=a.charAt(d);switch(c){case"-":b.justify=az;break;case"+":case" ":b.signstyle=c;break;case"0":b.filler=O;break;case"#":b.alternate=true;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":b.width=0;while(c=a.charCodeAt(d)-48,c>=0&&c<=9){b.width=b.width*10+c;d++}d--;break;case".":b.prec=0;d++;while(c=a.charCodeAt(d)-48,c>=0&&c<=9){b.prec=b.prec*10+c;d++}d--;break;case"d":case"i":b.signedconv=true;b.base=10;break;case"u":b.base=10;break;case"x":b.base=16;break;case"X":b.base=16;b.uppercase=true;break;case"o":b.base=8;break;case"e":case"f":case"g":b.signedconv=true;b.conv=c;break;case"E":case"F":case"G":b.signedconv=true;b.uppercase=true;b.conv=c.toLowerCase();break}}return b}function
cr(a,b){if(a.uppercase)b=b.toUpperCase();var
f=b.length;if(a.signedconv&&(a.sign<0||a.signstyle!==az))f++;if(a.alternate){if(a.base===8)f+=1;if(a.base===16)f+=2}var
c=e;if(a.justify===aL&&a.filler===ac)for(var
d=f;d<a.width;d++)c+=ac;if(a.signedconv)if(a.sign<0)c+=az;else if(a.signstyle!==az)c+=a.signstyle;if(a.alternate&&a.base===8)c+=O;if(a.alternate&&a.base===16)c+=a.uppercase?"0X":dl;if(a.justify===aL&&a.filler===O)for(var
d=f;d<a.width;d++)c+=O;c+=b;if(a.justify===az)for(var
d=f;d<a.width;d++)c+=ac;return T(c)}function
cs(a,b){function
j(a,b){if(Math.abs(a)<1.0)return a.toFixed(b);else{var
c=Number.parseInt(a.toString().split(aL)[1]);if(c>20){c-=20;a/=Math.pow(10,c);a+=new
Array(c+1).join(O);if(b>0)a=a+aA+new
Array(b+1).join(O);return a}else
return a.toFixed(b)}}var
c,f=cA(a),e=f.prec<0?6:f.prec;if(b<0||b===0&&1/b===Number.NEGATIVE_INFINITY){f.sign=-1;b=-b}if(Number.isNaN(b)){c=b4;f.filler=ac}else if(!Number.isFinite(b)){c="inf";f.filler=ac}else
switch(f.conv){case"e":var
c=b.toExponential(e),d=c.length;if(c.charAt(d-3)===b8)c=c.slice(0,d-1)+O+c.slice(d-1);break;case"f":c=j(b,e);break;case"g":e=e?e:1;c=b.toExponential(e-1);var
i=c.indexOf(b8),h=+c.slice(i+1);if(h<-4||b>=1e21||b.toFixed(0).length>e){var
d=i-1;while(c.charAt(d)===O)d--;if(c.charAt(d)===aA)d--;c=c.slice(0,d+1)+c.slice(i);d=c.length;if(c.charAt(d-3)===b8)c=c.slice(0,d-1)+O+c.slice(d-1);break}else{var
g=e;if(h<0){g-=h+1;c=b.toFixed(g)}else
while(c=b.toFixed(g),c.length>e+1)g--;if(g){var
d=c.length-1;while(c.charAt(d)===O)d--;if(c.charAt(d)===aA)d--;c=c.slice(0,d+1)}}break}return cr(f,c)}function
br(a,b){if(aj(a)===bY)return T(e+b);var
c=cA(a);if(b<0)if(c.signedconv){c.sign=-1;b=-b}else
b>>>=0;var
d=b.toString(c.base);if(c.prec>=0){c.filler=ac;var
f=c.prec-d.length;if(f>0)d=aR(f,O)+d}return cr(c,d)}var
ib=0;function
en(){return ib++}function
j(a){if(cE(a))return a;return eC(a)}function
a9(){return typeof
c.process!=="undefined"&&typeof
c.process.versions!=="undefined"&&typeof
c.process.versions.node!=="undefined"}function
iq(){function
a(a){if(a.charAt(0)===ad)return[e,a.slice(1)];return}function
b(a){var
h=/^([a-zA-Z]:|[\\/]{2}[^\\/]+[\\/]+[^\\/]+)?([\\/])?([\s\S]*?)$/,b=h.exec(a),c=b[1]||e,f=c.length>0&&c.charAt(1)!==":";if(b[2]||f){var
d=b[1]||e,g=b[2]||e;return[d,a.slice(d.length+g.length)]}return}return a9()&&c.process&&c.process.platform?c.process.platform==="win32"?b:a:a}var
cF=iq();function
eA(a){return a.slice(-1)!==ad?a+ad:a}if(a9()&&c.process&&c.process.cwd)var
a4=c.process.cwd().replace(/\\/g,ad);else
var
a4="/static";a4=eA(a4);function
h6(a){a=j(a);if(!cF(a))a=a4+a;var
e=cF(a),d=e[1].split(/[/\\]/),b=[];for(var
c=0;c<d.length;c++)switch(d[c]){case"..":if(b.length>1)b.pop();break;case".":break;case"":break;default:b.push(d[c]);break}b.unshift(e[0]);b.orig=a;return b}function
ik(a){for(var
g=e,c=g,b,i,d=0,h=a.length;d<h;d++){b=a.charCodeAt(d);if(b<V){for(var
f=d+1;f<h&&(b=a.charCodeAt(f))<V;f++);if(f-d>de){c.slice(0,1);g+=c;c=e;g+=a.slice(d,f)}else
c+=a.slice(d,f);if(f===h)break;d=f}if(b<dx){c+=String.fromCharCode(0xc0|b>>6);c+=String.fromCharCode(V|b&aO)}else if(b<0xd800||b>=dk)c+=String.fromCharCode(d2|b>>12,V|b>>6&aO,V|b&aO);else if(b>=0xdbff||d+1===h||(i=a.charCodeAt(d+1))<c3||i>dk)c+="\xef\xbf\xbd";else{d++;b=(b<<10)+i-0x35fdc00;c+=String.fromCharCode(dz|b>>18,V|b>>12&aO,V|b>>6&aO,V|b&aO)}if(c.length>aZ){c.slice(0,1);g+=c;c=e}}return g+c}function
D(a){return cE(a)?T(a):T(ik(a))}var
ir=["E2BIG","EACCES","EAGAIN",bW,"EBUSY","ECHILD","EDEADLK","EDOM",d1,"EFAULT","EFBIG","EINTR","EINVAL","EIO","EISDIR","EMFILE","EMLINK","ENAMETOOLONG","ENFILE","ENODEV",b7,"ENOEXEC","ENOLCK","ENOMEM","ENOSPC","ENOSYS",b_,dM,"ENOTTY","ENXIO","EPERM","EPIPE","ERANGE","EROFS","ESPIPE","ESRCH","EXDEV","EWOULDBLOCK","EINPROGRESS","EALREADY","ENOTSOCK","EDESTADDRREQ","EMSGSIZE","EPROTOTYPE","ENOPROTOOPT","EPROTONOSUPPORT","ESOCKTNOSUPPORT","EOPNOTSUPP","EPFNOSUPPORT","EAFNOSUPPORT","EADDRINUSE","EADDRNOTAVAIL","ENETDOWN","ENETUNREACH","ENETRESET","ECONNABORTED","ECONNRESET","ENOBUFS","EISCONN","ENOTCONN","ESHUTDOWN","ETOOMANYREFS","ETIMEDOUT","ECONNREFUSED","EHOSTDOWN","EHOSTUNREACH","ELOOP","EOVERFLOW"];function
am(a,b,c,d){var
f=ir.indexOf(a);if(f<0){if(d==null)d=-9999;f=[0,d]}var
g=[f,D(b||e),D(c||e)];return g}var
ex={};function
av(a){return ex[a]}function
al(a,b){throw h([0,a].concat(b))}function
cy(a){return a
instanceof
af}function
cz(a){return typeof
a==="string"&&!/[^\x00-\xff]/.test(a)}function
cp(a){if(!(a
instanceof
Uint8Array))a=new
Uint8Array(a);return new
af(4,a,a.length)}function
g(a){cB(C.Sys_error,a)}function
ey(a){g(a+a1)}function
eB(a){if(a.t!==4)bp(a);return a.c}function
X(a){return a.l}function
d$(){}function
H(a){this.data=a}H.prototype=new
d$();H.prototype.constructor=H;H.prototype.truncate=function(a){var
b=this.data;this.data=u(a|0);as(b,0,this.data,0,a)};H.prototype.length=function(){return X(this.data)};H.prototype.write=function(a,b,c,d){var
e=this.length();if(a+d>=e){var
f=u(a+d),g=this.data;this.data=f;as(g,0,this.data,0,e)}as(cp(b),c,this.data,a,d);return 0};H.prototype.read=function(a,b,c,d){var
e=this.length();if(a+d>=e)d=e-a;if(d){var
f=u(d|0);as(this.data,a,f,0,d);b.set(eB(f),c)}return d};function
aB(a,b,c){this.file=b;this.name=a;this.flags=c}aB.prototype.err_closed=function(){g(this.name+c9)};aB.prototype.length=function(){if(this.file)return this.file.length();this.err_closed()};aB.prototype.write=function(a,b,c,d){if(this.file)return this.file.write(a,b,c,d);this.err_closed()};aB.prototype.read=function(a,b,c,d){if(this.file)return this.file.read(a,b,c,d);this.err_closed()};aB.prototype.close=function(){this.file=undefined};function
x(a,b){this.content={};this.root=a;this.lookupFun=b}x.prototype.nm=function(a){return this.root+a};x.prototype.create_dir_if_needed=function(a){var
d=a.split(ad),c=e;for(var
b=0;b<d.length-1;b++){c+=d[b]+ad;if(this.content[c])continue;this.content[c]=Symbol("directory")}};x.prototype.slash=function(a){return/\/$/.test(a)?a:a+ad};x.prototype.lookup=function(a){if(!this.content[a]&&this.lookupFun){var
b=this.lookupFun(T(this.root),T(a));if(b!==0){this.create_dir_if_needed(a);this.content[a]=new
H(ag(b[1]))}}};x.prototype.exists=function(a,b){if(a===e)return 1;var
c=this.slash(a);if(this.content[c])return 1;if(!b)this.lookup(a);return this.content[a]?1:0};x.prototype.isFile=function(a){return this.exists(a)&&!this.is_dir(a)?1:0};x.prototype.mkdir=function(a,b,c){var
f=c&&av(bn);if(this.exists(a))if(f)al(f,am(d1,ce,this.nm(a)));else
g(a+": File exists");var
d=/^(.*)\/[^/]+/.exec(a);d=d&&d[1]||e;if(!this.exists(d))if(f)al(f,am(b7,ce,this.nm(d)));else
g(d+a1);if(!this.is_dir(d))if(f)al(f,am(b_,ce,this.nm(d)));else
g(d+b0);this.create_dir_if_needed(this.slash(a))};x.prototype.rmdir=function(a,b){var
c=b&&av(bn),d=a===e?e:this.slash(a),h=new
RegExp(dE+d+dp);if(!this.exists(a))if(c)al(c,am(b7,b3,this.nm(a)));else
g(a+a1);if(!this.is_dir(a))if(c)al(c,am(b_,b3,this.nm(a)));else
g(a+b0);for(var
f
in
this.content)if(f.match(h))if(c)al(c,am(dM,b3,this.nm(a)));else
g(this.nm(a)+": Directory not empty");delete
this.content[d]};x.prototype.readdir=function(a){var
h=a===e?e:this.slash(a);if(!this.exists(a))g(a+a1);if(!this.is_dir(a))g(a+b0);var
i=new
RegExp(dE+h+dp),d={},c=[];for(var
f
in
this.content){var
b=f.match(i);if(b&&!d[b[1]]){d[b[1]]=true;c.push(b[1])}}return c};x.prototype.opendir=function(a,b){var
c=b&&av(bn),d=this.readdir(a),e=false,f=0;return{readSync:function(){if(e)if(c)al(c,am(bW,d5,this.nm(a)));else
g(a+dj);if(f===d.length)return null;var
b=d[f];f++;return{name:b}},closeSync:function(){if(e)if(c)al(c,am(bW,d5,this.nm(a)));else
g(a+dj);e=true;d=[]}}};x.prototype.is_dir=function(a){if(a===e)return true;var
b=this.slash(a);return this.content[b]?1:0};x.prototype.unlink=function(a){if(!this.exists(a,true))g(a+a1);delete
this.content[a];return 0};x.prototype.open=function(a,b){var
c;if(b.rdonly&&b.wronly)g(this.nm(a)+ci);if(b.text&&b.binary)g(this.nm(a)+b1);this.lookup(a);if(this.content[a]){if(this.is_dir(a))g(this.nm(a)+da);if(b.create&&b.excl)g(this.nm(a)+b6);c=this.content[a];if(b.truncate)c.truncate()}else if(b.create){this.create_dir_if_needed(a);this.content[a]=new
H(u(0));c=this.content[a]}else
ey(this.nm(a));return new
aB(this.nm(a),c,b)};x.prototype.open=function(a,b){var
c;if(b.rdonly&&b.wronly)g(this.nm(a)+ci);if(b.text&&b.binary)g(this.nm(a)+b1);this.lookup(a);if(this.content[a]){if(this.is_dir(a))g(this.nm(a)+da);if(b.create&&b.excl)g(this.nm(a)+b6);c=this.content[a];if(b.truncate)c.truncate()}else if(b.create){this.create_dir_if_needed(a);this.content[a]=new
H(u(0));c=this.content[a]}else
ey(this.nm(a));return new
aB(this.nm(a),c,b)};x.prototype.register=function(a,b){var
c;if(this.content[a])g(this.nm(a)+b6);if(cy(b))c=new
H(b);if(cz(b))c=new
H(ag(b));else if(Array.isArray(b))c=new
H(cp(b));else if(typeof
b==="string")c=new
H(eg(b));else if(b.toString){var
d=ag(D(b.toString()));c=new
H(d)}if(c){this.create_dir_if_needed(a);this.content[a]=c}else
g(this.nm(a)+" : registering file with invalid content type")};x.prototype.constructor=x;function
l(a){return a.length}function
aE(a,b){return a.charCodeAt(b)}function
iv(a){var
d=l(a),c=new
Uint8Array(d),b=0;for(;b<d;b++)c[b]=aE(a,b);return c}function
W(a,b){this.fs=require(cm);this.fd=a;this.flags=b}W.prototype=new
d$();W.prototype.constructor=W;W.prototype.truncate=function(a){try{this.fs.ftruncateSync(this.fd,a|0)}catch(f){g(f.toString())}};W.prototype.length=function(){try{return this.fs.fstatSync(this.fd).size}catch(f){g(f.toString())}};W.prototype.write=function(a,b,c,d){try{if(this.flags.isCharacterDevice)this.fs.writeSync(this.fd,b,c,d);else
this.fs.writeSync(this.fd,b,c,d,a)}catch(f){g(f.toString())}return 0};W.prototype.read=function(a,b,c,d){try{if(this.flags.isCharacterDevice)var
e=this.fs.readSync(this.fd,b,c,d);else
var
e=this.fs.readSync(this.fd,b,c,d,a);return e}catch(f){g(f.toString())}};W.prototype.close=function(){try{this.fs.closeSync(this.fd);return 0}catch(f){g(f.toString())}};function
b(a){this.fs=require(cm);this.root=a}b.prototype.nm=function(a){return this.root+a};b.prototype.exists=function(a){try{return this.fs.existsSync(this.nm(a))?1:0}catch(f){return 0}};b.prototype.isFile=function(a){try{return this.fs.statSync(this.nm(a)).isFile()?1:0}catch(f){g(f.toString())}};b.prototype.mkdir=function(a,b,c){try{this.fs.mkdirSync(this.nm(a),{mode:b});return 0}catch(f){this.raise_nodejs_error(f,c)}};b.prototype.rmdir=function(a,b){try{this.fs.rmdirSync(this.nm(a));return 0}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.readdir=function(a,b){try{return this.fs.readdirSync(this.nm(a))}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.is_dir=function(a){try{return this.fs.statSync(this.nm(a)).isDirectory()?1:0}catch(f){g(f.toString())}};b.prototype.unlink=function(a,b){try{this.fs.unlinkSync(this.nm(a));return 0}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.open=function(a,b,c){var
d=require("node:constants"),e=0;for(var
h
in
b)switch(h){case"rdonly":e|=d.O_RDONLY;break;case"wronly":e|=d.O_WRONLY;break;case"append":e|=d.O_WRONLY|d.O_APPEND;break;case"create":e|=d.O_CREAT;break;case"truncate":e|=d.O_TRUNC;break;case"excl":e|=d.O_EXCL;break;case"binary":e|=d.O_BINARY;break;case"text":e|=d.O_TEXT;break;case"nonblock":e|=d.O_NONBLOCK;break}try{var
f=this.fs.openSync(this.nm(a),e),g=this.fs.lstatSync(this.nm(a)).isCharacterDevice();b.isCharacterDevice=g;return new
W(f,b)}catch(f){this.raise_nodejs_error(f,c)}};b.prototype.rename=function(a,b,c){try{this.fs.renameSync(this.nm(a),this.nm(b))}catch(f){this.raise_nodejs_error(f,c)}};b.prototype.stat=function(a,b){try{var
c=this.fs.statSync(this.nm(a));return this.stats_from_js(c)}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.lstat=function(a,b){try{var
c=this.fs.lstatSync(this.nm(a));return this.stats_from_js(c)}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.symlink=function(a,b,c,d){try{this.fs.symlinkSync(this.nm(b),this.nm(c),a?dn:"file");return 0}catch(f){this.raise_nodejs_error(f,d)}};b.prototype.readlink=function(a,b){try{var
c=this.fs.readlinkSync(this.nm(a),"utf8");return D(c)}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.opendir=function(a,b){try{return this.fs.opendirSync(this.nm(a))}catch(f){this.raise_nodejs_error(f,b)}};b.prototype.raise_nodejs_error=function(a,b){var
c=av(bn);if(b&&c){var
d=am(a.code,a.syscall,a.path,a.errno);al(c,d)}else
g(a.toString())};b.prototype.stats_from_js=function(a){var
b;if(a.isFile())b=0;else if(a.isDirectory())b=1;else if(a.isCharacterDevice())b=2;else if(a.isBlockDevice())b=3;else if(a.isSymbolicLink())b=4;else if(a.isFIFO())b=5;else if(a.isSocket())b=6;return[0,a.dev,a.ino,b,a.mode,a.nlink,a.uid,a.gid,a.rdev,a.size,a.atimeMs,a.mtimeMs,a.ctimeMs]};b.prototype.constructor=b;function
ep(a){var
b=cF(a);if(!b)return;return b[0]+ad}var
bx=ep(a4)||at("unable to compute caml_root"),aS=[];if(a9())aS.push({path:bx,device:new
b(bx)});else
aS.push({path:bx,device:new
x(bx)});aS.push({path:dU,device:new
x(dU)});function
eG(a){var
i=h6(a),a=i.join(ad),h=eA(a),d;for(var
f=0;f<aS.length;f++){var
c=aS[f];if(h.search(c.path)===0&&(!d||d.path.length<c.path.length))d={path:c.path,device:c.device,rest:a.slice(c.path.length,a.length)}}if(!d&&a9()){var
e=ep(a);if(e&&e.match(/^[a-zA-Z]:\/$/)){var
c={path:e,device:new
b(e)};aS.push(c);d={path:c.path,device:c.device,rest:a.slice(c.path.length,a.length)}}}if(d)return d;g("no device found for "+h)}function
hG(a,b){var
c=eG(a);if(!c.device.register)at("cannot register file");c.device.register(c.rest,b);return 0}function
eD(a,b){var
a=T(a),b=T(b);return hG(a,b)}function
hK(){var
b=c.jsoo_fs_tmp;if(b)for(var
a=0;a<b.length;a++)eD(b[a].name,b[a].content);c.jsoo_create_file=eD;c.jsoo_fs_tmp=[];return 0}function
eo(){return[0]}function
hM(a,b,c){if(!Number.isFinite(a)){if(Number.isNaN(a))return D(b4);return D(a>0?dB:"-infinity")}var
k=a===0&&1/a===Number.NEGATIVE_INFINITY?1:a>=0?0:1;if(k)a=-a;var
f=0;if(a===0);else if(a<1)while(a<1&&f>-1022){a*=2;f--}else
while(a>=2){a/=2;f++}var
l=f<0?e:aL,g=e;if(k)g=az;else
switch(c){case
43:g=aL;break;case
32:g=ac;break;default:break}if(b>=0&&b<13){var
i=Math.pow(2,b*4);a=Math.round(a*i)/i}var
d=a.toString(16);if(b>=0){var
j=d.indexOf(aA);if(j<0)d+=aA+aR(b,O);else{var
h=j+1+b;if(d.length<h)d+=aR(h-d.length,O);else
d=d.slice(0,h)}}return D(g+dl+d+"p"+l+f.toString(10))}function
hT(a){return+a.isZero()}var
er=Math.pow(2,-24);function
ic(a){throw a}function
ez(){ic(C.Division_by_zero)}function
d(a,b,c){this.lo=a&ab;this.mi=b&ab;this.hi=c&_}d.prototype.caml_custom="_j";d.prototype.copy=function(){return new
d(this.lo,this.mi,this.hi)};d.prototype.ucompare=function(a){if(this.hi>a.hi)return 1;if(this.hi<a.hi)return-1;if(this.mi>a.mi)return 1;if(this.mi<a.mi)return-1;if(this.lo>a.lo)return 1;if(this.lo<a.lo)return-1;return 0};d.prototype.compare=function(a){var
b=this.hi<<16,c=a.hi<<16;if(b>c)return 1;if(b<c)return-1;if(this.mi>a.mi)return 1;if(this.mi<a.mi)return-1;if(this.lo>a.lo)return 1;if(this.lo<a.lo)return-1;return 0};d.prototype.neg=function(){var
a=-this.lo,b=-this.mi+(a>>24),c=-this.hi+(b>>24);return new
d(a,b,c)};d.prototype.add=function(a){var
b=this.lo+a.lo,c=this.mi+a.mi+(b>>24),e=this.hi+a.hi+(c>>24);return new
d(b,c,e)};d.prototype.sub=function(a){var
b=this.lo-a.lo,c=this.mi-a.mi+(b>>24),e=this.hi-a.hi+(c>>24);return new
d(b,c,e)};d.prototype.mul=function(a){var
b=this.lo*a.lo,c=(b*er|0)+this.mi*a.lo+this.lo*a.mi,e=(c*er|0)+this.hi*a.lo+this.mi*a.mi+this.lo*a.hi;return new
d(b,c,e)};d.prototype.isZero=function(){return(this.lo|this.mi|this.hi)===0};d.prototype.isNeg=function(){return this.hi<<16<0};d.prototype.and=function(a){return new
d(this.lo&a.lo,this.mi&a.mi,this.hi&a.hi)};d.prototype.or=function(a){return new
d(this.lo|a.lo,this.mi|a.mi,this.hi|a.hi)};d.prototype.xor=function(a){return new
d(this.lo^a.lo,this.mi^a.mi,this.hi^a.hi)};d.prototype.shift_left=function(a){a=a&63;if(a===0)return this;if(a<24)return new
d(this.lo<<a,this.mi<<a|this.lo>>24-a,this.hi<<a|this.mi>>24-a);if(a<48)return new
d(0,this.lo<<a-24,this.mi<<a-24|this.lo>>48-a);return new
d(0,0,this.lo<<a-48)};d.prototype.shift_right_unsigned=function(a){a=a&63;if(a===0)return this;if(a<24)return new
d(this.lo>>a|this.mi<<24-a,this.mi>>a|this.hi<<24-a,this.hi>>a);if(a<48)return new
d(this.mi>>a-24|this.hi<<48-a,this.hi>>a-24,0);return new
d(this.hi>>a-48,0,0)};d.prototype.shift_right=function(a){a=a&63;if(a===0)return this;var
c=this.hi<<16>>16;if(a<24)return new
d(this.lo>>a|this.mi<<24-a,this.mi>>a|c<<24-a,this.hi<<16>>a>>>16);var
b=this.hi<<16>>31;if(a<48)return new
d(this.mi>>a-24|this.hi<<48-a,this.hi<<16>>a-24>>16,b&_);return new
d(this.hi<<16>>a-32,b,b)};d.prototype.lsl1=function(){this.hi=this.hi<<1|this.mi>>23;this.mi=(this.mi<<1|this.lo>>23)&ab;this.lo=this.lo<<1&ab};d.prototype.lsr1=function(){this.lo=(this.lo>>>1|this.mi<<23)&ab;this.mi=(this.mi>>>1|this.hi<<23)&ab;this.hi=this.hi>>>1};d.prototype.udivmod=function(a){var
e=0,c=this.copy(),b=a.copy(),f=new
d(0,0,0);while(c.ucompare(b)>0){e++;b.lsl1()}while(e>=0){e--;f.lsl1();if(c.ucompare(b)>=0){f.lo++;c=c.sub(b)}b.lsr1()}return{quotient:f,modulus:c}};d.prototype.div=function(a){var
b=this;if(a.isZero())ez();var
d=b.hi^a.hi;if(b.hi&Z)b=b.neg();if(a.hi&Z)a=a.neg();var
c=b.udivmod(a).quotient;if(d&Z)c=c.neg();return c};d.prototype.mod=function(a){var
b=this;if(a.isZero())ez();var
d=b.hi;if(b.hi&Z)b=b.neg();if(a.hi&Z)a=a.neg();var
c=b.udivmod(a).modulus;if(d&Z)c=c.neg();return c};d.prototype.toInt=function(){return this.lo|this.mi<<24};d.prototype.toFloat=function(){return(this.hi<<16)*Math.pow(2,32)+this.mi*Math.pow(2,24)+this.lo};d.prototype.toArray=function(){return[this.hi>>8,this.hi&ae,this.mi>>16,this.mi>>8&ae,this.mi&ae,this.lo>>16,this.lo>>8&ae,this.lo&ae]};d.prototype.lo32=function(){return this.lo|(this.mi&ae)<<24};d.prototype.hi32=function(){return this.mi>>>8&_|this.hi<<16};function
hW(a){return new
d(a&ab,a>>24&ab,a>>31&_)}function
hX(a){return a.toInt()}function
hS(a){return+a.isNeg()}function
hV(a){return a.neg()}function
hQ(a,b){var
c=cA(a);if(c.signedconv&&hS(b)){c.sign=-1;b=hV(b)}var
d=e,i=hW(c.base),h="0123456789abcdef";do{var
g=b.udivmod(i);b=g.quotient;d=h.charAt(hX(g.modulus))+d}while(!hT(b));if(c.prec>=0){c.filler=ac;var
f=c.prec-d.length;if(f>0)d=aR(f,O)+d}return cr(c,d)}function
h2(a){return a.slice(1)}function
h4(){var
b=console,c=["log","debug","info","warn",c$,"assert",dn,"dirxml","trace","group","groupCollapsed","groupEnd","time","timeEnd"];function
d(){}for(var
a=0;a<c.length;a++)if(!b[c[a]])b[c[a]]=d;return b}var
aQ=ah;function
bu(a){return function(){var
d=arguments.length;if(d>0){var
c=new
Array(d);for(var
b=0;b<d;b++)c[b]=arguments[b]}else
c=[undefined];var
e=aQ(a,c);return e
instanceof
Function?bu(e):e}}function
h3(a){return a.l>=0?a.l:a.l=a.length}function
h5(a){return function(){var
d=h3(a),c=new
Array(d);for(var
b=0;b<d;b++)c[b]=arguments[b];return aQ(a,c)}}function
a7(a){return function(){var
e=arguments.length,c=new
Array(e+1);c[0]=this;for(var
b=0;b<e;b++)c[b+1]=arguments[b];var
d=aQ(a,c);return d
instanceof
Function?bu(d):d}}function
h7(){return 0}var
au=new
Array();function
es(a){return au[a]}function
ak(a){var
b=es(a);if(!b.opened)g("Cannot flush a closed channel");if(!b.buffer||b.buffer_curr===0)return 0;if(b.output)b.output(by(b.buffer,0,b.buffer_curr));else
b.file.write(b.offset,b.buffer,0,b.buffer_curr);b.offset+=b.buffer_curr;b.buffer_curr=0;return 0}function
ij(a,b){if(b.name)try{var
d=require(cm),c=d.openSync(b.name,"rs");return new
W(c,b)}catch(f){}return new
W(a,b)}var
bz=new
Array(3);function
a2(a,b){H.call(this,u(0));this.log=function(a){return 0};if(a===1&&typeof
console.log==="function")this.log=console.log;else if(a===2&&typeof
console.error==="function")this.log=console.error;else if(typeof
console.log==="function")this.log=console.log;this.flags=b}a2.prototype.length=function(){return 0};a2.prototype.write=function(a,b,c,d){if(this.log){if(d>0&&c>=0&&c+d<=b.length&&b[c+d-1]===10)d--;var
e=u(d);as(cp(b),c,e,0,d);this.log(e.toUtf16());return 0}g(this.fd+c9)};a2.prototype.read=function(a,b,c,d){g(this.fd+": file descriptor is write only")};a2.prototype.close=function(){this.log=undefined};function
bA(a,b){if(b===undefined)b=bz.length;bz[b]=a;return b|0}function
iu(a,b,c){var
d={};while(b){switch(b[1]){case
0:d.rdonly=1;break;case
1:d.wronly=1;break;case
2:d.append=1;break;case
3:d.create=1;break;case
4:d.truncate=1;break;case
5:d.excl=1;break;case
6:d.binary=1;break;case
7:d.text=1;break;case
8:d.nonblock=1;break}b=b[2]}if(d.rdonly&&d.wronly)g(aj(a)+ci);if(d.text&&d.binary)g(aj(a)+b1);var
e=eG(a),f=e.device.open(e.rest,d);return bA(f,undefined)}(function(){function
a(a,b){return a9()?ij(a,b):new
a2(a,b)}bA(a(0,{rdonly:1,altname:"/dev/stdin",isCharacterDevice:true}),0);bA(a(1,{buffered:2,wronly:1,isCharacterDevice:true}),1);bA(a(2,{buffered:2,wronly:1,isCharacterDevice:true}),2)}());function
h8(a){var
b=bz[a];if(b.flags.wronly)g(dA+a+" is writeonly");var
d=null,c={file:b,offset:b.flags.append?b.length():0,fd:a,opened:true,out:false,buffer_curr:0,buffer_max:0,buffer:new
Uint8Array(dC),refill:d};au[c.fd]=c;return c.fd}function
et(a){var
b=bz[a];if(b.flags.rdonly)g(dA+a+" is readonly");var
d=b.flags.buffered!==undefined?b.flags.buffered:1,c={file:b,offset:b.flags.append?b.length():0,fd:a,opened:true,out:true,buffer_curr:0,buffer:new
Uint8Array(dC),buffered:d};au[c.fd]=c;return c.fd}function
h9(){var
b=0;for(var
a=0;a<au.length;a++)if(au[a]&&au[a].opened&&au[a].out)b=[0,au[a].fd,b];return b}function
h$(a,b,c,d){var
e=es(a);if(!e.opened)g("Cannot output to a closed channel");b=b.subarray(c,c+d);if(e.buffer_curr+b.length>e.buffer.length){var
h=new
Uint8Array(e.buffer_curr+b.length);h.set(e.buffer);e.buffer=h}switch(e.buffered){case
0:e.buffer.set(b,e.buffer_curr);e.buffer_curr+=b.length;ak(a);break;case
1:e.buffer.set(b,e.buffer_curr);e.buffer_curr+=b.length;if(e.buffer_curr>=e.buffer.length)ak(a);break;case
2:var
f=b.lastIndexOf(10);if(f<0){e.buffer.set(b,e.buffer_curr);e.buffer_curr+=b.length;if(e.buffer_curr>=e.buffer.length)ak(a)}else{e.buffer.set(b.subarray(0,f+1),e.buffer_curr);e.buffer_curr+=f+1;ak(a);e.buffer.set(b.subarray(f+1),e.buffer_curr);e.buffer_curr+=b.length-f-1}break}return 0}function
h_(a,b,c,d){var
b=eB(b);return h$(a,b,c,d)}function
eu(a,b,c,d){return h_(a,ag(b),c,d)}function
ev(a,b){var
c=T(String.fromCharCode(b));eu(a,c,0,1);return 0}function
h0(a){return a===245?1:0}var
ip=Math.log2&&Math.log2(1.1235582092889474e307)===1020;function
im(a){if(ip)return Math.floor(Math.log2(a));var
b=0;if(a===0)return Number.NEGATIVE_INFINITY;if(a>=1)while(a>=2){a/=2;b++}else
while(a<1){a*=2;b--}return b}function
ct(a){var
b=new
Float32Array(1);b[0]=a;var
c=new
Int32Array(b.buffer);return c[0]|0}function
bt(a,b,c){return new
d(a,b,c)}function
bs(a){if(!Number.isFinite(a)){if(Number.isNaN(a))return bt(1,0,dG);return a>0?bt(0,0,dG):bt(0,0,0xfff0)}var
f=a===0&&1/a===Number.NEGATIVE_INFINITY?Z:a>=0?0:Z;if(f)a=-a;var
b=im(a)+1023;if(b<=0){b=0;a/=Math.pow(2,-dc)}else{a/=Math.pow(2,b-dO);if(a<16){a*=2;b-=1}if(b===0)a/=2}var
d=Math.pow(2,24),c=a|0;a=(a-c)*d;var
e=a|0;a=(a-e)*d;var
g=a|0;c=c&b9|f|b<<4;return bt(g,e,c)}function
a6(a){return a.toArray()}function
ef(a,b,c){a.write(32,b.dims.length);a.write(32,b.kind|b.layout<<8);if(b.caml_custom===bk)for(var
d=0;d<b.dims.length;d++)if(b.dims[d]<_)a.write(16,b.dims[d]);else{a.write(16,_);a.write(32,0);a.write(32,b.dims[d])}else
for(var
d=0;d<b.dims.length;d++)a.write(32,b.dims[d]);switch(b.kind){case
2:case
3:case
12:for(var
d=0;d<b.data.length;d++)a.write(8,b.data[d]);break;case
4:case
5:for(var
d=0;d<b.data.length;d++)a.write(16,b.data[d]);break;case
6:for(var
d=0;d<b.data.length;d++)a.write(32,b.data[d]);break;case
8:case
9:a.write(8,0);for(var
d=0;d<b.data.length;d++)a.write(32,b.data[d]);break;case
7:for(var
d=0;d<b.data.length/2;d++){var
f=a6(b.get(d));for(var
e=0;e<8;e++)a.write(8,f[e])}break;case
1:for(var
d=0;d<b.data.length;d++){var
f=a6(bs(b.get(d)));for(var
e=0;e<8;e++)a.write(8,f[e])}break;case
0:for(var
d=0;d<b.data.length;d++){var
f=ct(b.get(d));a.write(32,f)}break;case
10:for(var
d=0;d<b.data.length/2;d++){var
e=b.get(d);a.write(32,ct(e[1]));a.write(32,ct(e[2]))}break;case
11:for(var
d=0;d<b.data.length/2;d++){var
g=b.get(d),f=a6(bs(g[1]));for(var
e=0;e<8;e++)a.write(8,f[e]);var
f=a6(bs(g[2]));for(var
e=0;e<8;e++)a.write(8,f[e])}break}c[0]=(4+b.dims.length)*4;c[1]=(4+b.dims.length)*8}function
ed(a){switch(a){case
7:case
10:case
11:return 2;default:return 1}}function
hy(a,b){var
c;switch(a){case
0:c=Float32Array;break;case
1:c=Float64Array;break;case
2:c=Int8Array;break;case
3:c=Uint8Array;break;case
4:c=Int16Array;break;case
5:c=Uint16Array;break;case
6:c=Int32Array;break;case
7:c=Int32Array;break;case
8:c=Int32Array;break;case
9:c=Int32Array;break;case
10:c=Float32Array;break;case
11:c=Float64Array;break;case
12:c=Uint8Array;break}if(!c)f("Bigarray.create: unsupported kind");var
d=new
c(b*ed(a));return d}function
cu(a){var
b=new
Int32Array(1);b[0]=a;var
c=new
Float32Array(b.buffer);return c[0]}function
a5(a){return new
d(a[7]<<0|a[6]<<8|a[5]<<16,a[4]<<0|a[3]<<8|a[2]<<16,a[1]<<0|a[0]<<8)}function
cv(a){var
f=a.lo,g=a.mi,c=a.hi,d=(c&0x7fff)>>4;if(d===2047)return(f|g|c&b9)===0?c&Z?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY:Number.NaN;var
e=Math.pow(2,-24),b=(f*e+g)*e+(c&b9);if(d>0){b+=16;b*=Math.pow(2,d-dO)}else
b*=Math.pow(2,-dc);if(c&Z)b=-b;return b}function
co(a){var
d=a.length,c=1;for(var
b=0;b<d;b++){if(a[b]<0)f("Bigarray.create: negative dimension");c=c*a[b]}return c}function
hP(a,b){return new
d(a&ab,a>>>24&ae|(b&_)<<8,b>>>16&_)}function
cw(a){return a.hi32()}function
cx(a){return a.lo32()}var
hz=bk;function
ar(a,b,c,d){this.kind=a;this.layout=b;this.dims=c;this.data=d}ar.prototype.caml_custom=hz;ar.prototype.offset=function(a){var
c=0;if(typeof
a==="number")a=[a];if(!Array.isArray(a))f("bigarray.js: invalid offset");if(this.dims.length!==a.length)f("Bigarray.get/set: bad number of dimensions");if(this.layout===0)for(var
b=0;b<this.dims.length;b++){if(a[b]<0||a[b]>=this.dims[b])bo();c=c*this.dims[b]+a[b]}else
for(var
b=this.dims.length-1;b>=0;b--){if(a[b]<1||a[b]>this.dims[b])bo();c=c*this.dims[b]+(a[b]-1)}return c};ar.prototype.get=function(a){switch(this.kind){case
7:var
d=this.data[a*2+0],b=this.data[a*2+1];return hP(d,b);case
10:case
11:var
e=this.data[a*2+0],c=this.data[a*2+1];return[bi,e,c];default:return this.data[a]}};ar.prototype.set=function(a,b){switch(this.kind){case
7:this.data[a*2+0]=cx(b);this.data[a*2+1]=cw(b);break;case
10:case
11:this.data[a*2+0]=b[1];this.data[a*2+1]=b[2];break;default:this.data[a]=b;break}return 0};ar.prototype.fill=function(a){switch(this.kind){case
7:var
c=cx(a),e=cw(a);if(c===e)this.data.fill(c);else
for(var
b=0;b<this.data.length;b++)this.data[b]=b%2===0?c:e;break;case
10:case
11:var
d=a[1],f=a[2];if(d===f)this.data.fill(d);else
for(var
b=0;b<this.data.length;b++)this.data[b]=b%2===0?d:f;break;default:this.data.fill(a);break}};ar.prototype.compare=function(a,b){if(this.layout!==a.layout||this.kind!==a.kind){var
f=this.kind|this.layout<<8,g=a.kind|a.layout<<8;return g-f}if(this.dims.length!==a.dims.length)return a.dims.length-this.dims.length;for(var
c=0;c<this.dims.length;c++)if(this.dims[c]!==a.dims[c])return this.dims[c]<a.dims[c]?-1:1;switch(this.kind){case
0:case
1:case
10:case
11:var
d,e;for(var
c=0;c<this.data.length;c++){d=this.data[c];e=a.data[c];if(d<e)return-1;if(d>e)return 1;if(d!==e){if(!b)return Number.NaN;if(!Number.isNaN(d))return 1;if(!Number.isNaN(e))return-1}}break;case
7:for(var
c=0;c<this.data.length;c+=2){if(this.data[c+1]<a.data[c+1])return-1;if(this.data[c+1]>a.data[c+1])return 1;if(this.data[c]>>>0<a.data[c]>>>0)return-1;if(this.data[c]>>>0>a.data[c]>>>0)return 1}break;case
2:case
3:case
4:case
5:case
6:case
8:case
9:case
12:for(var
c=0;c<this.data.length;c++){if(this.data[c]<a.data[c])return-1;if(this.data[c]>a.data[c])return 1}break}return 0};function
aP(a,b,c,d){this.kind=a;this.layout=b;this.dims=c;this.data=d}aP.prototype=new
ar();aP.prototype.offset=function(a){if(typeof
a!=="number")if(Array.isArray(a)&&a.length===1)a=a[0];else
f("Ml_Bigarray_c_1_1.offset");if(a<0||a>=this.dims[0])bo();return a};aP.prototype.get=function(a){return this.data[a]};aP.prototype.set=function(a,b){this.data[a]=b;return 0};aP.prototype.fill=function(a){this.data.fill(a);return 0};function
eb(a,b,c,d){var
e=ed(a);if(co(c)*e!==d.length)f("length doesn't match dims");if(b===0&&c.length===1&&e===1)return new
aP(a,b,c,d);return new
ar(a,b,c,d)}function
ec(a,b,c){var
k=a.read32s();if(k<0||k>16)at("input_value: wrong number of bigarray dimensions");var
r=a.read32s(),l=r&ae,q=r>>8&1,j=[];if(c===bk)for(var
d=0;d<k;d++){var
p=a.read16u();if(p===_){var
u=a.read32u(),v=a.read32u();if(u!==0)at("input_value: bigarray dimension overflow in 32bit");p=v}j.push(p)}else
for(var
d=0;d<k;d++)j.push(a.read32u());var
f=co(j),h=hy(l,f),i=eb(l,q,j,h);switch(l){case
2:for(var
d=0;d<f;d++)h[d]=a.read8s();break;case
3:case
12:for(var
d=0;d<f;d++)h[d]=a.read8u();break;case
4:for(var
d=0;d<f;d++)h[d]=a.read16s();break;case
5:for(var
d=0;d<f;d++)h[d]=a.read16u();break;case
6:for(var
d=0;d<f;d++)h[d]=a.read32s();break;case
8:case
9:var
t=a.read8u();if(t)at("input_value: cannot read bigarray with 64-bit OCaml ints");for(var
d=0;d<f;d++)h[d]=a.read32s();break;case
7:var
g=new
Array(8);for(var
d=0;d<f;d++){for(var
e=0;e<8;e++)g[e]=a.read8u();var
s=a5(g);i.set(d,s)}break;case
1:var
g=new
Array(8);for(var
d=0;d<f;d++){for(var
e=0;e<8;e++)g[e]=a.read8u();var
m=cv(a5(g));i.set(d,m)}break;case
0:for(var
d=0;d<f;d++){var
m=cu(a.read32s());i.set(d,m)}break;case
10:for(var
d=0;d<f;d++){var
o=cu(a.read32s()),n=cu(a.read32s());i.set(d,[bi,o,n])}break;case
11:var
g=new
Array(8);for(var
d=0;d<f;d++){for(var
e=0;e<8;e++)g[e]=a.read8u();var
o=cv(a5(g));for(var
e=0;e<8;e++)g[e]=a.read8u();var
n=cv(a5(g));i.set(d,[bi,o,n])}break}b[0]=(4+k)*4;return eb(l,q,j,h)}function
ea(a,b,c){return a.compare(b,c)}function
ew(a,b){return Math.imul(a,b)}function
ai(a,b){b=ew(b,0xcc9e2d51|0);b=b<<15|b>>>32-15;b=ew(b,0x1b873593);a^=b;a=a<<13|a>>>32-13;return(a+(a<<2)|0)+(0xe6546b64|0)|0}function
hL(a,b){a=ai(a,cx(b));a=ai(a,cw(b));return a}function
eq(a,b){return hL(a,bs(b))}function
ee(a){var
c=co(a.dims),d=0;switch(a.kind){case
2:case
3:case
12:if(c>dZ)c=dZ;var
e=0,b=0;for(b=0;b+4<=a.data.length;b+=4){e=a.data[b+0]|a.data[b+1]<<8|a.data[b+2]<<16|a.data[b+3]<<24;d=ai(d,e)}e=0;switch(c&3){case
3:e=a.data[b+2]<<16;case
2:e|=a.data[b+1]<<8;case
1:e|=a.data[b+0];d=ai(d,e)}break;case
4:case
5:if(c>aY)c=aY;var
e=0,b=0;for(b=0;b+2<=a.data.length;b+=2){e=a.data[b+0]|a.data[b+1]<<16;d=ai(d,e)}if((c&1)!==0)d=ai(d,a.data[b]);break;case
6:if(c>64)c=64;for(var
b=0;b<c;b++)d=ai(d,a.data[b]);break;case
8:case
9:if(c>64)c=64;for(var
b=0;b<c;b++)d=ai(d,a.data[b]);break;case
7:if(c>32)c=32;c*=2;for(var
b=0;b<c;b++)d=ai(d,a.data[b]);break;case
10:c*=2;case
0:if(c>64)c=64;for(var
b=0;b<c;b++)d=eq(d,a.data[b]);break;case
11:c*=2;case
1:if(c>32)c=32;for(var
b=0;b<c;b++)d=eq(d,a.data[b]);break}return d}function
hN(a,b){b[0]=4;return a.read32s()}function
ia(a,b){switch(a.read8u()){case
1:b[0]=4;return a.read32s();case
2:at("input_value: native integer value too large");break;default:at("input_value: ill-formed native integer")}}function
hY(a,b){var
d=new
Array(8);for(var
c=0;c<8;c++)d[c]=a.read8u();b[0]=8;return a5(d)}function
hU(a,b,c){var
e=a6(b);for(var
d=0;d<8;d++)a.write(8,e[d]);c[0]=8;c[1]=8}function
hO(a,b,c){return a.compare(b)}function
hR(a){return a.lo32()^a.hi32()}var
ek={_j:{deserialize:hY,serialize:hU,fixed_length:8,compare:hO,hash:hR},_i:{deserialize:hN,fixed_length:4},_n:{deserialize:ia,fixed_length:4},_bigarray:{deserialize:function(a,b){return ec(a,b,"_bigarray")},serialize:ef,compare:ea,hash:ee},_bigarr02:{deserialize:function(a,b){return ec(a,b,bk)},serialize:ef,compare:ea,hash:ee}};function
cq(a){return ek[a.caml_custom]&&ek[a.caml_custom].compare}function
ei(a,b,c,d){var
f=cq(b);if(f){var
e=c>0?f(b,a,d):f(a,b,d);if(d&&Number.isNaN(e))return c;if(Number.isNaN(+e))return+e;if((e|0)!==0)return e|0}return c}function
ej(a){if(typeof
a==="number")return a0;else if(cy(a))return bh;else if(cz(a))return 1252;else if(Array.isArray(a)&&a[0]===a[0]>>>0&&a[0]<=dt){var
b=a[0]|0;return b===bi?0:b}else if(a
instanceof
String)return c7;else if(typeof
a==="string")return c7;else if(a
instanceof
Number)return a0;else if(a&&a.caml_custom)return cb;else if(a&&a.compare)return 1256;else if(typeof
a==="function")return 1247;else if(typeof
a==="symbol")return 1251;return 1001}function
hZ(a,b){if(a<b)return-1;if(a===b)return 0;return 1}function
it(a,b){return a<b?-1:a>b?1:0}function
hC(a,b){a.t&6&&bq(a);b.t&6&&bq(b);return a.c<b.c?-1:a.c>b.c?1:0}function
hE(a,b,c){var
g=[];for(;;){if(!(c&&a===b)){var
e=ej(a);if(e===c6){a=a[1];continue}var
h=ej(b);if(h===c6){b=b[1];continue}if(e!==h){if(e===a0){if(h===cb)return ei(a,b,-1,c);return-1}if(h===a0){if(e===cb)return ei(b,a,1,c);return 1}return e<h?-1:1}switch(e){case
247:f(cf);break;case
248:var
d=hZ(a[2],b[2])|0;if(d!==0)return d;break;case
249:f(cf);break;case
250:f("equal: got Forward_tag, should not happen");break;case
251:f("equal: abstract value");break;case
252:if(a!==b){var
d=hC(a,b)|0;if(d!==0)return d}break;case
253:f("equal: got Double_tag, should not happen");break;case
254:f("equal: got Double_array_tag, should not happen");break;case
255:f("equal: got Custom_tag, should not happen");break;case
1247:f(cf);break;case
1255:var
j=cq(a);if(j!==cq(b))return a.caml_custom<b.caml_custom?-1:1;if(!j)f("compare: abstract value");var
d=j(a,b,c);if(Number.isNaN(d))return c?-1:d;if(d!==(d|0))return-1;if(d!==0)return d|0;break;case
1256:var
d=a.compare(b,c);if(Number.isNaN(d))return c?-1:d;if(d!==(d|0))return-1;if(d!==0)return d|0;break;case
1000:a=+a;b=+b;if(a<b)return-1;if(a>b)return 1;if(a!==b){if(!c)return Number.NaN;if(!Number.isNaN(a))return 1;if(!Number.isNaN(b))return-1}break;case
1001:if(a<b)return-1;if(a>b)return 1;if(a!==b)return c?1:Number.NaN;break;case
1251:if(a!==b)return c?1:Number.NaN;break;case
1252:var
a=aj(a),b=aj(b);if(a!==b){if(a<b)return-1;if(a>b)return 1}break;case
12520:var
a=a.toString(),b=b.toString();if(a!==b){if(a<b)return-1;if(a>b)return 1}break;default:if(h0(e)){f("compare: continuation value");break}if(a.length!==b.length)return a.length<b.length?-1:1;if(a.length>1)g.push(a,b,1);break}}if(g.length===0)return 0;var
i=g.pop();b=g.pop();a=g.pop();if(i+1<a.length)g.push(a,b,i+1);a=a[i];b=b[i]}}function
a8(a,b){return+(hE(a,b,false)!==0)}function
bv(a){if(Array.isArray(a)&&a[0]===a[0]>>>0)return a[0];else if(cy(a))return bh;else if(cz(a))return bh;else if(a
instanceof
Function||typeof
a==="function")return 247;else if(a&&a.caml_custom)return dt;else
return a0}var
eF=undefined;function
hA(a){var
d={},c=-1;if(a)for(var
b=1;b<a.length;b++){var
e=a[b][2];c=Math.max(c,e);d[j(a[b][1])]=e}d.next_idx=c+1;return d}function
S(a,b,c){if(c){var
d=c;if(eF)a=aQ(eF,[d]);else if(C.symbols){if(!C.symidx)C.symidx=hA(C.symbols);var
e=C.symidx[d];if(e>=0)a=e;else{var
a=C.symidx.next_idx++;C.symidx[d]=a}}}C[a+1]=b;if(c)C[c]=b}function
cC(a,b){ex[aj(a)]=b;return 0}function
ih(){f(bZ)}function
P(a,b){if(b>>>0>=l(a))ih();return aE(a,b)}function
I(a){a.t&6&&bq(a);return T(a.c)}function
ii(){return 0x7fffffff/4|0}function
cD(a){var
b=1;while(a&&a.joo_tramp){a=a.joo_tramp.apply(null,a.joo_args);b++}return a}function
y(a,b){return{joo_tramp:a,joo_args:b}}function
aF(a){{if(Array.isArray(a))return a;var
b;if(c.RangeError&&a
instanceof
c.RangeError&&a.message&&a.message.match(/maximum call stack/i))b=C.Stack_overflow;else if(c.InternalError&&a
instanceof
c.InternalError&&a.message&&a.message.match(/too much recursion/i))b=C.Stack_overflow;else if(a
instanceof
c.Error&&av(ca))b=[0,av(ca),a];else
b=[0,C.Failure,D(String(a))];if(a
instanceof
c.Error)b.js_error=a;return b}}function
h1(a){switch(a[2]){case-8:case-11:case-12:return 1;default:return 0}}function
hJ(a){var
b=e;if(a[0]===0){b+=a[1][1];if(a.length===3&&a[2][0]===0&&h1(a[1]))var
f=a[2],g=1;else
var
g=2,f=a;b+="(";for(var
d=g;d<f.length;d++){if(d>g)b+=dd;var
c=f[d];if(typeof
c==="number")b+=c.toString();else if(c
instanceof
af)b+=bg+c.toString()+bg;else if(typeof
c==="string")b+=bg+c.toString()+bg;else
b+=dW}b+=")"}else if(a[0]===w)b+=a[1];return b}function
el(a){if(Array.isArray(a)&&(a[0]===0||a[0]===w)){var
c=av(dY);if(c)aQ(c,[a,false]);else{var
d=hJ(a),b=av(dD);if(b)aQ(b,[0]);console.error(cc+d);if(a.js_error)throw a.js_error}}else
throw a}function
ig(){var
d=c.process;if(d&&d.on)d.on("uncaughtException",function(a,b){el(a);d.exit(2)});else if(c.addEventListener)c.addEventListener(c$,function(a){if(a.error)el(a.error)})}ig();function
s(a,b){return(a.l>=0?a.l:a.l=a.length)===1?a(b):ah(a,[b])}function
L(a,b,c){return(a.l>=0?a.l:a.l=a.length)===2?a(b,c):ah(a,[b,c])}function
hw(a,b,c,d){return(a.l>=0?a.l:a.l=a.length)===3?a(b,c,d):ah(a,[b,c,d])}function
hv(a,b,c,d,e){return(a.l>=0?a.l:a.l=a.length)===4?a(b,c,d,e):ah(a,[b,c,d,e])}function
bV(a,b,c,d,e,f){return(a.l>=0?a.l:a.l=a.length)===5?a(b,c,d,e,f):ah(a,[b,c,d,e,f])}function
hu(a,b,c,d,e,f,g,h){return(a.l>=0?a.l:a.l=a.length)===7?a(b,c,d,e,f,g,h):ah(a,[b,c,d,e,f,g,h])}var
is=0;hK();var
bB=[w,d9,-1],cK=[w,dy,-2],cH=[w,ck,-3],cG=[w,dg,-4],cI=[w,d4,-8],cJ=[w,dL,-9],q=[w,dS,-11],cL=[w,d0,-12],ht=[4,0,0,0,[12,45,[4,0,0,0,0]]],bK=[0,[11,'File "',[2,0,[11,'", line ',[4,0,0,0,[11,d7,[4,0,0,0,[12,45,[4,0,0,0,[11,d3,[2,0,0]]]]]]]]]],'File "%s", line %d, characters %d-%d: %s'],be=[0,[0,cl,aM,e],[0,[0,cg,bm,"x"],[0,[0,bj,bm,e],[0,[0,b2,dQ,bX],[0,[0,cj,aM,bX],[0,[0,cd,aM,bX],[0,[0,b$,c5,dT],[0,[0,du,df,dT],0]]]]]]]],c1=bl;S(11,cL,d0);S(10,q,dS);S(9,[w,dX,-10],dX);S(8,cJ,dL);S(7,cI,d4);S(6,[w,di,-7],di);S(5,[w,dw,-6],dw);S(4,[w,dF,-5],dF);S(3,cG,dg);S(2,cH,ck);S(1,cK,dy);S(0,bB,d9);function
o(a){if(typeof
a==="number")return 0;switch(a[0]){case
0:return[0,o(a[1])];case
1:return[1,o(a[1])];case
2:return[2,o(a[1])];case
3:return[3,o(a[1])];case
4:return[4,o(a[1])];case
5:return[5,o(a[1])];case
6:return[6,o(a[1])];case
7:return[7,o(a[1])];case
8:var
c=a[1];return[8,c,o(a[2])];case
9:var
b=a[1];return[9,b,b,o(a[3])];case
10:return[10,o(a[1])];case
11:return[11,o(a[1])];case
12:return[12,o(a[1])];case
13:return[13,o(a[1])];default:return[14,o(a[1])]}}function
E(a,b){if(typeof
a==="number")return b;switch(a[0]){case
0:return[0,E(a[1],b)];case
1:return[1,E(a[1],b)];case
2:return[2,E(a[1],b)];case
3:return[3,E(a[1],b)];case
4:return[4,E(a[1],b)];case
5:return[5,E(a[1],b)];case
6:return[6,E(a[1],b)];case
7:return[7,E(a[1],b)];case
8:var
c=a[1];return[8,c,E(a[2],b)];case
9:var
d=a[2],e=a[1];return[9,e,d,E(a[3],b)];case
10:return[10,E(a[1],b)];case
11:return[11,E(a[1],b)];case
12:return[12,E(a[1],b)];case
13:return[13,E(a[1],b)];default:return[14,E(a[1],b)]}}function
n(a,b){if(typeof
a==="number")return b;switch(a[0]){case
0:return[0,n(a[1],b)];case
1:return[1,n(a[1],b)];case
2:var
c=a[1];return[2,c,n(a[2],b)];case
3:var
d=a[1];return[3,d,n(a[2],b)];case
4:var
e=a[3],f=a[2],g=a[1];return[4,g,f,e,n(a[4],b)];case
5:var
h=a[3],i=a[2],j=a[1];return[5,j,i,h,n(a[4],b)];case
6:var
k=a[3],l=a[2],m=a[1];return[6,m,l,k,n(a[4],b)];case
7:var
o=a[3],p=a[2],q=a[1];return[7,q,p,o,n(a[4],b)];case
8:var
r=a[3],s=a[2],t=a[1];return[8,t,s,r,n(a[4],b)];case
9:var
u=a[1];return[9,u,n(a[2],b)];case
10:return[10,n(a[1],b)];case
11:var
v=a[1];return[11,v,n(a[2],b)];case
12:var
w=a[1];return[12,w,n(a[2],b)];case
13:var
x=a[2],y=a[1];return[13,y,x,n(a[3],b)];case
14:var
z=a[2],A=a[1];return[14,A,z,n(a[3],b)];case
15:return[15,n(a[1],b)];case
16:return[16,n(a[1],b)];case
17:var
B=a[1];return[17,B,n(a[2],b)];case
18:var
C=a[1];return[18,C,n(a[2],b)];case
19:return[19,n(a[1],b)];case
20:var
D=a[2],E=a[1];return[20,E,D,n(a[3],b)];case
21:var
F=a[1];return[21,F,n(a[2],b)];case
22:return[22,n(a[1],b)];case
23:var
G=a[1];return[23,G,n(a[2],b)];default:var
H=a[2],I=a[1];return[24,I,H,n(a[3],b)]}}var
eH=dr,eI=d8;function
aG(a){throw h([0,cG,a],1)}function
a_(a){return 0<=a?a:-a|0}function
bC(a){return a?eH:eI}function
bD(a){var
c=cs("%.12g",a),b=0,e=l(c);for(;;){if(e<=b)return c+aA;var
d=P(c,b);a:{if(48<=d){if(58>d)break a}else if(45===d)break a;return c}b=b+1|0}}h8(0);et(1);var
U=et(2);function
aT(a,b){eu(a,b,0,l(b))}function
cM(a){aT(U,a);ev(U,10);return ak(U)}var
eJ=[0,function(a){var
b=h9(0);for(;;){if(!b)return 0;var
d=b[2],e=b[1];try{ak(e)}catch(f){var
c=aF(f);if(c[1]!==cK)throw h(c,0)}b=d}}];function
bE(a){return s(cn(eJ),0)}cC(dD,bE);var
aU=(4*ii(0)|0)-1|0;function
aH(a,b){var
c=b;for(;;){if(!c)return;var
d=c[2];s(a,c[1]);c=d}}function
aw(a,b){var
c=u(a);hI(c,0,a,b);return c}var
eR="String.sub / Bytes.sub",eS="Bytes.blit",eT="String.blit / Bytes.blit_string";function
bG(a,b,c){if(0<=b&&0<=c&&(X(a)-c|0)>=b){var
d=u(c);as(a,b,d,0,c);return d}return aG(eR)}function
cN(a,b,c){return I(bG(a,b,c))}function
cO(a,b,c,d,e){if(0<=e&&0<=b&&(X(a)-e|0)>=b&&0<=d&&(X(c)-e|0)>=d){as(a,b,c,d,e);return}return aG(eS)}function
Y(a,b,c,d,e){if(0<=e&&0<=b&&(l(a)-e|0)>=b&&0<=d&&(X(c)-e|0)>=d){aC(a,b,c,d,e);return}return aG(eT)}function
cP(a){var
b=a-9|0;a:{if(4<b>>>0){if(23!==b)break a}else if(2===b)break a;return 1}return 0}function
a$(a,b,c){return I(bG(ag(a),b,c))}function
cQ(a){var
b=a-9|0;a:{if(4<b>>>0){if(23!==b)break a}else if(2===b)break a;return 1}return 0}var
eW="Buffer.add: cannot grow buffer";function
cR(a,b){var
d=a[2],c=[0,a[1][2]];for(;;){if(c[1]>=(d+b|0))break;c[1]=2*c[1]|0}if(aU<c[1]){if((d+b|0)>aU)throw h([0,cH,eW],1);c[1]=aU}var
e=u(c[1]);cO(a[1][1],0,e,0,a[2]);a[1]=[0,e,c[1]]}function
aV(a,b){var
c=l(b),d=a[2],e=a[1],f=d+c|0,g=e[1];if(e[2]<f){cR(a,c);Y(b,0,a[1][1],a[2],c)}else
aC(b,0,g,d,c);a[2]=f}var
eX="@]",eY="@}",eZ="@?",e0="@\n",e1="@.",e2="@@",e3="@%",e4="%c",e5="%s",e6=dm,e7=db,e8=d_,e9=dh,e_="%f",e$="%B",fa="%{",fb="%}",fc="%(",fd="%)",fe="%a",ff="%t",fg="%?",fh="%r",fi="%_r",fj=[0,a,850,23],fk=[0,a,837,26],fl=[0,a,847,28],fm=[0,a,815,21],fn=[0,a,819,21],fo=[0,a,823,19],fp=[0,a,827,22],fq=[0,a,832,30],fr=[0,a,851,23],fs=[0,a,836,26],ft=[0,a,846,28],fu=[0,a,814,21],fv=[0,a,818,21],fw=[0,a,822,19],fx=[0,a,826,22],fy=[0,a,831,30];function
bH(a){return 5===a[2]?12:-6}function
cS(a){return[0,0,u(a)]}function
cT(a,b){var
c=X(a[2]),d=a[1]+b|0;if(c<d){var
e=c*2|0,g=d<=e?e:d,f=u(g);cO(a[2],0,f,0,c);a[2]=f}}function
aI(a,b){cT(a,1);aD(a[2],a[1],b);a[1]=a[1]+1|0}function
z(a,b){var
c=l(b);cT(a,c);Y(b,0,a[2],a[1],c);a[1]=a[1]+c|0}function
cU(a){return cN(a[2],0,a[1])}function
cV(a){if(typeof
a==="number")switch(a){case
0:return eX;case
1:return eY;case
2:return eZ;case
3:return e0;case
4:return e1;case
5:return e2;default:return e3}switch(a[0]){case
0:return a[1];case
1:return a[1];default:return"@"+I(aw(1,a[1]))}}function
bI(a,b){var
c=b;for(;;){if(typeof
c==="number")return;switch(c[0]){case
0:var
d=c[1];z(a,e4);c=d;break;case
1:var
e=c[1];z(a,e5);c=e;break;case
2:var
f=c[1];z(a,e6);c=f;break;case
3:var
g=c[1];z(a,e7);c=g;break;case
4:var
h=c[1];z(a,e8);c=h;break;case
5:var
i=c[1];z(a,e9);c=i;break;case
6:var
j=c[1];z(a,e_);c=j;break;case
7:var
k=c[1];z(a,e$);c=k;break;case
8:var
l=c[2],m=c[1];z(a,fa);bI(a,m);z(a,fb);c=l;break;case
9:var
n=c[3],o=c[1];z(a,fc);bI(a,o);z(a,fd);c=n;break;case
10:var
p=c[1];z(a,fe);c=p;break;case
11:var
q=c[1];z(a,ff);c=q;break;case
12:var
r=c[1];z(a,fg);c=r;break;case
13:var
s=c[1];z(a,fh);c=s;break;default:var
t=c[1];z(a,fi);c=t}}}function
r(a){if(typeof
a==="number")return 0;switch(a[0]){case
0:return[0,r(a[1])];case
1:return[1,r(a[1])];case
2:return[2,r(a[1])];case
3:return[3,r(a[1])];case
4:return[4,r(a[1])];case
5:return[5,r(a[1])];case
6:return[6,r(a[1])];case
7:return[7,r(a[1])];case
8:var
b=a[1];return[8,b,r(a[2])];case
9:var
c=a[2],d=a[1];return[9,c,d,r(a[3])];case
10:return[10,r(a[1])];case
11:return[11,r(a[1])];case
12:return[12,r(a[1])];case
13:return[13,r(a[1])];default:return[14,r(a[1])]}}function
A(a){if(typeof
a==="number")return[0,,function(a){},,function(a){}];switch(a[0]){case
0:var
b=A(a[1]),s=b[2];return[0,,function(a){s(0)},,b[4]];case
1:var
c=A(a[1]),u=c[2];return[0,,function(a){u(0)},,c[4]];case
2:var
d=A(a[1]),v=d[2];return[0,,function(a){v(0)},,d[4]];case
3:var
e=A(a[1]),w=e[2];return[0,,function(a){w(0)},,e[4]];case
4:var
f=A(a[1]),x=f[2];return[0,,function(a){x(0)},,f[4]];case
5:var
g=A(a[1]),y=g[2];return[0,,function(a){y(0)},,g[4]];case
6:var
h=A(a[1]),z=h[2];return[0,,function(a){z(0)},,h[4]];case
7:var
i=A(a[1]),B=i[2];return[0,,function(a){B(0)},,i[4]];case
8:var
j=A(a[2]),C=j[2];return[0,,function(a){C(0)},,j[4]];case
9:var
D=a[2],E=a[1],k=A(a[3]),F=k[4],G=k[2],l=A(t(r(E),D)),H=l[4],I=l[2];return[0,,function(a){I(0);G(0)},,function(a){H(0);F(0)}];case
10:var
m=A(a[1]),J=m[2];return[0,,function(a){J(0)},,m[4]];case
11:var
n=A(a[1]),K=n[2];return[0,,function(a){K(0)},,n[4]];case
12:var
o=A(a[1]),L=o[2];return[0,,function(a){L(0)},,o[4]];case
13:var
p=A(a[1]),M=p[4],N=p[2];return[0,,function(a){N(0)},,function(a){M(0)}];default:var
q=A(a[1]),O=q[4],P=q[2];return[0,,function(a){P(0)},,function(a){O(0)}]}}function
t(a,b){a:{b:{c:{d:{e:{f:{g:{if(typeof
a!=="number"){switch(a[0]){case
0:var
d=a[1];if(typeof
b!=="number")switch(b[0]){case
0:return[0,t(d,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
1:var
e=a[1];if(typeof
b!=="number")switch(b[0]){case
1:return[1,t(e,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
2:var
f=a[1];if(typeof
b!=="number")switch(b[0]){case
2:return[2,t(f,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
3:var
g=a[1];if(typeof
b!=="number")switch(b[0]){case
3:return[3,t(g,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
4:var
i=a[1];if(typeof
b!=="number")switch(b[0]){case
4:return[4,t(i,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
5:var
j=a[1];if(typeof
b!=="number")switch(b[0]){case
5:return[5,t(j,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
6:var
k=a[1];if(typeof
b!=="number")switch(b[0]){case
6:return[6,t(k,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
7:var
l=a[1];if(typeof
b!=="number")switch(b[0]){case
7:return[7,t(l,b[1])];case
8:break f;case
9:break g;case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}break;case
8:var
m=a[2],n=a[1];if(typeof
b!=="number")switch(b[0]){case
8:var
o=b[1],p=t(m,b[2]);return[8,t(n,o),p];case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}throw h([0,q,fs],1);case
9:var
s=a[3],u=a[2],v=a[1];if(typeof
b!=="number")switch(b[0]){case
8:break f;case
9:var
w=b[3],x=b[2],y=b[1],c=A(t(r(u),y)),z=c[4];c[2].call(null,0);z(0);return[9,v,x,t(s,w)];case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e}throw h([0,q,ft],1);case
10:var
B=a[1];if(typeof
b!=="number"&&10===b[0])return[10,t(B,b[1])];throw h([0,q,fu],1);case
11:var
C=a[1];if(typeof
b!=="number")switch(b[0]){case
10:break a;case
11:return[11,t(C,b[1])]}throw h([0,q,fv],1);case
12:var
D=a[1];if(typeof
b!=="number")switch(b[0]){case
10:break a;case
11:break b;case
12:return[12,t(D,b[1])]}throw h([0,q,fw],1);case
13:var
E=a[1];if(typeof
b!=="number")switch(b[0]){case
10:break a;case
11:break b;case
12:break c;case
13:return[13,t(E,b[1])]}throw h([0,q,fx],1);default:var
F=a[1];if(typeof
b!=="number")switch(b[0]){case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:return[14,t(F,b[1])]}throw h([0,q,fy],1)}throw h([0,q,fr],1)}if(typeof
b==="number")return 0;switch(b[0]){case
10:break a;case
11:break b;case
12:break c;case
13:break d;case
14:break e;case
8:break f;case
9:break;default:throw h([0,q,fj],1)}}throw h([0,q,fl],1)}throw h([0,q,fk],1)}throw h([0,q,fq],1)}throw h([0,q,fp],1)}throw h([0,q,fo],1)}throw h([0,q,fn],1)}throw h([0,q,fm],1)}var
v=[w,"CamlinternalFormat.Type_mismatch",en(0)],eK="\\\\",eL="\\'",eM="\\b",eN="\\t",eO="\\n",eP="\\r",fA=bY,fB="%+d",fC="% d",fD=dm,fE="%+i",fF="% i",fG="%x",fH="%#x",fI="%X",fJ="%#X",fK="%o",fL="%#o",fM=dH,fN="%Ld",fO="%+Ld",fP="% Ld",fQ=dh,fR="%+Li",fS="% Li",fT="%Lx",fU="%#Lx",fV="%LX",fW="%#LX",fX="%Lo",fY="%#Lo",fZ="%Lu",f0="%ld",f1="%+ld",f2="% ld",f3=db,f4="%+li",f5="% li",f6="%lx",f7="%#lx",f8="%lX",f9="%#lX",f_="%lo",f$="%#lo",ga="%lu",gb="%nd",gc="%+nd",gd="% nd",ge=d_,gf="%+ni",gg="% ni",gh="%nx",gi="%#nx",gj="%nX",gk="%#nX",gl="%no",gm="%#no",gn="%nu",go=[0,c4],gt="neg_infinity",gu=dB,gv=b4,gx=[0,a,1558,4],gy="Printf: bad conversion %[",gz=[0,a,1626,39],gA=[0,a,1649,31],gB=[0,a,1650,31],gC="Printf: bad conversion %_",gD=dv,gE=dV,gF=dv,gG=dV;function
ba(a,b){if(typeof
a==="number")return[0,0,b];if(0===a[0])return[0,[0,a[1],a[2]],b];if(typeof
b!=="number"&&2===b[0])return[0,[1,a[1]],b[1]];throw h(v,1)}function
aW(a,b,c){var
d=ba(a,c);if(typeof
b!=="number")return[0,d[1],[0,b[1]],d[2]];if(!b)return[0,d[1],0,d[2]];var
e=d[2];if(typeof
e!=="number"&&2===e[0])return[0,d[1],1,e[1]];throw h(v,1)}function
m(a,b){if(typeof
a==="number")return[0,0,b];switch(a[0]){case
0:if(typeof
b!=="number"&&0===b[0]){var
w=m(a[1],b[1]);return[0,[0,w[1]],w[2]]}break;case
1:if(typeof
b!=="number"&&0===b[0]){var
x=m(a[1],b[1]);return[0,[1,x[1]],x[2]]}break;case
2:var
ag=a[2],y=ba(a[1],b),e=y[2],ah=y[1];if(typeof
e!=="number"&&1===e[0]){var
z=m(ag,e[1]);return[0,[2,ah,z[1]],z[2]]}throw h(v,1);case
3:var
ai=a[2],A=ba(a[1],b),f=A[2],aj=A[1];if(typeof
f!=="number"&&1===f[0]){var
B=m(ai,f[1]);return[0,[3,aj,B[1]],B[2]]}throw h(v,1);case
4:var
ak=a[4],al=a[1],g=aW(a[2],a[3],b),i=g[3],am=g[1];if(typeof
i!=="number"&&2===i[0]){var
an=g[2],C=m(ak,i[1]);return[0,[4,al,am,an,C[1]],C[2]]}throw h(v,1);case
5:var
ao=a[4],ap=a[1],j=aW(a[2],a[3],b),k=j[3],aq=j[1];if(typeof
k!=="number"&&3===k[0]){var
ar=j[2],D=m(ao,k[1]);return[0,[5,ap,aq,ar,D[1]],D[2]]}throw h(v,1);case
6:var
as=a[4],at=a[1],l=aW(a[2],a[3],b),n=l[3],au=l[1];if(typeof
n!=="number"&&4===n[0]){var
av=l[2],E=m(as,n[1]);return[0,[6,at,au,av,E[1]],E[2]]}throw h(v,1);case
7:var
aw=a[4],ax=a[1],p=aW(a[2],a[3],b),q=p[3],ay=p[1];if(typeof
q!=="number"&&5===q[0]){var
az=p[2],F=m(aw,q[1]);return[0,[7,ax,ay,az,F[1]],F[2]]}throw h(v,1);case
8:var
aA=a[4],aB=a[1],r=aW(a[2],a[3],b),s=r[3],aC=r[1];if(typeof
s!=="number"&&6===s[0]){var
aD=r[2],G=m(aA,s[1]);return[0,[8,aB,aC,aD,G[1]],G[2]]}throw h(v,1);case
9:var
aE=a[2],H=ba(a[1],b),t=H[2],aF=H[1];if(typeof
t!=="number"&&7===t[0]){var
I=m(aE,t[1]);return[0,[9,aF,I[1]],I[2]]}throw h(v,1);case
10:var
K=m(a[1],b);return[0,[10,K[1]],K[2]];case
11:var
aG=a[1],L=m(a[2],b);return[0,[11,aG,L[1]],L[2]];case
12:var
aH=a[1],N=m(a[2],b);return[0,[12,aH,N[1]],N[2]];case
13:if(typeof
b!=="number"&&8===b[0]){var
O=b[1],aI=b[2],aJ=a[3],aK=a[1];if(a8([0,a[2]],[0,O]))throw h(v,1);var
P=m(aJ,aI);return[0,[13,aK,O,P[1]],P[2]]}break;case
14:if(typeof
b!=="number"&&9===b[0]){var
Q=b[1],aL=b[3],aM=a[3],aN=a[2],aO=a[1],aP=[0,o(Q)];if(a8([0,o(aN)],aP))throw h(v,1);var
R=m(aM,o(aL));return[0,[14,aO,Q,R[1]],R[2]]}break;case
15:if(typeof
b!=="number"&&10===b[0]){var
S=m(a[1],b[1]);return[0,[15,S[1]],S[2]]}break;case
16:if(typeof
b!=="number"&&11===b[0]){var
T=m(a[1],b[1]);return[0,[16,T[1]],T[2]]}break;case
17:var
aQ=a[1],U=m(a[2],b);return[0,[17,aQ,U[1]],U[2]];case
18:var
V=a[2],u=a[1];if(0===u[0]){var
Z=u[1],aU=Z[2],_=m(Z[1],b),aV=_[1],$=m(V,_[2]);return[0,[18,[0,[0,aV,aU]],$[1]],$[2]]}var
aa=u[1],aX=aa[2],ab=m(aa[1],b),aY=ab[1],ac=m(V,ab[2]);return[0,[18,[1,[0,aY,aX]],ac[1]],ac[2]];case
19:if(typeof
b!=="number"&&13===b[0]){var
W=m(a[1],b[1]);return[0,[19,W[1]],W[2]]}break;case
20:if(typeof
b!=="number"&&1===b[0]){var
aR=a[2],aS=a[1],X=m(a[3],b[1]);return[0,[20,aS,aR,X[1]],X[2]]}break;case
21:if(typeof
b!=="number"&&2===b[0]){var
aT=a[1],Y=m(a[2],b[1]);return[0,[21,aT,Y[1]],Y[2]]}break;case
23:var
d=a[2],c=a[1];if(typeof
c!=="number")switch(c[0]){case
0:return M(c,d,b);case
1:return M(c,d,b);case
2:return M(c,d,b);case
3:return M(c,d,b);case
4:return M(c,d,b);case
5:return M(c,d,b);case
6:return M(c,d,b);case
7:return M(c,d,b);case
8:return M([8,c[1],c[2]],d,b);case
9:var
aZ=c[1],ae=J(c[2],d,b),af=ae[2];return[0,[23,[9,aZ,ae[1]],af[1]],af[2]];case
10:return M(c,d,b);default:return M(c,d,b)}switch(c){case
0:return M(c,d,b);case
1:return M(c,d,b);case
2:if(typeof
b!=="number"&&14===b[0]){var
ad=m(d,b[1]);return[0,[23,2,ad[1]],ad[2]]}throw h(v,1);default:return M(c,d,b)}}throw h(v,1)}function
M(a,b,c){var
d=m(b,c);return[0,[23,a,d[1]],d[2]]}function
J(a,b,c){if(typeof
a==="number")return[0,0,m(b,c)];switch(a[0]){case
0:if(typeof
c!=="number"&&0===c[0]){var
f=J(a[1],b,c[1]);return[0,[0,f[1]],f[2]]}break;case
1:if(typeof
c!=="number"&&1===c[0]){var
g=J(a[1],b,c[1]);return[0,[1,g[1]],g[2]]}break;case
2:if(typeof
c!=="number"&&2===c[0]){var
i=J(a[1],b,c[1]);return[0,[2,i[1]],i[2]]}break;case
3:if(typeof
c!=="number"&&3===c[0]){var
j=J(a[1],b,c[1]);return[0,[3,j[1]],j[2]]}break;case
4:if(typeof
c!=="number"&&4===c[0]){var
k=J(a[1],b,c[1]);return[0,[4,k[1]],k[2]]}break;case
5:if(typeof
c!=="number"&&5===c[0]){var
l=J(a[1],b,c[1]);return[0,[5,l[1]],l[2]]}break;case
6:if(typeof
c!=="number"&&6===c[0]){var
n=J(a[1],b,c[1]);return[0,[6,n[1]],n[2]]}break;case
7:if(typeof
c!=="number"&&7===c[0]){var
p=J(a[1],b,c[1]);return[0,[7,p[1]],p[2]]}break;case
8:if(typeof
c!=="number"&&8===c[0]){var
q=c[1],C=c[2],D=a[2];if(a8([0,a[1]],[0,q]))throw h(v,1);var
s=J(D,b,C);return[0,[8,q,s[1]],s[2]]}break;case
9:if(typeof
c!=="number"&&9===c[0]){var
d=c[2],e=c[1],E=c[3],F=a[3],G=a[2],H=a[1],I=[0,o(e)];if(a8([0,o(H)],I))throw h(v,1);var
K=[0,o(d)];if(a8([0,o(G)],K))throw h(v,1);var
u=A(t(r(e),d)),L=u[4];u[2].call(null,0);L(0);var
w=J(o(F),b,E),M=w[2];return[0,[9,e,d,r(w[1])],M]}break;case
10:if(typeof
c!=="number"&&10===c[0]){var
x=J(a[1],b,c[1]);return[0,[10,x[1]],x[2]]}break;case
11:if(typeof
c!=="number"&&11===c[0]){var
y=J(a[1],b,c[1]);return[0,[11,y[1]],y[2]]}break;case
13:if(typeof
c!=="number"&&13===c[0]){var
z=J(a[1],b,c[1]);return[0,[13,z[1]],z[2]]}break;case
14:if(typeof
c!=="number"&&14===c[0]){var
B=J(a[1],b,c[1]);return[0,[14,B[1]],B[2]]}break}throw h(v,1)}function
N(a,b,c){var
d=l(c),g=0<=b?a:0,f=a_(b);if(f<=d)return c;var
h=2===g?48:32,e=aw(f,h);switch(g){case
0:Y(c,0,e,0,d);break;case
1:Y(c,0,e,f-d|0,d);break;default:a:if(0<d){if(43!==P(c,0)&&45!==P(c,0)&&32!==P(c,0))break a;aD(e,0,P(c,0));Y(c,1,e,(f-d|0)+1|0,d-1|0);break}a:if(1<d&&48===P(c,0)){if(dP!==P(c,1)&&88!==P(c,1))break a;aD(e,1,P(c,1));Y(c,2,e,(f-d|0)+2|0,d-2|0);break}Y(c,0,e,f-d|0,d)}return I(e)}function
aJ(a,b){var
d=a_(a),c=l(b),e=P(b,0);a:{b:{if(58>e){if(32!==e){if(43>e)break a;switch(e-43|0){case
5:c:if(c<(d+2|0)&&1<c){if(dP!==P(b,1)&&88!==P(b,1))break c;var
g=aw(d+2|0,48);aD(g,1,P(b,1));Y(b,2,g,(d-c|0)+4|0,c-2|0);return I(g)}break b;case
0:case
2:break;case
1:case
3:case
4:break a;default:break b}}if(c>=(d+1|0))break a;var
f=aw(d+1|0,48);aD(f,0,e);Y(b,1,f,(d-c|0)+2|0,c-1|0);return I(f)}if(71<=e){if(5<e+dq>>>0)break a}else if(65>e)break a}if(c<d){var
h=aw(d,48);Y(b,0,h,d-c|0,c);return I(h)}}return b}function
fz(a){var
e=ag(a),b=[0,0],k=X(e)-1|0,s=0;if(k>=0){var
h=s;for(;;){var
f=a3(e,h);a:{b:{c:{if(32<=f){var
i=f-34|0;if(58<i>>>0){if(93<=i)break c}else if(56<i-1>>>0)break b;var
j=1;break a}if(11<=f){if(13===f)break b}else if(8<=f)break b}var
j=4;break a}var
j=2}b[1]=b[1]+j|0;var
w=h+1|0;if(k===h)break;h=w}}if(b[1]===X(e))var
n=e;else{var
c=u(b[1]);b[1]=0;var
m=X(e)-1|0,t=0;if(m>=0){var
g=t;for(;;){var
d=a3(e,g);a:{b:{c:{if(35<=d){if(92!==d){if(ch<=d)break c;break b}}else{if(32>d){if(14<=d)break c;switch(d){case
8:p(c,b[1],92);b[1]++;p(c,b[1],98);break a;case
9:p(c,b[1],92);b[1]++;p(c,b[1],116);break a;case
10:p(c,b[1],92);b[1]++;p(c,b[1],110);break a;case
13:p(c,b[1],92);b[1]++;p(c,b[1],114);break a;default:break c}}if(34>d)break b}p(c,b[1],92);b[1]++;p(c,b[1],d);break a}p(c,b[1],92);b[1]++;p(c,b[1],48+(d/ds|0)|0);b[1]++;p(c,b[1],48+((d/10|0)%10|0)|0);b[1]++;p(c,b[1],48+(d%10|0)|0);break a}p(c,b[1],d)}b[1]++;var
v=g+1|0;if(m===g)break;g=v}}var
n=c}var
r=I(n),o=l(r),q=aw(o+2|0,34);aC(r,0,q,1,o);return I(q)}function
cW(a,b){var
g=a_(b),f=go[1];switch(a[2]){case
0:var
c=102;break;case
1:var
c=101;break;case
2:var
c=69;break;case
3:var
c=c4;break;case
4:var
c=71;break;case
5:var
c=f;break;case
6:var
c=104;break;case
7:var
c=72;break;default:var
c=70}var
d=cS(16);aI(d,37);switch(a[1]){case
0:break;case
1:aI(d,43);break;default:aI(d,32)}if(8<=a[2])aI(d,35);aI(d,46);z(d,e+g);aI(d,c);return cU(d)}function
bb(a,b){if(13>a)return b;var
h=[0,0],i=l(b)-1|0,o=0;if(i>=0){var
d=o;for(;;){if(9>=aE(b,d)+dK>>>0)h[1]++;var
r=d+1|0;if(i===d)break;d=r}}var
j=h[1],k=u(l(b)+((j-1|0)/3|0)|0),m=[0,0];function
e(a){aD(k,m[1],a);m[1]++}var
f=[0,((j-1|0)%3|0)+1|0],n=l(b)-1|0,p=0;if(n>=0){var
c=p;for(;;){var
g=aE(b,c);if(9<g+dK>>>0)e(g);else{if(0===f[1]){e(95);f[1]=3}f[1]--;e(g)}var
q=c+1|0;if(n===c)break;c=q}}return I(k)}function
gp(a,b){switch(a){case
1:var
c=fB;break;case
2:var
c=fC;break;case
4:var
c=fE;break;case
5:var
c=fF;break;case
6:var
c=fG;break;case
7:var
c=fH;break;case
8:var
c=fI;break;case
9:var
c=fJ;break;case
10:var
c=fK;break;case
11:var
c=fL;break;case
0:case
13:var
c=fA;break;case
3:case
14:var
c=fD;break;default:var
c=fM}return bb(a,br(c,b))}function
gq(a,b){switch(a){case
1:var
c=f1;break;case
2:var
c=f2;break;case
4:var
c=f4;break;case
5:var
c=f5;break;case
6:var
c=f6;break;case
7:var
c=f7;break;case
8:var
c=f8;break;case
9:var
c=f9;break;case
10:var
c=f_;break;case
11:var
c=f$;break;case
0:case
13:var
c=f0;break;case
3:case
14:var
c=f3;break;default:var
c=ga}return bb(a,br(c,b))}function
gr(a,b){switch(a){case
1:var
c=gc;break;case
2:var
c=gd;break;case
4:var
c=gf;break;case
5:var
c=gg;break;case
6:var
c=gh;break;case
7:var
c=gi;break;case
8:var
c=gj;break;case
9:var
c=gk;break;case
10:var
c=gl;break;case
11:var
c=gm;break;case
0:case
13:var
c=gb;break;case
3:case
14:var
c=ge;break;default:var
c=gn}return bb(a,br(c,b))}function
gs(a,b){switch(a){case
1:var
c=fO;break;case
2:var
c=fP;break;case
4:var
c=fR;break;case
5:var
c=fS;break;case
6:var
c=fT;break;case
7:var
c=fU;break;case
8:var
c=fV;break;case
9:var
c=fW;break;case
10:var
c=fX;break;case
11:var
c=fY;break;case
0:case
13:var
c=fN;break;case
3:case
14:var
c=fQ;break;default:var
c=fZ}return bb(a,hQ(c,b))}function
$(d,b,c){function
j(a){switch(d[1]){case
0:var
e=45;break;case
1:var
e=43;break;default:var
e=32}return hM(c,b,e)}function
r(a){var
b=hD(c);return 3===b?c<0.?gt:gu:4<=b?gv:a}switch(d[2]){case
5:var
f=cs(cW(d,b),c),e=0,w=l(f);for(;;){if(e===w)var
q=0;else{var
k=P(f,e)-46|0;a:{if(23<k>>>0){if(55===k)break a}else if(21<k-1>>>0)break a;e=e+1|0;continue}var
q=1}var
x=q?f:f+aA;return r(x)}case
6:return j(0);case
7:var
i=ag(j(0)),g=X(i);if(0===g)var
o=i;else{var
m=u(g),n=g-1|0,s=0;if(n>=0){var
a=s;for(;;){var
h=a3(i,a),t=25<h+dq>>>0?h:h-32|0;p(m,a,t);var
v=a+1|0;if(n===a)break;a=v}}var
o=m}return I(o);case
8:return r(j(0));default:return cs(cW(d,b),c)}}function
aX(a,b,c,d){var
f=b,e=c,g=d;for(;;){if(typeof
g==="number")return s(f,e);switch(g[0]){case
0:var
O=g[1];return function(a){return i(f,[5,e,a],O)};case
1:var
P=g[1];return function(a){a:{b:{if(40<=a){if(92===a){var
b=eK;break a}if(ch>a)break b}else{if(32<=a){if(39>a)break b;var
b=eL;break a}if(14>a)switch(a){case
8:var
b=eM;break a;case
9:var
b=eN;break a;case
10:var
b=eO;break a;case
13:var
b=eP;break a}}var
c=u(4);p(c,0,92);p(c,1,48+(a/ds|0)|0);p(c,2,48+((a/10|0)%10|0)|0);p(c,3,48+(a%10|0)|0);var
b=I(c);break a}var
d=u(1);p(d,0,a);var
b=I(d)}var
g=l(b),h=aw(g+2|0,39);aC(b,0,h,1,g);return i(f,[4,e,I(h)],P)};case
2:return bJ(f,e,g[2],g[1],function(a){return a});case
3:return bJ(f,e,g[2],g[1],fz);case
4:return bc(f,e,g[4],g[2],g[3],gp,g[1]);case
5:return bc(f,e,g[4],g[2],g[3],gq,g[1]);case
6:return bc(f,e,g[4],g[2],g[3],gr,g[1]);case
7:return bc(f,e,g[4],g[2],g[3],gs,g[1]);case
8:var
w=g[4],x=g[3],z=g[2],t=g[1];if(typeof
z==="number"){if(typeof
x==="number")return x?function(a,b){return i(f,[4,e,$(t,a,b)],w)}:function(a){return i(f,[4,e,$(t,bH(t),a)],w)};var
_=x[1];return function(a){return i(f,[4,e,$(t,_,a)],w)}}if(0===z[0]){var
C=z[2],D=z[1];if(typeof
x==="number")return x?function(a,b){return i(f,[4,e,N(D,C,$(t,a,b))],w)}:function(a){return i(f,[4,e,N(D,C,$(t,bH(t),a))],w)};var
aa=x[1];return function(a){return i(f,[4,e,N(D,C,$(t,aa,a))],w)}}var
E=z[1];if(typeof
x==="number")return x?function(a,b,c){return i(f,[4,e,N(E,a,$(t,b,c))],w)}:function(a,b){return i(f,[4,e,N(E,a,$(t,bH(t),b))],w)};var
ab=x[1];return function(a,b){return i(f,[4,e,N(E,a,$(t,ab,b))],w)};case
9:return bJ(f,e,g[2],g[1],bC);case
10:e=[7,e];g=g[1];break;case
11:e=[2,e,g[1]];g=g[2];break;case
12:e=[3,e,g[1]];g=g[2];break;case
13:var
Q=g[3],R=g[2],F=cS(16);bI(F,R);var
M=cU(F);return function(a){return i(f,[4,e,M],Q)};case
14:var
S=g[3],T=g[2];return function(a){var
c=a[1],b=m(c,o(r(T)));if(typeof
b[2]==="number")return i(f,e,n(b[1],S));throw h(v,1)};case
15:var
U=g[1];return function(c,b){return i(f,[6,e,function(a){return L(c,a,b)}],U)};case
16:var
V=g[1];return function(a){return i(f,[6,e,a],V)};case
17:e=[0,e,g[1]];g=g[2];break;case
18:var
B=g[1];if(0===B[0]){let
b=e,c=f,d=g[2];f=function(a){return i(c,[1,b,[0,a]],d)};e=0;g=B[1][1]}else{let
b=e,c=f,d=g[2];f=function(a){return i(c,[1,b,[1,a]],d)};e=0;g=B[1][1]}break;case
19:throw h([0,q,gx],1);case
20:var
W=g[3],X=[8,e,gy];return function(a){return i(f,X,W)};case
21:var
Y=g[2];return function(a){return i(f,[4,e,br(dH,a)],Y)};case
22:var
Z=g[1];return function(a){return i(f,[5,e,a],Z)};case
23:var
j=g[2],A=g[1];if(typeof
A==="number")switch(A){case
0:return a<50?k(a+1|0,f,e,j):y(k,[0,f,e,j]);case
1:return a<50?k(a+1|0,f,e,j):y(k,[0,f,e,j]);case
2:throw h([0,q,gz],1);default:return a<50?k(a+1|0,f,e,j):y(k,[0,f,e,j])}switch(A[0]){case
0:return a<50?k(a+1|0,f,e,j):y(k,[0,f,e,j]);case
1:return a<50?k(a+1|0,f,e,j):y(k,[0,f,e,j]);case
2:return a<50?k(a+1|0,f,e,j):y(k,[0,f,e,j]);case
3:return a<50?k(a+1|0,f,e,j):y(k,[0,f,e,j]);case
4:return a<50?k(a+1|0,f,e,j):y(k,[0,f,e,j]);case
5:return a<50?k(a+1|0,f,e,j):y(k,[0,f,e,j]);case
6:return a<50?k(a+1|0,f,e,j):y(k,[0,f,e,j]);case
7:return a<50?k(a+1|0,f,e,j):y(k,[0,f,e,j]);case
8:return a<50?k(a+1|0,f,e,j):y(k,[0,f,e,j]);case
9:var
K=A[2];return a<50?bU(a+1|0,f,e,K,j):y(bU,[0,f,e,K,j]);case
10:return a<50?k(a+1|0,f,e,j):y(k,[0,f,e,j]);default:return a<50?k(a+1|0,f,e,j):y(k,[0,f,e,j])}default:var
G=g[3],H=g[1],J=s(g[2],0);return a<50?bT(a+1|0,f,e,G,H,J):y(bT,[0,f,e,G,H,J])}}}function
i(a,b,c){return cD(aX(0,a,b,c))}function
bU(a,f,c,d,e){if(typeof
d==="number")return a<50?k(a+1|0,f,c,e):y(k,[0,f,c,e]);switch(d[0]){case
0:var
b=d[1];return function(a){return Q(f,c,b,e)};case
1:var
g=d[1];return function(a){return Q(f,c,g,e)};case
2:var
i=d[1];return function(a){return Q(f,c,i,e)};case
3:var
j=d[1];return function(a){return Q(f,c,j,e)};case
4:var
l=d[1];return function(a){return Q(f,c,l,e)};case
5:var
m=d[1];return function(a){return Q(f,c,m,e)};case
6:var
n=d[1];return function(a){return Q(f,c,n,e)};case
7:var
o=d[1];return function(a){return Q(f,c,o,e)};case
8:var
p=d[2];return function(a){return Q(f,c,p,e)};case
9:var
s=d[3],u=d[2],v=t(r(d[1]),u);return function(a){return Q(f,c,E(v,s),e)};case
10:var
w=d[1];return function(a,b){return Q(f,c,w,e)};case
11:var
x=d[1];return function(a){return Q(f,c,x,e)};case
12:var
z=d[1];return function(a){return Q(f,c,z,e)};case
13:throw h([0,q,gA],1);default:throw h([0,q,gB],1)}}function
Q(a,b,c,d){return cD(bU(0,a,b,c,d))}function
k(a,b,c,d){var
e=[8,c,gC];return a<50?aX(a+1|0,b,e,d):y(aX,[0,b,e,d])}function
bJ(g,f,c,d,e){if(typeof
d==="number")return function(a){return i(g,[4,f,s(e,a)],c)};if(0===d[0]){var
b=d[2],h=d[1];return function(a){return i(g,[4,f,N(h,b,s(e,a))],c)}}var
j=d[1];return function(a,b){return i(g,[4,f,N(j,a,s(e,b))],c)}}function
bc(k,j,h,d,e,f,g){if(typeof
d==="number"){if(typeof
e==="number")return e?function(a,b){return i(k,[4,j,aJ(a,L(f,g,b))],h)}:function(a){return i(k,[4,j,L(f,g,a)],h)};var
b=e[1];return function(a){return i(k,[4,j,aJ(b,L(f,g,a))],h)}}if(0===d[0]){var
c=d[2],l=d[1];if(typeof
e==="number")return e?function(a,b){return i(k,[4,j,N(l,c,aJ(a,L(f,g,b)))],h)}:function(a){return i(k,[4,j,N(l,c,L(f,g,a))],h)};var
n=e[1];return function(a){return i(k,[4,j,N(l,c,aJ(n,L(f,g,a)))],h)}}var
m=d[1];if(typeof
e==="number")return e?function(a,b,c){return i(k,[4,j,N(m,a,aJ(b,L(f,g,c)))],h)}:function(a,b){return i(k,[4,j,N(m,a,L(f,g,b))],h)};var
o=e[1];return function(a,b){return i(k,[4,j,N(m,a,aJ(o,L(f,g,b)))],h)}}function
bT(a,b,c,d,e,f){if(e){var
h=e[1];return function(a){return gw(b,c,d,h,s(f,a))}}var
g=[4,c,f];return a<50?aX(a+1|0,b,g,d):y(aX,[0,b,g,d])}function
gw(a,b,c,d,e){return cD(bT(0,a,b,c,d,e))}function
aa(a,b){var
c=b;for(;;){if(typeof
c==="number")return;switch(c[0]){case
0:var
f=c[1],g=cV(c[2]);aa(a,f);return aT(a,g);case
1:var
d=c[2],e=c[1];if(0===d[0]){var
h=d[1];aa(a,e);aT(a,gD);c=h}else{var
i=d[1];aa(a,e);aT(a,gE);c=i}break;case
6:var
l=c[2];aa(a,c[1]);return s(l,a);case
7:aa(a,c[1]);ak(a);return;case
8:var
m=c[2];aa(a,c[1]);return aG(m);case
2:case
4:var
j=c[2];aa(a,c[1]);return aT(a,j);default:var
k=c[2];aa(a,c[1]);ev(a,k);return}}}function
an(a,b){var
c=b;for(;;){if(typeof
c==="number")return;switch(c[0]){case
0:var
j=c[1],k=cV(c[2]);an(a,j);return aV(a,k);case
1:var
e=c[2],g=c[1];if(0===e[0]){var
l=e[1];an(a,g);aV(a,gF);c=l}else{var
m=e[1];an(a,g);aV(a,gG);c=m}break;case
6:var
o=c[2];an(a,c[1]);return aV(a,s(o,0));case
7:c=c[1];break;case
8:var
q=c[2];an(a,c[1]);return aG(q);case
2:case
4:var
n=c[2];an(a,c[1]);return aV(a,n);default:var
h=c[2];an(a,c[1]);var
d=a[2],f=a[1],i=f[1];if(f[2]<=d){cR(a,1);aD(a[1][1],a[2],h)}else
p(i,d,h);a[2]=d+1|0;return}}}function
bd(c,b){return i(function(a){aa(c,a);return 0},0,b[1])}function
K(a){return i(function(a){var
e=64,c=aU<64?aU:e,d=u(c),b=[0,[0,d,c],0,d];an(b,a);return cN(b[1][1],0,b[2])},0,a[1])}var
bF=[0,0],gH=[0,[3,0,0],"%S"],gI=dW,gJ=[0,[4,0,0,0,0],bY],gK=e,gL=[0,[11,dd,[2,0,[2,0,0]]],", %s%s"],gM=[0,[12,40,[2,0,[2,0,[12,41,0]]]],"(%s%s)"],gN=e,gO=e,gP=[0,[12,40,[2,0,[12,41,0]]],"(%s)"],gQ="Out of memory",gR="Stack overflow",gS="Pattern matching failed",gT="Assertion failed",gU="Undefined recursive module",gV="Raised at",gW="Re-raised at",gX="Raised by primitive operation at",gY="Called from",gZ=" (inlined)",g0=[0,[2,0,[12,32,[2,0,[11,' in file "',[2,0,[12,34,[2,0,[11,", line ",[4,0,0,0,[11,d7,ht]]]]]]]]]],'%s %s in file "%s"%s, line %d, characters %d-%d'],g1=e,g2=[0,[2,0,[11," unknown location",0]],"%s unknown location"],g3=[0,[2,0,[12,10,0]],"%s\n"];function
bL(a,b){var
c=a[1+b];return 1-(typeof
c==="number"?1:0)?bv(c)===bh?s(K(gH),c):bv(c)===253?bD(c):gI:s(K(gJ),c)}function
cX(a,b){if(a.length-1<=b)return gK;var
c=cX(a,b+1|0),d=bL(a,b);return L(K(gL),d,c)}function
ax(a){a:{b:{var
b=cn(bF);for(;;){if(!b)break;c:{var
u=b[2],v=b[1];try{var
j=s(v,a)}catch(f){break c}if(j)break b}b=u}var
h=0;break a}var
h=[0,j[1]]}if(h)return h[1];if(a===bB)return gQ;if(a===cJ)return gR;if(a[1]===cI){var
e=a[2],m=e[3],x=e[2],y=e[1];return bV(K(bK),y,x,m,m+5|0,gS)}if(a[1]===q){var
f=a[2],n=f[3],z=f[2],A=f[1];return bV(K(bK),A,z,n,n+6|0,gT)}if(a[1]===cL){var
g=a[2],o=g[3],B=g[2],C=g[1];return bV(K(bK),C,B,o,o+6|0,gU)}if(0===bv(a)){var
i=a.length-1,w=a[1][1];if(2<i>>>0)var
p=cX(a,2),r=bL(a,1),c=L(K(gM),r,p);else
switch(i){case
0:var
c=gN;break;case
1:var
c=gO;break;default:var
t=bL(a,1),c=s(K(gP),t)}var
d=[0,w,[0,c]]}else
var
d=[0,a[1],0];var
k=d[2],l=d[1];return k?l+k[1]:l}function
bM(a,b){var
f=hF(b),h=f.length-2|0,q=0;if(h>=0){var
d=q;for(;;){var
c=eh(f,d)[1+d];let
b=d;var
g=function(a){return a?0===b?gV:gW:0===b?gX:gY};if(0===c[0])var
i=c[5],j=c[4],k=c[3],l=c[6]?gZ:g1,m=c[2],n=c[7],o=g(c[1]),e=[0,hu(K(g0),o,n,m,l,k,j,i)];else if(c[1])var
e=0;else
var
p=g(0),e=[0,s(K(g2),p)];if(e){var
r=e[1];s(bd(a,g3),r)}var
t=d+1|0;if(h===d)break;d=t}}}function
cY(a){for(;;){var
b=cn(bF),c=1-hx(bF,b,[0,a,b]);if(!c)return c}}var
g4=[0,e,"(Cannot print locations:\n bytecode executable program file not found)","(Cannot print locations:\n bytecode executable program file appears to be corrupt)","(Cannot print locations:\n bytecode executable program file has wrong magic number)","(Cannot print locations:\n bytecode executable program file cannot be opened;\n -- too many open files. Try running with OCAMLRUNPARAM=b=2)"].slice(),g5=[0,[11,cc,[2,0,[12,10,0]]],dJ],g6=[0],g7="Fatal error: out of memory in uncaught exception handler",g8=[0,[11,cc,[2,0,[12,10,0]]],dJ],g9=[0,[11,"Fatal error in uncaught exception handler: exception ",[2,0,[12,10,0]]],"Fatal error in uncaught exception handler: exception %s\n"];cC(dY,function(a,b){try{try{var
g=b?g6:eo(0);try{bE(0)}catch(f){}try{var
f=ax(a);s(bd(U,g5),f);bM(U,g);var
c=h7(0);if(c<0){var
d=a_(c);cM(eh(g4,d)[1+d])}var
n=ak(U),i=n}catch(f){var
k=aF(f),l=ax(a);s(bd(U,g8),l);bM(U,g);var
m=ax(k);s(bd(U,g9),m);bM(U,eo(0));var
i=ak(U)}var
j=i}catch(f){var
e=aF(f);if(e!==bB)throw h(e,0);var
j=cM(g7)}return j}catch(f){return 0}});var
cZ=[w,"Jsoo_runtime.Error.Exn",en(0)],bN=[0,cZ,[0]],eV=e,eU="String.concat",hs=[0,[11,dI,[2,0,[11," value: ",[8,[0,0,0],0,[0,1],[11,", resetting to ",[8,[0,0,0],0,[0,1],0]]]]]],"Invalid %s value: %.1f, resetting to %.1f"],hr=[0,[11,dI,[2,0,[11," format, resetting to ",[8,[0,0,0],0,[0,1],0]]]],"Invalid %s format, resetting to %.1f"],hq=[0,[0,cl,5.,b5,aN],[0,[0,cg,dR,3.,1.],[0,[0,bj,dR,2.,1.],[0,[0,b2,1.,aN,2.],[0,[0,cj,5.,b5,aN],[0,[0,cd,5.,b5,aN],[0,[0,b$,1.,aN,3.],0]]]]]]],hp=[0,[12,34,[2,0,[11,'":"',[2,0,[12,34,0]]]]],'"%s":"%s"'],hn=aq,ho=aq,hm=aq,hl=aq,hk=aq,hj=aq,hf="Easy",hg="Normal",hh="Hard",hi="Extreme",he=[0,[0,cl,aM],[0,[0,cg,bm],[0,[0,bj,bm],[0,[0,b2,dQ],[0,[0,cj,aM],[0,[0,cd,aM],[0,[0,b$,c5],[0,[0,aq,d8],[0,[0,du,df],0]]]]]]]]],hd=[0,[2,0,[2,0,[11,"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/",0]]],"%s%s=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/"],hc=[0,[2,0,[2,0,[12,61,[2,0,[11,";expires=",[2,0,[11,";path=/",0]]]]]]],"%s%s=%s;expires=%s;path=/"],eQ=u(0),g_=bv(bN)===w?bN:bN[1];cC(ca,g_);(function(a){throw a});var
B=c,R=null;function
bO(a){return 1-(a==R?1:0)}var
ao=false;B.String;B.RegExp;B.Object;B.Math;B.Error;B.JSON;var
ha=B.Array,hb=B.Date;cY(function(a){return a[1]===cZ?[0,D(a[2].toString())]:0});cY(function(a){return a
instanceof
ha?0:[0,D(a.toString())]});function
aK(d){return h5(function(a){if(bO(a)){var
e=s(d,a);if(1-(e|0))a.preventDefault();return e}var
c=event,b=s(d,c);if(1-(b|0))c.returnValue=b;return b})}var
F=B.document,g$=true;1-(B.HTMLElement===undefined?1:0);var
G=h4(0);function
ap(a,b){try{var
c=new
hb;c.setTime(c.getTime()+31536000000.);var
e=D(c.toUTCString());F.cookie=j(hv(K(hc),c1,a,b,e));var
f=G.log(j("Setting saved: "+bl+a+" = "+b));return f}catch(f){var
d=aF(f);return G.log(j("Error setting cookie: "+ax(d)))}}function
ay(a){try{var
i=D(F.cookie),k=bl+a+"=",n=[0,0],o=[0,l(i)],q=l(i)-1|0;if(q>=0){var
c=q;for(;;){if(aE(i,c)===59){var
v=n[1];n[1]=[0,a$(i,c+1|0,(o[1]-c|0)-1|0),v];o[1]=c}var
w=c-1|0;if(0===c)break;c=w}}var
u=n[1],f=[0,a$(i,0,o[1]),u];for(;;){if(f){var
r=f[2],d=f[1];a:if(d===e)var
g=d;else{if(!cQ(aE(d,0))&&!cQ(aE(d,l(d)-1|0))){var
g=d;break a}var
m=ag(d),p=X(m),b=[0,0];for(;;){if(b[1]>=p)break;if(!cP(a3(m,b[1])))break;b[1]++}var
h=[0,p-1|0];for(;;){if(b[1]>h[1])break;if(!cP(a3(m,h[1])))break;h[1]--}var
t=b[1]<=h[1]?bG(m,b[1],(h[1]-b[1]|0)+1|0):eQ,g=I(t)}if(l(k)>l(g)){f=r;continue}if(a$(g,0,l(k))!==k){f=r;continue}var
s=[0,a$(g,l(k),l(g)-l(k)|0)]}else
var
s=f;return s}}catch(f){var
x=aF(f);G.log(j("Error getting cookie: "+ax(x)));return 0}}function
bP(a){F.cookie=j(L(K(hd),c1,a));return G.log(j("Setting deleted: "+bl+a))}function
bQ(a){aH(function(a){var
c=a[2],b=a[1];return ay(b)?0:(ap(b,c),G.log(j("Set default for "+b+d3+c)))},he);G.log("Default settings loaded")}function
bR(a){try{G.log("Initializing settings controls...");bQ(0);aH(function(a){var
b=a[1],i=a[3],h=a[2],c=F.getElementById(j(b)),f=F.getElementById(j(b+"-value")),g=1-(c==R?1:0);if(!g)return g;var
e=ay(b),k=e?e[1]:h;c.value=j(k);function
d(a){var
e=D(c.value);ap(b,e);var
h=1-(f==R?1:0);if(!h)return h;if(b!==bj)var
g=e+i;else{try{var
l=em(e),d=l}catch(f){var
d=1.}var
k=d<=0.7?hf:d<=1.3?hg:d<=1.7?hh:hi,g=k}f.textContent=j(g)}d(0);c.oninput=aK(function(a){d(0);return ao});return c.onchange=aK(function(a){d(0);return ao})},be);var
b=F.getElementById(aq);if(1-(b==R?1:0)){var
c=ay(hj);a:{if(c&&c[1]===dr){b.checked=g$;break a}b.checked=ao}b.onchange=aK(function(a){ap(hk,bC(b.checked|0));return ao})}var
d=F.getElementById("save-settings");if(1-(d==R?1:0))d.onclick=aK(function(a){aH(function(a){var
b=a[1],c=F.getElementById(j(b)),d=1-(c==R?1:0);return d?ap(b,D(c.value)):d},be);if(1-(b==R?1:0))ap(hl,bC(b.checked|0));B.alert("Settings saved successfully!");return ao});var
e=F.getElementById("reset-settings");if(1-(e==R?1:0))e.onclick=aK(function(a){if(B.confirm("Reset all settings to defaults?")|0){aH(function(a){var
c=a[1],e=a[2];bP(c);var
b=F.getElementById(j(c)),d=1-(b==R?1:0);return d?(b.value=j(e),b.dispatchEvent(new
B.Event("input")),0):d},be);if(1-(b==R?1:0)){b.checked=ao;bP(hm)}B.alert("Settings reset to defaults!")}return ao});var
f=F.getElementById("export-settings");if(1-(f==R?1:0))f.onclick=aK(function(a){var
h=[0,0];aH(function(a){var
c=a[1],b=ay(c),d=b?(h[1]=[0,[0,c,b[1]],h[1]],0):b;return d},be);var
C=ay(hn);if(C)h[1]=[0,[0,ho,C[1]],h[1]];var
r=h[1];function
c(a){var
b=a[2],c=a[1];return L(K(hp),c,b)}if(r){var
p=r[2],t=r[1];if(p){var
G=p[2],H=p[1],J=c(t),v=[0,c(H),dN];a:{b:{var
n=v,m=1,k=G;for(;;){if(!k)break;var
q=k[2],w=k[1];if(!q)break b;var
M=q[2],N=q[1],O=c(w),x=[0,c(N),dN];n[1+m]=[0,O,x];n=x;m=1;k=M}n[1+m]=0;break a}n[1+m]=[0,c(w),0]}var
i=[0,J,v]}else
var
i=[0,c(t),0]}else
var
i=0;if(i){a:{b:{var
e=0,d=i,S=0;for(;;){if(!d)break;var
y=d[1];if(!d[2])break b;var
z=(l(y)+1|0)+e|0,P=d[2],Q=e<=z?z:aG(eU);e=Q;d=P}var
A=e;break a}var
A=l(y)+e|0}var
o=u(A),g=S,f=i;for(;;){if(f){var
b=f[1];if(f[2]){var
R=f[2];aC(b,0,o,g,l(b));aC(c_,0,o,g+l(b)|0,1);g=(g+l(b)|0)+1|0;f=R;continue}aC(b,0,o,g,l(b))}var
D=I(o);break}}else
var
D=eV;var
T=new
B.Blob(h2([0,j("{"+D+"}")]),{type:"application/json"}),E=B.URL.createObjectURL(T),s=F.createElement("a");s.href=E;s.download="h42n42_settings.json";s.click();B.URL.revokeObjectURL(E);return ao});var
h=G.log("Settings controls initialized successfully");return h}catch(f){var
g=aF(f);return G.log(j("Error initializing settings: "+ax(g)))}}function
bS(a){try{aH(function(a){var
c=a[4],b=a[1],d=ay(b);if(!d)return d;try{var
e=em(d[1]),g=e<a[2]?1:0,f=g||(a[3]<e?1:0),h=f?(G.log(j(hw(K(hs),b,e,c))),ap(b,bD(c))):f;return h}catch(f){G.log(j(L(K(hr),b,c)));return ap(b,bD(c))}},hq);var
c=G.log("Settings validation completed");return c}catch(f){var
b=aF(f);return G.log(j("Error validating settings: "+ax(b)))}}var
bf={getCookie:a7(function(a,b){var
c=ay(b);return c?j(c[1]):R}),setCookie:a7(function(a,b,c){return ap(b,c)}),deleteCookie:a7(function(a,b){return bP(b)}),validateSettings:a7(function(a){return bS}),initControls:a7(function(a){return bR})};a:{if(D(typeof
bf)==="function"&&0<bf.length){var
c0=bu(bf);break a}var
c0=bf}il.H42N42SettingsDebug=c0;G.log("H42N42 Settings System loaded");if(D(F.readyState)===c8){var
c2=function(a){if(D(F.readyState)===c8){B.setTimeout(bu(c2),aN);return 0}G.log("Settings DOM ready, initializing...");bQ(0);bS(0);var
b=bO(F.querySelector(d6));return b?bR(0):b};c2(0)}else{G.log("Settings DOM already loaded");bQ(0);bS(0);if(bO(F.querySelector(d6)))bR(0)}bE(0);return}(globalThis));
