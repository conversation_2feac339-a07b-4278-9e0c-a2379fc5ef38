<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>H42N42 - Theme</title>
    <link rel="stylesheet" type="text/css" href="css/common.css">
    <link rel="stylesheet" type="text/css" href="css/theme.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <nav class="nav-menu">
        <ul>
            <li><a href="welcome.html">🏠 Home</a></li>
            <li><a href="game.html">🎮 Play Game</a></li>
            <li><a href="instructions.html">📖 Instructions</a></li>
            <li><a href="settings.html">⚙️ Settings</a></li>
            <li><a href="theme.html">🎨 Theme</a></li>
            <li><a href="credits.html">👥 Credits</a></li>
        </ul>
    </nav>
    
    <div class="page-container">
        <h1>🎨 Theme Customization</h1>
        
        <div class="theme-content">
            <section class="theme-section">
                <h2>🌈 Color Schemes</h2>
                <div class="theme-grid">
                    <div class="theme-card" id="theme-default">
                        <div class="theme-preview default-theme">
                            <div class="preview-header">H42N42</div>
                            <div class="preview-nav">
                                <span>🏠</span><span>🎮</span><span>📖</span>
                            </div>
                            <div class="preview-content">
                                <div class="preview-toxic">☣️ TOXIC</div>
                                <div class="preview-game">
                                    <div class="preview-creet"></div>
                                    <div class="preview-creet"></div>
                                </div>
                                <div class="preview-hospital">🏥 HOSPITAL</div>
                            </div>
                        </div>
                        <h3>Default</h3>
                        <p>Classic blue-purple gradient theme</p>
                        <button class="btn btn-secondary theme-btn" id="apply-default">Apply</button>
                    </div>

                    <div class="theme-card" id="theme-dark">
                        <div class="theme-preview dark-theme">
                            <div class="preview-header">H42N42</div>
                            <div class="preview-nav">
                                <span>🏠</span><span>🎮</span><span>📖</span>
                            </div>
                            <div class="preview-content">
                                <div class="preview-toxic">☣️ TOXIC</div>
                                <div class="preview-game">
                                    <div class="preview-creet"></div>
                                    <div class="preview-creet"></div>
                                </div>
                                <div class="preview-hospital">🏥 HOSPITAL</div>
                            </div>
                        </div>
                        <h3>Dark Mode</h3>
                        <p>Easy on the eyes dark theme</p>
                        <button class="btn btn-secondary theme-btn" id="apply-dark">Apply</button>
                    </div>

                    <div class="theme-card" id="theme-nature">
                        <div class="theme-preview nature-theme">
                            <div class="preview-header">H42N42</div>
                            <div class="preview-nav">
                                <span>🏠</span><span>🎮</span><span>📖</span>
                            </div>
                            <div class="preview-content">
                                <div class="preview-toxic">☣️ TOXIC</div>
                                <div class="preview-game">
                                    <div class="preview-creet"></div>
                                    <div class="preview-creet"></div>
                                </div>
                                <div class="preview-hospital">🏥 HOSPITAL</div>
                            </div>
                        </div>
                        <h3>Nature</h3>
                        <p>Green forest-inspired theme</p>
                        <button class="btn btn-secondary theme-btn" id="apply-nature">Apply</button>
                    </div>

                    <div class="theme-card" id="theme-sunset">
                        <div class="theme-preview sunset-theme">
                            <div class="preview-header">H42N42</div>
                            <div class="preview-nav">
                                <span>🏠</span><span>🎮</span><span>📖</span>
                            </div>
                            <div class="preview-content">
                                <div class="preview-toxic">☣️ TOXIC</div>
                                <div class="preview-game">
                                    <div class="preview-creet"></div>
                                    <div class="preview-creet"></div>
                                </div>
                                <div class="preview-hospital">🏥 HOSPITAL</div>
                            </div>
                        </div>
                        <h3>Sunset</h3>
                        <p>Warm orange-pink sunset theme</p>
                        <button class="btn btn-secondary theme-btn" id="apply-sunset">Apply</button>
                    </div>

                    <div class="theme-card" id="theme-ocean">
                        <div class="theme-preview ocean-theme">
                            <div class="preview-header">H42N42</div>
                            <div class="preview-nav">
                                <span>🏠</span><span>🎮</span><span>📖</span>
                            </div>
                            <div class="preview-content">
                                <div class="preview-toxic">☣️ TOXIC</div>
                                <div class="preview-game">
                                    <div class="preview-creet"></div>
                                    <div class="preview-creet"></div>
                                </div>
                                <div class="preview-hospital">🏥 HOSPITAL</div>
                            </div>
                        </div>
                        <h3>Ocean</h3>
                        <p>Cool blue ocean depths theme</p>
                        <button class="btn btn-secondary theme-btn" id="apply-ocean">Apply</button>
                    </div>

                    <div class="theme-card" id="theme-neon">
                        <div class="theme-preview neon-theme">
                            <div class="preview-header">H42N42</div>
                            <div class="preview-nav">
                                <span>🏠</span><span>🎮</span><span>📖</span>
                            </div>
                            <div class="preview-content">
                                <div class="preview-toxic">☣️ TOXIC</div>
                                <div class="preview-game">
                                    <div class="preview-creet"></div>
                                    <div class="preview-creet"></div>
                                </div>
                                <div class="preview-hospital">🏥 HOSPITAL</div>
                            </div>
                        </div>
                        <h3>Neon</h3>
                        <p>Vibrant cyberpunk neon theme</p>
                        <button class="btn btn-secondary theme-btn" id="apply-neon">Apply</button>
                    </div>
                </div>
            </section>
            
            <!-- <section class="theme-section">
                <h2>⚙️ Customization Options</h2>
                <div class="customization-options">
                    <div class="option-group">
                        <label for="font-size">Font Size</label>
                        <input type="range" id="font-size" min="12" max="20" value="16">
                        <span class="option-value" id="font-size-value">16px</span>
                    </div>
                    <div class="option-group">
                        <label for="animation-speed">Animation Speed</label>
                        <input type="range" id="animation-speed" min="0.5" max="2.0" step="0.1" value="1.0">
                        <span class="option-value" id="animation-speed-value">1.0x</span>
                    </div>
                    <div class="option-group">
                        <label for="reduce-motion">Reduce Motion</label>
                        <input type="checkbox" id="reduce-motion">
                        <span class="option-description">Reduces animations for accessibility</span>
                    </div>
                    <div class="option-group">
                        <label for="high-contrast">High Contrast</label>
                        <input type="checkbox" id="high-contrast">
                        <span class="option-description">Increases contrast for better visibility</span>
                    </div>
                </div>
            </section> -->
            
            <div class="theme-actions">
                <button class="btn btn-secondary" id="reset-theme">🔄 Reset to Default</button>
            </div>
        </div>
    </div>
    
    <script src="js/theme_standalone.js"></script>
    <script src="js/h42n42_client.js"></script>
</body>
</html>
