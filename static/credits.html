<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>H42N42 - Credits</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" type="text/css" href="css/common.css">
    <link rel="stylesheet" type="text/css" href="css/credits.css">
</head>
<body>
    <nav class="nav-menu">
        <ul>
            <li><a href="welcome.html">🏠 Home</a></li>
            <li><a href="game.html">🎮 Play Game</a></li>
            <li><a href="instructions.html">📖 Instructions</a></li>
            <li><a href="settings.html">⚙️ Settings</a></li>
            <li><a href="theme.html">🎨 Theme</a></li>
            <li><a href="credits.html">👥 Credits</a></li>
        </ul>
    </nav>
    
    <div class="page-container">
        <h1>👥Credits</h1>
        
        <div class="credits-content">
            <section>
                <h2>H42N42 Project</h2>
                <p>A simulation game built as part of the Ocsigen/Eliom learning project.</p>
                <p>This project demonstrates the power of OCaml for web development using modern functional programming techniques.</p>
            </section>
            
            <section>
                <h2>Technologies Used</h2>
                <ul>
                    <li><strong>OCaml</strong> - The core programming language</li>
                    <li><strong>Eliom</strong> - Web framework for client-server applications</li>
                    <li><strong>Js_of_ocaml</strong> - OCaml to JavaScript compiler</li>
                    <li><strong>Lwt</strong> - Cooperative threading library</li>
                    <li><strong>Dune</strong> - Build system for OCaml projects</li>
                </ul>
            </section>
            
            <section>
                <h2>Project Specifications</h2>
                <p>Built according to the H42N42 project requirements:</p>
                <ul>
                    <li>Individual Lwt threads for each Creet</li>
                    <li>Mouse event handling with Lwt_js_events</li>
                    <li>Type-safe HTML generation</li>
                    <li>Realistic physics and collision detection</li>
                    <li>Progressive difficulty and game balance</li>
                </ul>
            </section>
            <section>
                <h2>Game Design</h2>
                <p>The H42N42 simulation is inspired by epidemiological models and real-time strategy games.</p>
                <p>Features include:</p>
                <ul>
                    <li>Realistic creature behavior and movement</li>
                    <li>Dynamic infection mechanics</li>
                    <li>Interactive drag-and-drop controls</li>
                    <li>Progressive difficulty scaling</li>
                    <li>Beautiful visual design with CSS animations</li>
                </ul>
            </section>
			<section>
                <h2>Creator</h2>
                <p>This game was created by <strong>rperez-t</strong>.</p>
				<p>Created with ❤️ using functional programming principles and modern web technologies.</p>
                <p>This project showcases the elegance of OCaml for both client and server-side development.</p>
            </section>
        </div>
    </div>

    <script src="js/theme_standalone.js"></script>
    <script src="js/h42n42_client.js"></script>
</body>
</html>
