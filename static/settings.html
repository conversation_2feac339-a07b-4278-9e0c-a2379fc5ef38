<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>H42N42 - Settings</title>
    <link rel="stylesheet" type="text/css" href="css/common.css">
    <link rel="stylesheet" type="text/css" href="css/settings.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <nav class="nav-menu">
        <ul>
            <li><a href="welcome.html">🏠 Home</a></li>
            <li><a href="game.html">🎮 Play Game</a></li>
            <li><a href="instructions.html">📖 Instructions</a></li>
            <li><a href="settings.html">⚙️ Settings</a></li>
            <li><a href="theme.html">🎨 Theme</a></li>
            <li><a href="credits.html">👥 Credits</a></li>
        </ul>
    </nav>
    
    <div class="page-container">
        <h1>⚙️ Game Settings</h1>
        
        <div class="settings-content">
            <section class="settings-section">
                <h2>🎮 Gameplay Settings</h2>
                <div class="setting-group">
                    <label for="initial-creets">Initial Creet Count</label>
                    <input type="range" id="initial-creets" min="5" max="20" value="10">
                    <span class="setting-value" id="initial-creets-value">10</span>
                </div>
                <div class="setting-group">
                    <label for="game-speed">Game Speed Multiplier</label>
                    <input type="range" id="game-speed" min="0.5" max="3.0" step="0.1" value="1.0">
                    <span class="setting-value" id="game-speed-value">1.0x</span>
                </div>
                <div class="setting-group">
                    <label for="difficulty-progression">Difficulty Progression</label>
                    <input type="range" id="difficulty-progression" min="0.5" max="2.0" step="0.1" value="1.0">
                    <span class="setting-value" id="difficulty-progression-value">Normal</span>
                </div>
                <div class="setting-group">
                    <label for="spawn-delay">Spawn Delay (seconds)</label>
                    <input type="range" id="spawn-delay" min="5" max="30" value="15">
                    <span class="setting-value" id="spawn-delay-value">15s</span>
                </div>
            </section>
            
            <section class="settings-section">
                <h2>🦠 Infection Settings</h2>
                <div class="setting-group">
                    <label for="infection-rate">Infection Rate (%)</label>
                    <input type="range" id="infection-rate" min="1" max="10" value="2">
                    <span class="setting-value" id="infection-rate-value">2%</span>
                </div>
                <div class="setting-group">
                    <label for="berserk-chance">Berserk Chance (%)</label>
                    <input type="range" id="berserk-chance" min="5" max="20" value="10">
                    <span class="setting-value" id="berserk-chance-value">10%</span>
                </div>
                <div class="setting-group">
                    <label for="mean-chance">Mean Chance (%)</label>
                    <input type="range" id="mean-chance" min="5" max="20" value="10">
                    <span class="setting-value" id="mean-chance-value">10%</span>
                </div>
            </section>
            
            <section class="settings-section">
                <h2>🏥 Hospital Settings</h2>
                <div class="setting-group">
                    <label for="auto-heal">Auto-heal in Hospital</label>
                    <input type="checkbox" id="auto-heal">
                    <span class="setting-description">Creets heal automatically when touching hospital</span>
                </div>
                <div class="setting-group">
                    <label for="heal-time">Healing Time (seconds)</label>
                    <input type="range" id="heal-time" min="1" max="10" value="3">
                    <span class="setting-value" id="heal-time-value">3s</span>
                </div>
            </section>
            
            <div class="settings-actions">
                <button class="btn btn-secondary" id="reset-settings">🔄 Reset to Defaults</button>
            </div>
        </div>
    </div>
    
    <script src="js/theme_standalone.js"></script>
    <script src="js/settings_standalone.js"></script>
    <script src="js/h42n42_client.js"></script>
</body>
</html>
