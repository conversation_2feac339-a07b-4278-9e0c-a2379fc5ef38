(* H42N42 Settings Management - Standalone OCaml implementation *)
open Js_of_ocaml

(* Cookie utilities *)
let cookie_prefix = "h42n42_"
let cookie_expiry_days = 365

let set_cookie name value =
  try
    let expires = new%js Js.date_now in
    let time = expires##getTime +. (float_of_int cookie_expiry_days *. 24. *. 60. *. 60. *. 1000.) in
    let _ = expires##setTime time in
    let cookie_string = Printf.sprintf "%s%s=%s;expires=%s;path=/" 
      cookie_prefix name value (Js.to_string expires##toUTCString) in
    Dom_html.document##.cookie := Js.string cookie_string;
    Firebug.console##log (Js.string ("Setting saved: " ^ cookie_prefix ^ name ^ " = " ^ value))
  with
  | exn -> Firebug.console##log (Js.string ("Error setting cookie: " ^ (Printexc.to_string exn)))

let get_cookie name =
  try
    let cookies = Js.to_string Dom_html.document##.cookie in
    let name_eq = cookie_prefix ^ name ^ "=" in
    let cookie_list = String.split_on_char ';' cookies in
    let rec find_cookie = function
      | [] -> None
      | cookie :: rest ->
        let trimmed = String.trim cookie in
        if String.length trimmed >= String.length name_eq then
          let prefix = String.sub trimmed 0 (String.length name_eq) in
          if prefix = name_eq then
            let value = String.sub trimmed (String.length name_eq) 
              (String.length trimmed - String.length name_eq) in
            Some value
          else
            find_cookie rest
        else
          find_cookie rest
    in
    find_cookie cookie_list
  with
  | exn -> 
    Firebug.console##log (Js.string ("Error getting cookie: " ^ (Printexc.to_string exn)));
    None

let delete_cookie name =
  let cookie_string = Printf.sprintf "%s%s=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/" 
    cookie_prefix name in
  Dom_html.document##.cookie := Js.string cookie_string;
  Firebug.console##log (Js.string ("Setting deleted: " ^ cookie_prefix ^ name))

(* Load default settings into cookies if they don't exist *)
let load_default_settings () =
  let default_settings = [
    ("initial-creets", "10");
    ("game-speed", "1.0");
    ("difficulty-progression", "1.0");
    ("infection-rate", "2");
    ("berserk-chance", "10");
    ("mean-chance", "10");
    ("heal-time", "3");
    ("auto-heal", "false");
    ("spawn-delay", "15"); (* Default spawn delay *)
  ] in

  List.iter (fun (setting, default_value) ->
    match get_cookie setting with
    | None ->
      set_cookie setting default_value;
      Firebug.console##log (Js.string ("Set default for " ^ setting ^ ": " ^ default_value))
    | Some _ -> ()
  ) default_settings;

  Firebug.console##log (Js.string "Default settings loaded")

(* Settings management *)
let init_settings_controls () =
  try
    Firebug.console##log (Js.string "Initializing settings controls...");

    (* Load defaults first *)
    load_default_settings ();

    let settings_inputs = [
      ("initial-creets", "10", "");
      ("game-speed", "1.0", "x");
      ("difficulty-progression", "1.0", "");
      ("infection-rate", "2", "%");
      ("berserk-chance", "10", "%");
      ("mean-chance", "10", "%");
      ("heal-time", "3", "s");
      ("spawn-delay", "15", "s"); (* Add spawn delay *)
    ] in
    
    List.iter (fun (setting, default_val, suffix) ->
      let input = Dom_html.document##getElementById (Js.string setting) in
      let value_display = Dom_html.document##getElementById (Js.string (setting ^ "-value")) in
      
      Js.Opt.iter input (fun inp ->
        let input_elem = Js.Unsafe.coerce inp in
        
        (* Load saved value *)
        let saved_value = match get_cookie setting with Some v -> v | None -> default_val in
        input_elem##.value := Js.string saved_value;
        
        (* Update display function *)
        let update_display () =
          let value = Js.to_string input_elem##.value in
          set_cookie setting value;
          
          Js.Opt.iter value_display (fun el ->
            let display_value = match setting with
              | "difficulty-progression" ->
                let val_float = try float_of_string value with _ -> 1.0 in
                if val_float <= 0.7 then "Easy"
                else if val_float <= 1.3 then "Normal"
                else if val_float <= 1.7 then "Hard"
                else "Extreme"
              | _ -> value ^ suffix
            in
            el##.textContent := Js.some (Js.string display_value)
          )
        in
        
        (* Initial display update *)
        update_display ();
        
        (* Add event listeners *)
        input_elem##.oninput := Dom_html.handler (fun _ -> update_display (); Js._false);
        input_elem##.onchange := Dom_html.handler (fun _ -> update_display (); Js._false)
      )
    ) settings_inputs;
    
    (* Handle auto-heal checkbox *)
    let auto_heal = Dom_html.document##getElementById (Js.string "auto-heal") in
    Js.Opt.iter auto_heal (fun inp ->
      let input_elem = Js.Unsafe.coerce inp in
      (match get_cookie "auto-heal" with
       | Some "true" -> input_elem##.checked := Js._true
       | _ -> input_elem##.checked := Js._false);
      
      input_elem##.onchange := Dom_html.handler (fun _ ->
        let value = string_of_bool (Js.to_bool input_elem##.checked) in
        set_cookie "auto-heal" value;
        Js._false
      )
    );
    
    (* Save settings button *)
    let save_btn = Dom_html.document##getElementById (Js.string "save-settings") in
    Js.Opt.iter save_btn (fun btn ->
      btn##.onclick := Dom_html.handler (fun _ ->
        List.iter (fun (setting, _, _) ->
          let input = Dom_html.document##getElementById (Js.string setting) in
          Js.Opt.iter input (fun inp ->
            let input_elem = Js.Unsafe.coerce inp in
            let value = Js.to_string input_elem##.value in
            set_cookie setting value
          )
        ) settings_inputs;
        
        Js.Opt.iter auto_heal (fun inp ->
          let input_elem = Js.Unsafe.coerce inp in
          let value = string_of_bool (Js.to_bool input_elem##.checked) in
          set_cookie "auto-heal" value
        );
        
        Dom_html.window##alert (Js.string "Settings saved successfully!");
        Js._false
      )
    );
    
    (* Reset settings button *)
    let reset_btn = Dom_html.document##getElementById (Js.string "reset-settings") in
    Js.Opt.iter reset_btn (fun btn ->
      btn##.onclick := Dom_html.handler (fun _ ->
        if Js.to_bool (Dom_html.window##confirm (Js.string "Reset all settings to defaults?")) then (
          List.iter (fun (setting, default_val, _) ->
            delete_cookie setting;
            let input = Dom_html.document##getElementById (Js.string setting) in
            Js.Opt.iter input (fun inp ->
              let input_elem = Js.Unsafe.coerce inp in
              input_elem##.value := Js.string default_val;
              let event = Js.Unsafe.new_obj (Js.Unsafe.global##._Event) [|Js.Unsafe.inject (Js.string "input")|] in
              let _ = input_elem##dispatchEvent event in ()
            )
          ) settings_inputs;
          
          Js.Opt.iter auto_heal (fun inp ->
            let input_elem = Js.Unsafe.coerce inp in
            input_elem##.checked := Js._false;
            delete_cookie "auto-heal"
          );
          
          Dom_html.window##alert (Js.string "Settings reset to defaults!")
        );
        Js._false
      )
    );
    
    (* Export settings button *)
    let export_btn = Dom_html.document##getElementById (Js.string "export-settings") in
    Js.Opt.iter export_btn (fun btn ->
      btn##.onclick := Dom_html.handler (fun _ ->
        let settings = ref [] in
        List.iter (fun (setting, _, _) ->
          match get_cookie setting with
          | Some value -> settings := (setting, value) :: !settings
          | None -> ()
        ) settings_inputs;
        
        (match get_cookie "auto-heal" with
         | Some value -> settings := ("auto-heal", value) :: !settings
         | None -> ());
        
        let settings_json = 
          "{" ^ 
          (String.concat "," 
            (List.map (fun (k, v) -> Printf.sprintf "\"%s\":\"%s\"" k v) !settings)) ^
          "}"
        in
        
        (* Create and download file *)
        let blob_data = Js.array [|Js.Unsafe.inject (Js.string settings_json)|] in
        let blob_options = object%js val type_ = Js.string "application/json" end in
        let blob = Js.Unsafe.new_obj (Js.Unsafe.global##._Blob) [|Js.Unsafe.inject blob_data; Js.Unsafe.inject blob_options|] in
        let url = Js.Unsafe.meth_call (Js.Unsafe.global##._URL) "createObjectURL" [|Js.Unsafe.inject blob|] in
        let a = Dom_html.document##createElement (Js.string "a") in
        let a_elem = Js.Unsafe.coerce a in
        a_elem##.href := url;
        a_elem##.download := Js.string "h42n42_settings.json";
        let _ = a_elem##click in
        let _ = Js.Unsafe.meth_call (Js.Unsafe.global##._URL) "revokeObjectURL" [|Js.Unsafe.inject url|] in
        Js._false
      )
    );
    
    Firebug.console##log (Js.string "Settings controls initialized successfully")
  with
  | exn -> Firebug.console##log (Js.string ("Error initializing settings: " ^ (Printexc.to_string exn)))

(* Settings validation *)
let validate_settings () =
  try
    let settings = [
      ("initial-creets", 5.0, 20.0, 10.0);
      ("game-speed", 0.5, 3.0, 1.0);
      ("difficulty-progression", 0.5, 2.0, 1.0);
      ("infection-rate", 1.0, 10.0, 2.0);
      ("berserk-chance", 5.0, 20.0, 10.0);
      ("mean-chance", 5.0, 20.0, 10.0);
      ("heal-time", 1.0, 10.0, 3.0);
    ] in
    
    List.iter (fun (setting, min_val, max_val, default_val) ->
      match get_cookie setting with
      | Some value_str ->
        (try
          let value = float_of_string value_str in
          if value < min_val || value > max_val then (
            Firebug.console##log (Js.string (Printf.sprintf "Invalid %s value: %.1f, resetting to %.1f" setting value default_val));
            set_cookie setting (string_of_float default_val)
          )
        with _ ->
          Firebug.console##log (Js.string (Printf.sprintf "Invalid %s format, resetting to %.1f" setting default_val));
          set_cookie setting (string_of_float default_val))
      | None -> ()
    ) settings;
    
    Firebug.console##log (Js.string "Settings validation completed")
  with
  | exn -> Firebug.console##log (Js.string ("Error validating settings: " ^ (Printexc.to_string exn)))

(* Export functions for debugging *)
let () =
  Js.export "H42N42SettingsDebug" 
    (object%js
       method getCookie name = 
         match get_cookie name with 
         | Some v -> Js.some (Js.string v) 
         | None -> Js.null
       method setCookie name value = set_cookie name value
       method deleteCookie name = delete_cookie name
       method validateSettings = validate_settings
       method initControls = init_settings_controls
     end)

(* Initialize when DOM is ready *)
let () =
  Firebug.console##log (Js.string "H42N42 Settings System loaded");

  let init_when_ready () =
    let ready_state = Js.to_string Dom_html.document##.readyState in
    if ready_state = "loading" then (
      let rec check_ready () =
        let current_state = Js.to_string Dom_html.document##.readyState in
        if current_state = "loading" then
          ignore (Dom_html.window##setTimeout (Js.wrap_callback check_ready) 10.0)
        else (
          Firebug.console##log (Js.string "Settings DOM ready, initializing...");
          load_default_settings ();
          validate_settings ();
          let settings_container = Dom_html.document##querySelector (Js.string ".settings-content") in
          if Js.Opt.test settings_container then
            init_settings_controls ()
        )
      in
      check_ready ()
    ) else (
      Firebug.console##log (Js.string "Settings DOM already loaded");
      load_default_settings ();
      validate_settings ();
      let settings_container = Dom_html.document##querySelector (Js.string ".settings-content") in
      if Js.Opt.test settings_container then
        init_settings_controls ()
    )
  in

  init_when_ready ()
