(* H42N42 Game Logic - Standalone OCaml implementation with Lwt *)
open Js_of_ocaml
open Js_of_ocaml_lwt

(* Game types and constants *)
type creet_state = Healthy | Infected | Berserk | Mean
type creet_status = Normal | Grabbed 

type creet = {
  id: int;
  x: float;
  y: float;
  vx: float;
  vy: float;
  state: creet_state;
  status: creet_status;
  size: float;
  speed_multiplier: float;
  infection_timer: float;
  direction_change_timer: float;
  element: Dom_html.element Js.t option;
}

type game_state = {
  creets: creet list;
  game_area_width: float;
  game_area_height: float;
  toxic_river_height: float;
  hospital_height: float;
  is_running: bool;
  game_over: bool;
  time_elapsed: float;
  difficulty_multiplier: float;
  grabbed_creet: creet option;
  last_reproduction_time: float; (* New field to track reproduction time *)
}

(* Load default settings into cookies if they don't exist *)
let load_default_game_settings () =
  let cookie_prefix = "h42n42_" in
  let default_settings = [
    ("initial-creets", "10");
    ("game-speed", "1.0");
    ("difficulty-progression", "1.0");
    ("infection-rate", "2");
    ("berserk-chance", "10");
    ("mean-chance", "10");
    ("heal-time", "3");
    ("auto-heal", "false");
  ] in

  List.iter (fun (setting, default_value) ->
    let cookies = Js.to_string Dom_html.document##.cookie in
    let name_eq = cookie_prefix ^ setting ^ "=" in
    let cookie_exists = String.contains cookies (String.get name_eq 0) &&
                       (String.sub cookies 0 (min (String.length cookies) (String.length name_eq)) = name_eq ||
                        String.contains cookies ';') in
    if not cookie_exists then (
      let expires = new%js Js.date_now in
      let time = expires##getTime +. (365.0 *. 24. *. 60. *. 60. *. 1000.) in
      let _ = expires##setTime time in
      let cookie_string = Printf.sprintf "%s%s=%s;expires=%s;path=/"
        cookie_prefix setting default_value (Js.to_string expires##toUTCString) in
      Dom_html.document##.cookie := Js.string cookie_string;
      Firebug.console##log (Js.string ("Set default game setting " ^ setting ^ ": " ^ default_value))
    )
  ) default_settings;

  Firebug.console##log (Js.string "Default game settings loaded")

(* Game settings from cookies *)
let get_setting key default_value =
  try
    let cookies = Js.to_string Dom_html.document##.cookie in
    let name_eq = "h42n42_" ^ key ^ "=" in
    let cookie_list = String.split_on_char ';' cookies in
    let rec find_cookie = function
      | [] ->
        Firebug.console##log (Js.string ("Setting " ^ key ^ " not found, using default: " ^ (string_of_float default_value)));
        default_value
      | cookie :: rest ->
        let trimmed = String.trim cookie in
        if String.length trimmed >= String.length name_eq then
          let prefix = String.sub trimmed 0 (String.length name_eq) in
          if prefix = name_eq then
            let value = String.sub trimmed (String.length name_eq)
              (String.length trimmed - String.length name_eq) in
            try
              let parsed = float_of_string value in
              Firebug.console##log (Js.string ("Loaded setting " ^ key ^ ": " ^ value));
              parsed
            with _ ->
              Firebug.console##log (Js.string ("Invalid setting " ^ key ^ ", using default: " ^ (string_of_float default_value)));
              default_value
          else
            find_cookie rest
        else
          find_cookie rest
    in
    find_cookie cookie_list
  with
  | _ ->
    Firebug.console##log (Js.string ("Error loading setting " ^ key ^ ", using default: " ^ (string_of_float default_value)));
    default_value

let get_bool_setting key default_value =
  try
    let cookies = Js.to_string Dom_html.document##.cookie in
    let name_eq = "h42n42_" ^ key ^ "=" in
    let cookie_list = String.split_on_char ';' cookies in
    let rec find_cookie = function
      | [] ->
        Firebug.console##log (Js.string ("Bool setting " ^ key ^ " not found, using default: " ^ (string_of_bool default_value)));
        default_value
      | cookie :: rest ->
        let trimmed = String.trim cookie in
        if String.length trimmed >= String.length name_eq then
          let prefix = String.sub trimmed 0 (String.length name_eq) in
          if prefix = name_eq then
            let value = String.sub trimmed (String.length name_eq)
              (String.length trimmed - String.length name_eq) in
            let result = value = "true" in
            Firebug.console##log (Js.string ("Loaded bool setting " ^ key ^ ": " ^ value));
            result
          else
            find_cookie rest
        else
          find_cookie rest
    in
    find_cookie cookie_list
  with
  | _ ->
    Firebug.console##log (Js.string ("Error loading bool setting " ^ key ^ ", using default: " ^ (string_of_bool default_value)));
    default_value

(* Game settings *)
let initial_creets_count () = int_of_float (get_setting "initial-creets" 15.0)
let game_speed () = get_setting "game-speed" 1.0
let difficulty_progression () = get_setting "difficulty-progression" 1.0
let infection_rate () = get_setting "infection-rate" 2.0
let berserk_chance () = get_setting "berserk-chance" 10.0
let mean_chance () = get_setting "mean-chance" 10.0
let heal_time () = get_setting "heal-time" 3.0
let auto_heal () = get_bool_setting "auto-heal" false
let spawn_delay () = get_setting "spawn-delay" 15.0

(* Game constants *)
let creet_base_size = 30.0
let creet_base_speed = ref 50.0 
let toxic_river_height = 0.0  (* Toxic zone is above game area (y <= 0) *)
let hospital_height = 60.0
let direction_change_interval = 2.0
let reproduction_interval = 5.0
let difficulty_increase_rate = 0.1

(* Speed increments based on difficulty level *)
let speed_increment difficulty_multiplier =
  if difficulty_multiplier < 2.0 then 10.0  (* Normal *)
  else if difficulty_multiplier < 4.0 then 50.0  (* Hard *)
  else if difficulty_multiplier < 6.0 then 100.0  (* Extreme *)
  else 200.0  (* Insane *)

(* Game state references *)
let current_game_state = ref None
let game_loop_id = ref None

(* Lwt thread management for Creets *)
let creet_threads = ref []
let creet_thread_running = ref true

(* Initialize random seed *)
let () = Random.self_init ()

(* Predefined safe spawn coordinates *)
let safe_spawn_coordinates = [|
  (* Top area - safe distance from toxic river *)
  (100.0, 120.0); (200.0, 130.0); (300.0, 125.0); (400.0, 135.0); (500.0, 120.0); (600.0, 140.0); (700.0, 125.0);
  (150.0, 160.0); (250.0, 170.0); (350.0, 165.0); (450.0, 175.0); (550.0, 160.0); (650.0, 180.0);

  (* Middle area - center of game field *)
  (80.0, 220.0); (180.0, 230.0); (280.0, 225.0); (380.0, 235.0); (480.0, 220.0); (580.0, 240.0); (680.0, 225.0);
  (120.0, 280.0); (220.0, 290.0); (320.0, 285.0); (420.0, 295.0); (520.0, 280.0); (620.0, 300.0); (720.0, 285.0);
  (90.0, 340.0); (190.0, 350.0); (290.0, 345.0); (390.0, 355.0); (490.0, 340.0); (590.0, 360.0); (690.0, 345.0);

  (* Lower middle area - safe distance from hospital *)
  (110.0, 400.0); (210.0, 410.0); (310.0, 405.0); (410.0, 415.0); (510.0, 400.0); (610.0, 420.0); (710.0, 405.0);
  (140.0, 460.0); (240.0, 470.0); (340.0, 465.0); (440.0, 475.0); (540.0, 460.0); (640.0, 480.0);

  (* Edge positions - left and right sides *)
  (50.0, 200.0); (50.0, 300.0); (50.0, 400.0); (750.0, 200.0); (750.0, 300.0); (750.0, 400.0);
|]

(* Utility functions *)
let random_float min max = min +. (Random.float (max -. min))
let random_bool probability = Random.float 100.0 < probability

let get_random_spawn_position () =
  let index = Random.int (Array.length safe_spawn_coordinates) in
  let (x, y) = safe_spawn_coordinates.(index) in
  Firebug.console##log (Js.string (Printf.sprintf "Selected spawn position %d: (%.1f, %.1f)" index x y));
  (x, y)

(* Get spawn positions using modulo for any number of Creets *)
let get_spawn_positions count =
  let total_positions = Array.length safe_spawn_coordinates in
  let positions = ref [] in

  for i = 0 to (count - 1) do
    let index = i mod total_positions in
    let (x, y) = safe_spawn_coordinates.(index) in
    positions := (x, y) :: !positions;
    Firebug.console##log (Js.string (Printf.sprintf "Creet %d assigned position %d: (%.1f, %.1f)" (i + 1) index x y))
  done;

  List.rev !positions

let distance x1 y1 x2 y2 = 
  sqrt ((x2 -. x1) *. (x2 -. x1) +. (y2 -. y1) *. (y2 -. y1))

let normalize_vector vx vy =
  let magnitude = sqrt (vx *. vx +. vy *. vy) in
  if magnitude > 0.0 then (vx /. magnitude, vy /. magnitude) else (0.0, 0.0)

(* Creet creation *)
let create_creet id x y =
  let angle = Random.float (2.0 *. Float.pi) in
  let speed = !creet_base_speed *. (game_speed ()) in
  {
    id = id;
    x = x;
    y = y;
    vx = cos angle *. speed;
    vy = sin angle *. speed;
    state = Healthy;
    status = Normal;
    size = creet_base_size;
    speed_multiplier = 1.0;
    infection_timer = 0.0;
    direction_change_timer = random_float 0.0 direction_change_interval;
    element = None;
  }

(* DOM element creation *)
let create_creet_element creet =
  try
    let element = Dom_html.document##createElement (Js.string "div") in
    element##.className := Js.string "creet";
    
    (* Set initial position and size *)
    Firebug.console##log (Js.string (Printf.sprintf "Setting Creet %d position: left=%.1fpx, top=%.1fpx" creet.id creet.x creet.y));
    element##.style##.left := Js.string (Printf.sprintf "%.1fpx" creet.x);
    element##.style##.top := Js.string (Printf.sprintf "%.1fpx" creet.y);
    element##.style##.width := Js.string (Printf.sprintf "%.1fpx" creet.size);
    element##.style##.height := Js.string (Printf.sprintf "%.1fpx" creet.size);
    element##.style##.position := Js.string "absolute";
    element##.style##.borderRadius := Js.string "50%";
    element##.style##.border := Js.string "2px solid";
    element##.style##.cursor := Js.string "pointer";
    let _ = element##.style##setProperty (Js.string "transition") (Js.string "all 0.1s ease") (Js.undefined) in
    
    (* Set color based on state *)
    let (bg_color, border_color) = match creet.state with
      | Healthy -> ("#4CAF50", "#2E7D32")
      | Infected -> ("#FF5722", "#D32F2F")
      | Berserk -> ("#9C27B0", "#6A1B9A")
      | Mean -> ("#FF9800", "#F57C00")
    in
    element##.style##.backgroundColor := Js.string bg_color;
    element##.style##.borderColor := Js.string border_color;

    (* Mouse event handlers will be added after drag functions are defined *)

    Some element
  with
  | exn -> 
    Firebug.console##log (Js.string ("Error creating creet element: " ^ (Printexc.to_string exn)));
    None

(* Game area setup *)
let setup_game_area () =
  try
    (* Use fixed dimensions for the actual game area (not container) *)
    let width = 800.0 in
    let height = 580.0 in (* Game area height without toxic river and hospital *)
    Firebug.console##log (Js.string (Printf.sprintf "Using fixed game area dimensions: %.1fx%.1f" width height));
    (width, height)
  with
  | exn ->
    Firebug.console##log (Js.string ("Error setting up game area: " ^ (Printexc.to_string exn)));
    (800.0, 580.0)

(* Initialize game state *)
let init_game_state () =
  let (width, height) = setup_game_area () in
  let initial_count = initial_creets_count () in
  let spawn_positions = get_spawn_positions initial_count in
  let creets = List.mapi (fun i (x, y) -> create_creet (i + 1) x y) spawn_positions in

  {
    creets = creets;
    game_area_width = width;
    game_area_height = height;
    toxic_river_height = toxic_river_height;
    hospital_height = hospital_height;
    is_running = false;
    game_over = false;
    time_elapsed = 0.0;
    difficulty_multiplier = 1.0;
    grabbed_creet = None;
    last_reproduction_time = 0.0; (* Initialize to 0.0 *)
  }

(* Movement logic for Healthy Creets *)
let move_healthy creet _dt =
  (creet.vx, creet.vy)

(* Movement logic for Infected Creets *)
let move_infected creet _dt =
  (creet.vx *. 0.85, creet.vy *. 0.85)  (* 15% slower *)

(* Movement logic for Berserk Creets *)
let move_berserk creet _dt =
  (creet.vx, creet.vy)

(* Movement logic for Mean Creets *)
let move_mean creet _dt game_state =
  let search_radius = creet.size *. 5.0 in
  let healthy_in_range = List.find_opt (fun target ->
    target.state = Healthy &&
    let dist = distance creet.x creet.y target.x target.y in
    dist <= search_radius
  ) game_state.creets in
  match healthy_in_range with
  | Some target ->
    let dx = target.x -. creet.x in
    let dy = target.y -. creet.y in
    let (nx, ny) = normalize_vector dx dy in
    (nx *. !creet_base_speed *. 1.2, ny *. !creet_base_speed *. 1.2)  (* Mean Creets move 20% faster *)
  | None ->
    (creet.vx, creet.vy)  (* Fallback to random movement *)

(* Higher-level function to determine movement based on Creet state *)
let move_creet creet dt game_state =
  match creet.state with
  | Healthy -> move_healthy creet dt
  | Infected -> move_infected creet dt
  | Berserk -> move_berserk creet dt
  | Mean -> move_mean creet dt game_state

(* Update creet position with collision detection *)
let update_creet_position creet dt game_state =
  if creet.status = Grabbed then creet
  else
    (* Get the effective velocity based on the Creet's state *)
    let (effective_vx, effective_vy) = move_creet creet dt game_state in

    (* Compute the new position *)
    let new_x = creet.x +. effective_vx *. dt in
    let new_y = creet.y +. effective_vy *. dt in

    (* Boundary collision detection with realistic bouncing *)
    let (final_x, final_vx) =
      if new_x <= 0.0 then
        (0.0, abs_float effective_vx)  (* Bounce right *)
      else if new_x >= game_state.game_area_width -. creet.size then
        (game_state.game_area_width -. creet.size, -.abs_float effective_vx)  (* Bounce left *)
      else (new_x, effective_vx)
    in

    let (final_y, final_vy) =
      if new_y <= 0.0 then
        (0.0, abs_float effective_vy)  (* Bounce down from top *)
      else if new_y >= game_state.game_area_height -. creet.size then
        (game_state.game_area_height -. creet.size, -.abs_float effective_vy)  (* Bounce up from bottom *)
      else (new_y, effective_vy)
    in

    (* Gradual growth for Berserk Creets *)
    let final_size =
      if creet.state = Berserk then
        min (creet.size +. (dt *. 5.0)) (creet_base_size *. 4.0)  (* Grow up to 4x size *)
      else
        creet.size
    in

    { creet with x = final_x; y = final_y; vx = final_vx; vy = final_vy; size = final_size }

(* Check toxic river collision *)
let check_toxic_collision creet game_state =
  Firebug.console##log (Js.string "Checking toxic collision...");
  Firebug.console##log (Js.string (Printf.sprintf "Game state: %b" game_state.is_running));
  if creet.y <= 0.0 && creet.state = Healthy then
    let new_state = Infected in
    let new_speed = creet.speed_multiplier *. 0.85 in (* 15% slower *)
    let new_size = creet.size in

    (* 10% chance to become berserk or mean *)
    let (final_state, final_size) =
      if random_bool (berserk_chance ()) then
        (Berserk, creet.size *. 1.5) (* Start growing *)
      else if random_bool (mean_chance ()) then
        (Mean, creet.size *. 0.85) (* 15% smaller *)
      else
        (new_state, new_size)
    in

    { creet with state = final_state; speed_multiplier = new_speed; size = final_size }
  else
    creet

(* Check hospital healing *)
let check_hospital_healing creet game_state =
  if creet.y >= game_state.game_area_height && creet.status = Grabbed && creet.state <> Healthy then
    { creet with state = Healthy; speed_multiplier = 1.0; size = creet_base_size }
  else
    creet

(* Check creet-to-creet infection *)
let check_creet_infection creet other_creets =
  if creet.state = Healthy && creet.status <> Grabbed then
    let infected_nearby = List.exists (fun other ->
      if other.state <> Healthy && other.id <> creet.id then
        let dist = distance creet.x creet.y other.x other.y in
        dist < (creet.size +. other.size) /. 2.0 && random_bool (infection_rate ())
      else false
    ) other_creets in

    if infected_nearby then
      let new_state = Infected in
      let new_speed = creet.speed_multiplier *. 0.85 in
      let (final_state, final_size) =
        if random_bool (berserk_chance ()) then
          (Berserk, creet.size)  (* Start small and grow over time *)
        else if random_bool (mean_chance ()) then
          (Mean, creet.size *. 0.85)  (* 15% smaller *)
        else
          (new_state, creet.size)
      in
      { creet with state = final_state; speed_multiplier = new_speed; size = final_size }
    else
      creet
  else
    creet

(* Update creet direction randomly *)
let update_creet_direction creet dt =
  let new_timer = creet.direction_change_timer -. dt in
  if new_timer <= 0.0 then
    let angle = Random.float (2.0 *. Float.pi) in
    let speed = !creet_base_speed *. creet.speed_multiplier in
    {
      creet with
      vx = cos angle *. speed;
      vy = sin angle *. speed;
      direction_change_timer = random_float 1.0 direction_change_interval; (* Randomize next change *)
    }
  else
    { creet with direction_change_timer = new_timer }

(* Check collision between two creets *)
let creets_collide creet1 creet2 =
  let dx = creet1.x -. creet2.x in
  let dy = creet1.y -. creet2.y in
  let distance = sqrt (dx *. dx +. dy *. dy) in
  let min_distance = (creet1.size +. creet2.size) /. 2.0 in
  distance < min_distance

(* Handle collision between two creets *)
let handle_creet_collision creet1 creet2 =
  if creets_collide creet1 creet2 then
    let dx = creet1.x -. creet2.x in
    let dy = creet1.y -. creet2.y in
    let distance = sqrt (dx *. dx +. dy *. dy) in

    if distance > 0.0 then
      (* Normalize collision vector *)
      let nx = dx /. distance in
      let ny = dy /. distance in

      (* Calculate relative velocity *)
      let dvx = creet1.vx -. creet2.vx in
      let dvy = creet1.vy -. creet2.vy in

      (* Calculate relative velocity in collision normal direction *)
      let speed = dvx *. nx +. dvy *. ny in

      (* Do not resolve if velocities are separating *)
      if speed > 0.0 then
        (* Calculate collision impulse *)
        let impulse = 2.0 *. speed /. 2.0 in (* Assuming equal mass *)

        (* Update velocities (elastic collision) *)
        let new_vx1 = creet1.vx -. impulse *. nx in
        let new_vy1 = creet1.vy -. impulse *. ny in
        let new_vx2 = creet2.vx +. impulse *. nx in
        let new_vy2 = creet2.vy +. impulse *. ny in

        (* Separate creets to prevent overlap *)
        let overlap = (creet1.size +. creet2.size) /. 2.0 -. distance in
        let separation = overlap /. 2.0 in
        let new_x1 = creet1.x +. nx *. separation in
        let new_y1 = creet1.y +. ny *. separation in
        let new_x2 = creet2.x -. nx *. separation in
        let new_y2 = creet2.y -. ny *. separation in

        ({ creet1 with x = new_x1; y = new_y1; vx = new_vx1; vy = new_vy1 },
         { creet2 with x = new_x2; y = new_y2; vx = new_vx2; vy = new_vy2 })
      else
        (creet1, creet2)
    else
      (creet1, creet2)
  else
    (creet1, creet2)

(* Update creet with collision detection against all other creets *)
let update_creet_with_collisions creet all_creets dt game_state =
  (* First update position and direction *)
  let position_updated = update_creet_position creet dt game_state in
  let direction_updated = update_creet_direction position_updated dt in

  (* Check for toxic river collision and hospital healing *)
  let toxic_checked = check_toxic_collision direction_updated game_state in
  let hospital_checked = check_hospital_healing toxic_checked game_state in
  let infection_checked = check_creet_infection hospital_checked all_creets in

  (* Then check for collisions with other creets *)
  let rec check_collisions current_creet remaining_creets =
    match remaining_creets with
    | [] -> current_creet
    | other_creet :: rest ->
      if current_creet.id <> other_creet.id && current_creet.status <> Grabbed && other_creet.status <> Grabbed then
        let (updated_current, _) = handle_creet_collision current_creet other_creet in
        check_collisions updated_current rest
      else
        check_collisions current_creet rest
  in

  check_collisions infection_checked all_creets

(* Update DOM element position and appearance *)
let update_creet_element creet =
  match creet.element with
  | Some element ->
    (* Update position *)
    element##.style##.left := Js.string (Printf.sprintf "%.1fpx" creet.x);
    element##.style##.top := Js.string (Printf.sprintf "%.1fpx" creet.y);

    (* Update size *)
    element##.style##.width := Js.string (Printf.sprintf "%.1fpx" creet.size);
    element##.style##.height := Js.string (Printf.sprintf "%.1fpx" creet.size);

    (* Update color based on state *)
    let (bg_color, border_color) = match creet.state with
      | Healthy -> ("#4CAF50", "#2E7D32")  (* Green *)
      | Infected -> ("#FF9800", "#F57C00")  (* Orange *)
      | Berserk -> ("#D32F2F", "#B71C1C")  (* Red *)
      | Mean -> ("#9C27B0", "#6A1B9A")      (* Purple *)
    in
    element##.style##.backgroundColor := Js.string bg_color;
    element##.style##.borderColor := Js.string border_color
  | None -> ()

(* Drag and drop state *)
let dragging_creet = ref None
let drag_offset_x = ref 0.0
let drag_offset_y = ref 0.0

(* Start dragging a creet *)
let start_drag creet_id event =
  try
    Firebug.console##log (Js.string (Printf.sprintf "Starting drag for Creet %d" creet_id));
    dragging_creet := Some creet_id;

    (* Note: Dom_html.mouseEvent doesn't have preventDefault method *)

    (* Get mouse coordinates relative to the page *)
    let mouse_x = float_of_int event##.clientX in
    let mouse_y = float_of_int event##.clientY in

    (* Get game area to calculate proper offset *)
    let game_area = Dom_html.document##getElementById (Js.string "game-area") in
    Js.Opt.case game_area
      (fun () ->
        Firebug.console##log (Js.string "Game area not found for drag start"))
      (fun area ->
        let rect = area##getBoundingClientRect in
        let area_left = rect##.left in
        let area_top = rect##.top in

        (* Calculate mouse position relative to game area *)
        let relative_mouse_x = mouse_x -. area_left in
        let relative_mouse_y = mouse_y -. area_top in

        Firebug.console##log (Js.string (Printf.sprintf "Start drag - Mouse: (%.1f, %.1f), Game area offset: (%.1f, %.1f), Relative: (%.1f, %.1f)"
          mouse_x mouse_y area_left area_top relative_mouse_x relative_mouse_y));

        (* Find the creet in current game state and calculate offset *)
        match !current_game_state with
        | Some game_state ->
          (match List.find_opt (fun c -> c.id = creet_id) game_state.creets with
          | Some creet ->
            drag_offset_x := relative_mouse_x -. creet.x;
            drag_offset_y := relative_mouse_y -. creet.y;
            Firebug.console##log (Js.string (Printf.sprintf "Creet %d at (%.1f, %.1f), Drag offset: (%.1f, %.1f)"
              creet.id creet.x creet.y !drag_offset_x !drag_offset_y))
          | None ->
            Firebug.console##log (Js.string "Creet not found for drag start"))
        | None ->
          Firebug.console##log (Js.string "No game state for drag start"))
  with
  | exn ->
    Firebug.console##log (Js.string ("Error starting drag: " ^ (Printexc.to_string exn)))

(* Stop dragging *)
let stop_drag () =
  match !dragging_creet with
  | Some creet_id ->
    Firebug.console##log (Js.string (Printf.sprintf "Stopping drag for Creet %d" creet_id));

    (* Reset creet status to Normal *)
    (match !current_game_state with
    | Some game_state ->
      let updated_creets = List.map (fun creet ->
        if creet.id = creet_id then
          { creet with status = Normal }
        else
          creet
      ) game_state.creets in

      let updated_game_state = { game_state with creets = updated_creets; grabbed_creet = None } in
      current_game_state := Some updated_game_state
    | None -> ());

    dragging_creet := None
  | None -> ()

(* Update creet position during drag *)
let update_drag mouse_x mouse_y =
  match !dragging_creet with
  | Some creet_id ->
    (match !current_game_state with
    | Some game_state ->
      let game_area = Dom_html.document##getElementById (Js.string "game-area") in
      Js.Opt.case game_area
        (fun () ->
          Firebug.console##log (Js.string "Game area not found for drag update"))
        (fun area ->
          let rect = area##getBoundingClientRect in
          let area_left = rect##.left in
          let area_top = rect##.top in

          let relative_mouse_x = mouse_x -. area_left in
          let relative_mouse_y = mouse_y -. area_top in

          (* Allow creets to move into the hospital zone *)
          let new_x = relative_mouse_x -. !drag_offset_x in
          let new_y = relative_mouse_y -. !drag_offset_y in

          (* Update creet position and status *)
          let updated_creets = List.map (fun creet ->
            if creet.id = creet_id then
              { creet with x = new_x; y = new_y; status = Grabbed }
            else
              creet
          ) game_state.creets in

          let grabbed_creet_obj = List.find_opt (fun c -> c.id = creet_id) updated_creets in
          let updated_game_state = { game_state with creets = updated_creets; grabbed_creet = grabbed_creet_obj } in
          current_game_state := Some updated_game_state;

          (* Update DOM element position *)
          (match List.find_opt (fun c -> c.id = creet_id) updated_creets with
          | Some creet -> update_creet_element creet
          | None -> ()))
    | None -> ())
  | None -> ()

(* Add Lwt mouse event handlers to a creet element *)
let add_creet_mouse_events creet element =
  Lwt.async (fun () ->
    let%lwt () =
      Lwt_js_events.mousedowns element (fun event _ ->
        Firebug.console##log (Js.string (Printf.sprintf "Lwt Mouse down on Creet %d" creet.id));
        start_drag creet.id event;
        Lwt.return ()
      )
    in
    Lwt.return ()
  )

(* Add creet to DOM *)
let add_creet_to_dom creet =
  try
    Firebug.console##log (Js.string (Printf.sprintf "Adding Creet %d to DOM at (%.1f, %.1f)" creet.id creet.x creet.y));
    let game_area = Dom_html.document##getElementById (Js.string "game-area") in
    Js.Opt.case game_area
      (fun () ->
        Firebug.console##log (Js.string "Game area not found for creet");
        creet)
      (fun area ->
        Firebug.console##log (Js.string "Game area found, creating element...");
        match create_creet_element creet with
        | Some element ->
          Firebug.console##log (Js.string (Printf.sprintf "Element created for Creet %d, adding to game area" creet.id));
          let _ = area##appendChild (element :> Dom.node Js.t) in
          add_creet_mouse_events creet element;
          Firebug.console##log (Js.string (Printf.sprintf "Creet %d successfully added to DOM with mouse events" creet.id));
          { creet with element = Some element }
        | None ->
          Firebug.console##log (Js.string (Printf.sprintf "Failed to create element for Creet %d" creet.id));
          creet)
  with
  | exn ->
    Firebug.console##log (Js.string ("Error adding creet to DOM: " ^ (Printexc.to_string exn)));
    creet

(* Individual Lwt thread for each Creet *)
let rec creet_thread creet_id () =
  if !creet_thread_running then
    let%lwt () = Lwt_js.sleep 0.016 in (* ~60 FPS *)
    (match !current_game_state with
    | Some game_state when game_state.is_running ->
      (match List.find_opt (fun c -> c.id = creet_id) game_state.creets with
      | Some creet when creet.status <> Grabbed ->
        let dt = 0.016 in (* Fixed timestep for individual threads *)
        let updated_creet = update_creet_with_collisions creet game_state.creets dt game_state in

        (* Update the creet in the global game state *)
        let updated_creets = List.map (fun c ->
          if c.id = creet_id then updated_creet else c
        ) game_state.creets in

        let updated_game_state = { game_state with creets = updated_creets } in
        current_game_state := Some updated_game_state;

        (* Update DOM element *)
        update_creet_element updated_creet;

        creet_thread creet_id ()
      | _ ->
        creet_thread creet_id ())
    | _ ->
      creet_thread creet_id ())
  else
    Lwt.return ()

(* Start a Lwt thread for a specific Creet *)
let start_creet_thread creet =
  Firebug.console##log (Js.string (Printf.sprintf "Starting Lwt thread for Creet %d" creet.id));
  let thread = creet_thread creet.id () in
  creet_threads := thread :: !creet_threads;
  thread

(* Stop all Creet threads *)
let stop_all_creet_threads () =
  Firebug.console##log (Js.string "Stopping all Creet Lwt threads");
  creet_thread_running := false;
  creet_threads := []

(* Start all Creet threads *)
let start_all_creet_threads creets =
  Firebug.console##log (Js.string (Printf.sprintf "Starting Lwt threads for %d Creets" (List.length creets)));
  creet_thread_running := true;
  List.iter (fun creet ->
    Lwt.async (fun () -> start_creet_thread creet)
  ) creets

(* Reproduce creets if there are healthy creets and the reproduction interval is met *)
let reproduce_creets game_state =
  let healthy_creets = List.filter (fun c -> c.state = Healthy) game_state.creets in
  let time_since_last_reproduction = game_state.time_elapsed -. game_state.last_reproduction_time in
  if List.length healthy_creets > 0 && time_since_last_reproduction >= spawn_delay () then
    let new_creet_id = List.length game_state.creets + 1 in
    let (x, y) = get_random_spawn_position () in
    let new_creet = create_creet new_creet_id x y in
    Firebug.console##log (Js.string (Printf.sprintf "New creet reproduced at (%.1f, %.1f)" x y));

    (* Add the new creet to the DOM *)
    let new_creet_with_element = add_creet_to_dom new_creet in

    (* Update the game state with the new creet and reset the reproduction timer *)
    { game_state with
      creets = new_creet_with_element :: game_state.creets;
      last_reproduction_time = game_state.time_elapsed; (* Reset the timer *)
    }
  else
    game_state

(* Main game coordination loop - manages global game state and UI using Lwt *)
let rec game_coordination_loop last_time () =
  if !creet_thread_running then
    let%lwt () = Lwt_js.sleep 0.016 in (* ~60 FPS *)
    (match !current_game_state with
    | None -> Lwt.return ()
    | Some game_state when not game_state.is_running -> Lwt.return ()
    | Some game_state ->
      try%lwt
        let current_time = Js.to_float (new%js Js.date_now)##getTime in
        let dt = (current_time -. last_time) /. 1000.0 in

        (* Check game over condition *)
        let healthy_creets = List.filter (fun c -> c.state = Healthy) game_state.creets in
        let game_over = List.length healthy_creets = 0 in

        (* Update difficulty *)
        let new_difficulty = game_state.difficulty_multiplier +. (difficulty_increase_rate *. dt) in

        (* Adjust base speed based on difficulty *)
        let new_base_speed = !creet_base_speed +. (speed_increment new_difficulty *. dt) in

        (* Reproduce creets *)
        let game_state_with_reproduction = reproduce_creets game_state in

        (* Check if new creets were added and start their threads *)
        let old_creet_count = List.length game_state.creets in
        let new_creet_count = List.length game_state_with_reproduction.creets in
        if new_creet_count > old_creet_count then (
          let new_creets = List.filter (fun c ->
            not (List.exists (fun old_c -> old_c.id = c.id) game_state.creets)
          ) game_state_with_reproduction.creets in
          List.iter (fun creet ->
            Lwt.async (fun () -> start_creet_thread creet)
          ) new_creets
        );

        (* Update game state *)
        let new_game_state = {
          game_state_with_reproduction with
          time_elapsed = game_state.time_elapsed +. dt;
          difficulty_multiplier = new_difficulty;
          game_over = game_over;
          is_running = not game_over;
        } in

        (* Update the global base speed *)
        creet_base_speed := new_base_speed;

        current_game_state := Some new_game_state;

        (* Update footer info *)
        let score_element = Dom_html.document##getElementById (Js.string "score") in
        Js.Opt.iter score_element (fun el ->
          el##.innerHTML := Js.string (Printf.sprintf "Healthy Creets: %d" (List.length healthy_creets))
        );

        let time_element = Dom_html.document##getElementById (Js.string "time") in
        Js.Opt.iter time_element (fun el ->
          el##.innerHTML := Js.string (Printf.sprintf "Time: %.1fs" new_game_state.time_elapsed)
        );

        let difficulty_element = Dom_html.document##getElementById (Js.string "difficulty") in
        Js.Opt.iter difficulty_element (fun el ->
          el##.innerHTML := Js.string (Printf.sprintf "Difficulty: %.1fx" new_game_state.difficulty_multiplier)
        );

        if game_over then (
          Firebug.console##log (Js.string "Game Over! Stopping all Creet threads.");
          stop_all_creet_threads ();
          let game_over_msg = Dom_html.document##getElementById (Js.string "game-over") in
          Js.Opt.iter game_over_msg (fun msg ->
            msg##.style##.display := Js.string "block"
          );
          Lwt.return ()
        ) else (
          game_coordination_loop current_time ()
        )
      with
      | exn ->
        Firebug.console##log (Js.string ("Game coordination loop error: " ^ (Printexc.to_string exn)));
        Lwt.return ())
  else
    Lwt.return ()

(* Start game *)
let start_game () =
  try
    Firebug.console##log (Js.string "Starting H42N42 game...");

    (* Initialize game state *)
    let game_state = init_game_state () in
    Firebug.console##log (Js.string (Printf.sprintf "Game state initialized with %d Creets" (List.length game_state.creets)));

    (* Add all creets to DOM *)
    Firebug.console##log (Js.string "Adding Creets to DOM...");
    let creets_with_elements = List.map add_creet_to_dom game_state.creets in
    Firebug.console##log (Js.string (Printf.sprintf "Added %d Creets to DOM" (List.length creets_with_elements)));

    let updated_game_state = { game_state with creets = creets_with_elements; is_running = true } in

    current_game_state := Some updated_game_state;

    (* Hide entire game controls div, show game *)
    let game_controls = Dom_html.document##querySelector (Js.string ".game-controls") in
    Js.Opt.iter game_controls (fun controls -> controls##.style##.display := Js.string "none");

    let game_container = Dom_html.document##getElementById (Js.string "game-container") in
    Js.Opt.iter game_container (fun container -> container##.style##.display := Js.string "block");

    (* Start individual Lwt threads for each Creet *)
    Firebug.console##log (Js.string "Starting individual Lwt threads for each Creet...");
    start_all_creet_threads creets_with_elements;

    (* Start game coordination loop *)
    let start_time = Js.to_float (new%js Js.date_now)##getTime in
    Lwt.async (fun () -> game_coordination_loop start_time ());

    Firebug.console##log (Js.string "Game started successfully with Lwt threads!")
  with
  | exn ->
    Firebug.console##log (Js.string ("Error starting game: " ^ (Printexc.to_string exn)))

(* Stop game *)
let stop_game () =
  try
    Firebug.console##log (Js.string "Stopping game and all Lwt threads...");

    (* Stop all Creet threads *)
    stop_all_creet_threads ();

    (* Stop game loop (legacy support) *)
    (match !game_loop_id with
     | Some id -> Dom_html.window##clearTimeout id
     | None -> ());
    game_loop_id := None;

    (* Update game state *)
    (match !current_game_state with
     | Some game_state ->
       current_game_state := Some { game_state with is_running = false }
     | None -> ());

    (* Show game controls, hide game over *)
    let game_controls = Dom_html.document##querySelector (Js.string ".game-controls") in
    Js.Opt.iter game_controls (fun controls -> controls##.style##.display := Js.string "flex");

    let game_over_msg = Dom_html.document##getElementById (Js.string "game-over") in
    Js.Opt.iter game_over_msg (fun msg -> msg##.style##.display := Js.string "none");

    Firebug.console##log (Js.string "Game stopped")
  with
  | exn ->
    Firebug.console##log (Js.string ("Error stopping game: " ^ (Printexc.to_string exn)))

(* Restart button logic *)
let restart_game () =
  try
    Firebug.console##log (Js.string "Restarting game...");

    (* Stop the current game loop *)
    stop_game ();

    (* Clear the game area *)
    let game_area = Dom_html.document##getElementById (Js.string "game-area") in
    Js.Opt.iter game_area (fun area ->
      area##.innerHTML := Js.string ""
    );

    (* Hide the Game Over prompt *)
    let game_over_msg = Dom_html.document##getElementById (Js.string "game-over") in
    Js.Opt.iter game_over_msg (fun msg ->
      msg##.style##.display := Js.string "none"
    );

    (* Reset game constants to defaults from settings *)
    creet_base_speed := get_setting "game-speed" 50.0; (* Reset base speed *)
    let initial_difficulty = get_setting "difficulty-progression" 1.0 in

    (* Reinitialize the game state *)
    let game_state = init_game_state () in
    let updated_game_state = { game_state with difficulty_multiplier = initial_difficulty } in
    current_game_state := Some updated_game_state;

    (* Restart the game *)
    start_game ();

    Firebug.console##log (Js.string "Game restarted successfully with default settings!")
  with
  | exn ->
    Firebug.console##log (Js.string ("Error restarting game: " ^ (Printexc.to_string exn)))

(* Initialize game controls *)
let init_game_controls () =
  try
    Firebug.console##log (Js.string "Initializing game controls...");

    (* Start button *)
    let start_btn = Dom_html.document##getElementById (Js.string "start-game") in
    Js.Opt.iter start_btn (fun btn ->
      btn##.onclick := Dom_html.handler (fun _ ->
        start_game ();
        Js._false
      )
    );

    (* Restart button *)
    let restart_btn = Dom_html.document##getElementById (Js.string "restart-game") in
    Js.Opt.iter restart_btn (fun btn ->
      btn##.onclick := Dom_html.handler (fun _ ->
        restart_game ();
        Js._false
      )
    );

    (* "Play Again" button in the Game Over prompt *)
    let play_again_btn = Dom_html.document##getElementById (Js.string "restart-game-over") in
    Js.Opt.iter play_again_btn (fun btn ->
      btn##.onclick := Dom_html.handler (fun _ ->
        restart_game ();
        Js._false
      )
    );

    (* Global Lwt mouse event handlers for drag and drop *)
    Lwt.async (fun () ->
      let%lwt () =
        Lwt_js_events.mousemoves Dom_html.document (fun event _ ->
          let mouse_x = float_of_int event##.clientX in
          let mouse_y = float_of_int event##.clientY in
          update_drag mouse_x mouse_y;
          Lwt.return ()
        )
      in
      Lwt.return ()
    );

    Lwt.async (fun () ->
      let%lwt () =
        Lwt_js_events.mouseups Dom_html.document (fun _ _ ->
          stop_drag ();
          Lwt.return ()
        )
      in
      Lwt.return ()
    );

    Firebug.console##log (Js.string "Game controls and drag handlers initialized")
  with
  | exn ->
    Firebug.console##log (Js.string ("Error initializing game controls: " ^ (Printexc.to_string exn)))

(* Export functions for debugging *)
let () =
  Js.export "H42N42GameDebug"
    (object%js
       method getSettings =
         object%js
           val initialCreets = initial_creets_count ()
           val gameSpeed = game_speed ()
           val difficultyProgression = difficulty_progression ()
           val infectionRate = infection_rate ()
           val berserkChance = berserk_chance ()
           val meanChance = mean_chance ()
           val healTime = heal_time ()
           val autoHeal = auto_heal ()
         end
       method setupGameArea = setup_game_area
       method initGameState = init_game_state
       method startGame = start_game
       method stopGame = stop_game
       method getCurrentState = fun () -> !current_game_state
     end)

(* Initialize when DOM is ready *)
let () =
  Firebug.console##log (Js.string "H42N42 Game Logic loaded");

  let init_when_ready () =
    let ready_state = Js.to_string Dom_html.document##.readyState in
    if ready_state = "loading" then (
      let rec check_ready () =
        let current_state = Js.to_string Dom_html.document##.readyState in
        if current_state = "loading" then
          ignore (Dom_html.window##setTimeout (Js.wrap_callback check_ready) 10.0)
        else (
          Firebug.console##log (Js.string "Game DOM ready, initializing...");
          load_default_game_settings ();
          let _ = init_game_state () in
          let game_container = Dom_html.document##querySelector (Js.string "#game-container") in
          if Js.Opt.test game_container then
            init_game_controls ();
          ()
        )
      in
      check_ready ()
    ) else (
      Firebug.console##log (Js.string "Game DOM already loaded");
      load_default_game_settings ();
      let _ = init_game_state () in
      let game_container = Dom_html.document##querySelector (Js.string "#game-container") in
      if Js.Opt.test game_container then
        init_game_controls ();
      ()
    )
  in

  init_when_ready ()
