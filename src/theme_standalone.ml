(* Standalone OCaml theme system - compiles to separate JavaScript file *)
open Js_of_ocaml

(* Theme types *)
type theme = Default | Dark | Nature | Sunset | Ocean | Neon

let string_of_theme = function
  | Default -> "default"
  | Dark -> "dark"
  | Nature -> "nature"
  | Sunset -> "sunset"
  | Ocean -> "ocean"
  | Neon -> "neon"

let theme_of_string = function
  | "dark" -> Dark
  | "nature" -> Nature
  | "sunset" -> Sunset
  | "ocean" -> Ocean
  | "neon" -> Neon
  | _ -> Default

let all_themes = [Default; Dark; Nature; Sunset; Ocean; Neon]

(* Cookie utilities *)
let cookie_prefix = "h42n42_"
let cookie_expiry_days = 365

let set_cookie name value =
  try
    let expires = new%js Js.date_now in
    let time = expires##getTime +. (float_of_int cookie_expiry_days *. 24. *. 60. *. 60. *. 1000.) in
    let _ = expires##setTime time in
    let cookie_string = Printf.sprintf "%s%s=%s;expires=%s;path=/"
      cookie_prefix name value (Js.to_string expires##toUTCString) in
    Dom_html.document##.cookie := Js.string cookie_string;
    Firebug.console##log (Js.string ("Cookie set: " ^ cookie_prefix ^ name ^ " = " ^ value))
  with
  | exn -> Firebug.console##log (Js.string ("Error setting cookie: " ^ (Printexc.to_string exn)))

let get_cookie name =
  try
    let cookies = Js.to_string Dom_html.document##.cookie in
    let name_eq = cookie_prefix ^ name ^ "=" in
    let cookie_list = String.split_on_char ';' cookies in
    let rec find_cookie = function
      | [] -> None
      | cookie :: rest ->
        let trimmed = String.trim cookie in
        if String.length trimmed >= String.length name_eq then
          let prefix = String.sub trimmed 0 (String.length name_eq) in
          if prefix = name_eq then
            let value = String.sub trimmed (String.length name_eq) 
              (String.length trimmed - String.length name_eq) in
            Some value
          else
            find_cookie rest
        else
          find_cookie rest
    in
    find_cookie cookie_list
  with
  | exn -> 
    Firebug.console##log (Js.string ("Error getting cookie: " ^ (Printexc.to_string exn)));
    None

(* Theme management *)
let apply_theme theme =
  try
    let theme_str = string_of_theme theme in
    Firebug.console##log (Js.string ("Applying theme: " ^ theme_str));
    
    (* Remove all existing theme classes *)
    List.iter (fun t ->
      let class_name = (string_of_theme t) ^ "-theme" in
      Dom_html.document##.body##.classList##remove (Js.string class_name)
    ) all_themes;
    
    (* Add new theme class (except for default) *)
    if theme <> Default then (
      let class_name = theme_str ^ "-theme" in
      Dom_html.document##.body##.classList##add (Js.string class_name)
    );
    
    (* Save to cookie *)
    set_cookie "theme" theme_str;
    
    (* Update active theme card *)
    List.iter (fun t ->
      let card_id = "theme-" ^ (string_of_theme t) in
      let card = Dom_html.document##getElementById (Js.string card_id) in
      Js.Opt.iter card (fun c ->
        c##.classList##remove (Js.string "active")
      )
    ) all_themes;
    
    let active_card_id = "theme-" ^ theme_str in
    let active_card = Dom_html.document##getElementById (Js.string active_card_id) in
    Js.Opt.iter active_card (fun c ->
      c##.classList##add (Js.string "active")
    );
    
    Firebug.console##log (Js.string ("Theme applied successfully: " ^ theme_str))
  with
  | exn -> Firebug.console##log (Js.string ("Error applying theme: " ^ (Printexc.to_string exn)))

let load_theme () =
  try
    let saved_theme = match get_cookie "theme" with
      | Some theme_str -> theme_of_string theme_str
      | None -> Default
    in
    Firebug.console##log (Js.string ("Loading saved theme: " ^ (string_of_theme saved_theme)));
    apply_theme saved_theme
  with
  | exn -> Firebug.console##log (Js.string ("Error loading theme: " ^ (Printexc.to_string exn)))

(* Customization management *)
let apply_customizations () =
  try
    let font_size = match get_cookie "font_size" with
      | Some s -> s ^ "px"
      | None -> "16px"
    in
    let animation_speed = match get_cookie "animation_speed" with
      | Some s -> s
      | None -> "1.0"
    in
    let reduce_motion = match get_cookie "reduce_motion" with
      | Some "true" -> true
      | _ -> false
    in
    let high_contrast = match get_cookie "high_contrast" with
      | Some "true" -> true
      | _ -> false
    in
    
    Firebug.console##log (Js.string (Printf.sprintf 
      "Applying customizations: font_size=%s, animation_speed=%s, reduce_motion=%b, high_contrast=%b"
      font_size animation_speed reduce_motion high_contrast));
    
    (* Apply font size *)
    let _ = Dom_html.document##.documentElement##.style##setProperty
      (Js.string "--base-font-size") (Js.string font_size) (Js.undefined) in

    (* Apply animation speed *)
    let _ = Dom_html.document##.documentElement##.style##setProperty
      (Js.string "--animation-speed") (Js.string animation_speed) (Js.undefined) in
    
    (* Apply reduce motion *)
    if reduce_motion then
      Dom_html.document##.body##.classList##add (Js.string "reduce-motion")
    else
      Dom_html.document##.body##.classList##remove (Js.string "reduce-motion");
    
    (* Apply high contrast *)
    if high_contrast then
      Dom_html.document##.body##.classList##add (Js.string "high-contrast")
    else
      Dom_html.document##.body##.classList##remove (Js.string "high-contrast")
  with
  | exn -> Firebug.console##log (Js.string ("Error applying customizations: " ^ (Printexc.to_string exn)))

(* Theme controls initialization *)
let init_theme_controls () =
  try
    Firebug.console##log (Js.string "Initializing theme controls...");
    
    (* Theme buttons *)
    List.iter (fun theme ->
      let theme_str = string_of_theme theme in
      let button_id = "apply-" ^ theme_str in
      let button = Dom_html.document##getElementById (Js.string button_id) in
      Js.Opt.iter button (fun btn ->
        Firebug.console##log (Js.string ("Found theme button: " ^ button_id));
        btn##.onclick := Dom_html.handler (fun _ ->
          Firebug.console##log (Js.string ("Theme button clicked: " ^ theme_str));
          apply_theme theme;
          Js._false
        )
      )
    ) all_themes;
    
    (* Reset button *)
    let reset_btn = Dom_html.document##getElementById (Js.string "reset-theme") in
    Js.Opt.iter reset_btn (fun btn ->
      Firebug.console##log (Js.string "Found reset button");
      btn##.onclick := Dom_html.handler (fun _ ->
        Firebug.console##log (Js.string "Reset button clicked");
        apply_theme Default;
        Js._false
      )
    );
    
    Firebug.console##log (Js.string "Theme controls initialized")
  with
  | exn -> Firebug.console##log (Js.string ("Error initializing theme controls: " ^ (Printexc.to_string exn)))

(* Main initialization *)
let init_theme_system () =
  try
    Firebug.console##log (Js.string "H42N42 Standalone Theme System initializing...");
    
    (* Always load theme and customizations on every page *)
    load_theme ();
    apply_customizations ();
    
    (* Initialize controls based on page type *)
    let theme_container = Dom_html.document##querySelector (Js.string ".theme-content") in
    if Js.Opt.test theme_container then (
      Firebug.console##log (Js.string "Theme page detected, initializing theme controls...");
      init_theme_controls ()
    ) else (
      Firebug.console##log (Js.string "No theme page detected")
    );
    
    Firebug.console##log (Js.string "H42N42 Standalone Theme System initialized successfully")
  with
  | exn -> Firebug.console##log (Js.string ("Error initializing theme system: " ^ (Printexc.to_string exn)))

(* Export functions to global scope for debugging *)
let () =
  Js.export "H42N42ThemeDebug" 
    (object%js
       method loadTheme = load_theme
       method applyTheme theme_str = apply_theme (theme_of_string theme_str)
       method applyCustomizations = apply_customizations
       method init = init_theme_system
       method getCookie name = 
         match get_cookie name with 
         | Some v -> Js.some (Js.string v) 
         | None -> Js.null
       method setCookie name value = set_cookie name value
     end)

(* Initialize when DOM is ready *)
let () =
  Firebug.console##log (Js.string "Theme standalone script loaded");
  
  let init_when_ready () =
    let ready_state = Js.to_string Dom_html.document##.readyState in
    if ready_state = "loading" then (
      Firebug.console##log (Js.string "DOM still loading, waiting...");
      (* Use a simple timer to check when DOM is ready *)
      let rec check_ready () =
        let current_state = Js.to_string Dom_html.document##.readyState in
        if current_state = "loading" then
          ignore (Dom_html.window##setTimeout (Js.wrap_callback check_ready) 10.0)
        else (
          Firebug.console##log (Js.string "DOM ready detected");
          init_theme_system ()
        )
      in
      check_ready ()
    ) else (
      Firebug.console##log (Js.string "DOM already loaded, initializing immediately");
      init_theme_system ()
    )
  in
  
  init_when_ready ()
